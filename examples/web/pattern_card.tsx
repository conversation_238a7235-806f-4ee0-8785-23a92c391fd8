/**
 * Example: Pattern Card Component for CCL Web Frontend
 * Demonstrates React component patterns with TypeScript, hooks, and styling
 */

import React, { useState, useCallback, memo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Card, 
  CardHeader, 
  CardContent, 
  CardFooter 
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  StarIcon, 
  CodeBracketIcon, 
  ShieldCheckIcon,
  ArrowDownTrayIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';

import { Pattern, PatternPurchase } from '@/types/pattern';
import { useAuth } from '@/hooks/useAuth';
import { usePattern } from '@/hooks/usePattern';
import { patternApi } from '@/services/api/pattern';
import { cn } from '@/lib/utils';
import { formatCurrency } from '@/lib/format';

interface PatternCardProps {
  pattern: Pattern;
  onPurchase?: (pattern: Pattern) => void;
  onPreview?: (pattern: Pattern) => void;
  variant?: 'default' | 'compact' | 'featured';
  className?: string;
}

/**
 * PatternCard displays a pattern with purchase/download capabilities
 * Memoized to prevent unnecessary re-renders in lists
 */
export const PatternCard = memo<PatternCardProps>(({ 
  pattern, 
  onPurchase,
  onPreview,
  variant = 'default',
  className 
}) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { isPurchased, isLoading: isLoadingPurchase } = usePattern(pattern.id);
  const [isExpanded, setIsExpanded] = useState(false);
  const [rating, setRating] = useState(pattern.userRating || 0);

  // Purchase mutation with optimistic updates
  const purchaseMutation = useMutation<PatternPurchase, Error, string>({
    mutationFn: (patternId: string) => patternApi.purchasePattern(patternId),
    onMutate: async (patternId) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['pattern', patternId] });
      
      // Snapshot previous value
      const previousPattern = queryClient.getQueryData(['pattern', patternId]);
      
      // Optimistically update
      queryClient.setQueryData(['pattern', patternId], (old: any) => ({
        ...old,
        purchased: true,
        purchaseCount: (old?.purchaseCount || 0) + 1
      }));
      
      return { previousPattern };
    },
    onError: (err, patternId, context) => {
      // Rollback on error
      if (context?.previousPattern) {
        queryClient.setQueryData(['pattern', patternId], context.previousPattern);
      }
      toast.error('Purchase failed', {
        description: err.message || 'Please try again later'
      });
    },
    onSuccess: (purchase) => {
      toast.success('Pattern purchased!', {
        description: 'You can now download and use this pattern',
        action: {
          label: 'Download',
          onClick: () => handleDownload()
        }
      });
      
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: ['pattern', pattern.id] });
      queryClient.invalidateQueries({ queryKey: ['user', 'purchases'] });
      
      // Call parent callback
      onPurchase?.(pattern);
    }
  });

  // Rating mutation
  const rateMutation = useMutation({
    mutationFn: ({ patternId, rating }: { patternId: string; rating: number }) =>
      patternApi.ratePattern(patternId, rating),
    onSuccess: () => {
      toast.success('Thanks for rating!');
      queryClient.invalidateQueries({ queryKey: ['pattern', pattern.id] });
    }
  });

  const handlePurchase = useCallback(async () => {
    if (!user) {
      toast.error('Please sign in to purchase patterns');
      return;
    }

    purchaseMutation.mutate(pattern.id);
  }, [user, pattern.id, purchaseMutation]);

  const handleDownload = useCallback(async () => {
    try {
      const blob = await patternApi.downloadPattern(pattern.id);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${pattern.slug}.zip`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('Pattern downloaded successfully');
    } catch (error) {
      toast.error('Download failed', {
        description: 'Please try again later'
      });
    }
  }, [pattern.id, pattern.slug]);

  const handleRating = useCallback((newRating: number) => {
    if (!user) {
      toast.error('Please sign in to rate patterns');
      return;
    }
    
    setRating(newRating);
    rateMutation.mutate({ patternId: pattern.id, rating: newRating });
  }, [user, pattern.id, rateMutation]);

  const renderStars = () => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => handleRating(star)}
            disabled={!user || rateMutation.isPending}
            className={cn(
              "transition-colors",
              user && "hover:text-yellow-500",
              !user && "cursor-not-allowed opacity-50"
            )}
          >
            {star <= (rating || pattern.rating) ? (
              <StarIconSolid className="h-4 w-4 text-yellow-500" />
            ) : (
              <StarIcon className="h-4 w-4" />
            )}
          </button>
        ))}
        <span className="ml-2 text-sm text-muted-foreground">
          ({pattern.ratingCount})
        </span>
      </div>
    );
  };

  if (variant === 'compact') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={cn("group cursor-pointer", className)}
        onClick={() => onPreview?.(pattern)}
      >
        <div className="flex items-center justify-between p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
          <div className="flex items-center gap-3">
            <CodeBracketIcon className="h-5 w-5 text-muted-foreground" />
            <div>
              <h4 className="font-medium">{pattern.name}</h4>
              <p className="text-sm text-muted-foreground">
                by {pattern.author.name}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {pattern.verified && (
              <ShieldCheckIcon className="h-4 w-4 text-green-500" />
            )}
            <Badge variant="secondary">{formatCurrency(pattern.price)}</Badge>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
    >
      <Card className={cn(
        "overflow-hidden transition-shadow hover:shadow-lg",
        variant === 'featured' && "border-primary",
        className
      )}>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <h3 className="text-lg font-semibold leading-none">
                {pattern.name}
              </h3>
              <p className="text-sm text-muted-foreground">
                by {pattern.author.name}
              </p>
            </div>
            <div className="flex items-center gap-2">
              {pattern.verified && (
                <Badge variant="default" className="gap-1">
                  <ShieldCheckIcon className="h-3 w-3" />
                  Verified
                </Badge>
              )}
              {pattern.trending && (
                <Badge variant="secondary" className="gap-1">
                  <SparklesIcon className="h-3 w-3" />
                  Trending
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <p className={cn(
            "text-sm text-muted-foreground",
            !isExpanded && "line-clamp-2"
          )}>
            {pattern.description}
          </p>
          
          {pattern.description.length > 100 && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-primary hover:underline"
            >
              {isExpanded ? 'Show less' : 'Show more'}
            </button>
          )}

          <div className="flex flex-wrap gap-2">
            {pattern.tags.map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Category</span>
              <Badge variant="secondary">{pattern.category}</Badge>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Used by</span>
              <span className="font-medium">{pattern.usageCount} projects</span>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Updated</span>
              <span className="font-medium">
                {formatDistanceToNow(new Date(pattern.updatedAt), { 
                  addSuffix: true 
                })}
              </span>
            </div>
          </div>

          <div className="pt-2">
            {renderStars()}
          </div>

          {pattern.preview && (
            <div className="rounded-md bg-muted p-3">
              <pre className="text-xs overflow-x-auto">
                <code>{pattern.preview}</code>
              </pre>
            </div>
          )}
        </CardContent>

        <CardFooter className="gap-2">
          {isPurchased ? (
            <>
              <Button
                onClick={handleDownload}
                className="flex-1"
                variant="default"
              >
                <ArrowDownTrayIcon className="mr-2 h-4 w-4" />
                Download
              </Button>
              <Button
                onClick={() => onPreview?.(pattern)}
                variant="outline"
              >
                View Details
              </Button>
            </>
          ) : (
            <>
              <Button
                onClick={handlePurchase}
                disabled={purchaseMutation.isPending || isLoadingPurchase}
                className="flex-1"
                variant="default"
              >
                {purchaseMutation.isPending ? (
                  'Processing...'
                ) : (
                  <>Purchase for {formatCurrency(pattern.price)}</>
                )}
              </Button>
              <Button
                onClick={() => onPreview?.(pattern)}
                variant="outline"
              >
                Preview
              </Button>
            </>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
});

PatternCard.displayName = 'PatternCard';

// Example usage with loading skeleton
export const PatternCardSkeleton: React.FC = () => {
  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <div className="space-y-2">
          <div className="h-5 w-3/4 bg-muted rounded animate-pulse" />
          <div className="h-4 w-1/2 bg-muted rounded animate-pulse" />
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="h-4 w-full bg-muted rounded animate-pulse" />
          <div className="h-4 w-5/6 bg-muted rounded animate-pulse" />
        </div>
        <div className="flex gap-2">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-6 w-16 bg-muted rounded-full animate-pulse" />
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <div className="h-10 w-full bg-muted rounded animate-pulse" />
      </CardFooter>
    </Card>
  );
};