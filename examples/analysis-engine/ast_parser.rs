// Example: AST Parser Implementation for CCL Analysis Engine
// This demonstrates the pattern for parsing different languages

use std::path::Path;
use anyhow::{Result, Context};
use tree_sitter::{Parser, Tree, Node};
use rayon::prelude::*;

/// Represents a parsed AST with metadata
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ParsedAst {
    pub file_path: String,
    pub language: Language,
    pub tree: Tree,
    pub content: String,
    pub metrics: CodeMetrics,
}

/// Supported programming languages
#[derive(Debug, Clone, PartialEq)]
pub enum Language {
    Rust,
    Python,
    Go,
    TypeScript,
    JavaScript,
    Java,
    // Add more as needed
}

/// Code metrics extracted during parsing
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct CodeMetrics {
    pub lines_of_code: usize,
    pub cyclomatic_complexity: usize,
    pub function_count: usize,
    pub class_count: usize,
}

/// Main AST parser for the analysis engine
pub struct AstParser {
    parsers: HashMap<Language, Parser>,
}

impl AstParser {
    /// Initialize parser with all supported languages
    pub fn new() -> Result<Self> {
        let mut parsers = HashMap::new();
        
        // Initialize language-specific parsers
        parsers.insert(Language::Rust, Self::init_rust_parser()?);
        parsers.insert(Language::Python, Self::init_python_parser()?);
        parsers.insert(Language::Go, Self::init_go_parser()?);
        parsers.insert(Language::TypeScript, Self::init_typescript_parser()?);
        
        Ok(Self { parsers })
    }
    
    /// Parse a single file
    pub async fn parse_file(&self, path: &Path) -> Result<ParsedAst> {
        // Read file content
        let content = tokio::fs::read_to_string(path)
            .await
            .context("Failed to read file")?;
        
        // Detect language from extension
        let language = Self::detect_language(path)?;
        
        // Get appropriate parser
        let parser = self.parsers.get(&language)
            .ok_or_else(|| anyhow::anyhow!("Unsupported language: {:?}", language))?;
        
        // Parse the content
        let tree = parser.parse(&content, None)
            .ok_or_else(|| anyhow::anyhow!("Failed to parse file"))?;
        
        // Extract metrics
        let metrics = self.extract_metrics(&tree, &content, language);
        
        Ok(ParsedAst {
            file_path: path.to_string_lossy().to_string(),
            language,
            tree,
            content,
            metrics,
        })
    }
    
    /// Parse multiple files in parallel
    pub async fn parse_directory(&self, dir: &Path) -> Result<Vec<ParsedAst>> {
        // Collect all source files
        let files = self.collect_source_files(dir).await?;
        
        // Parse files in parallel using rayon
        let results: Vec<Result<ParsedAst>> = files
            .par_iter()
            .map(|path| {
                // Create a runtime for async operations in parallel iterator
                let rt = tokio::runtime::Handle::current();
                rt.block_on(self.parse_file(path))
            })
            .collect();
        
        // Collect successful parses and log errors
        let mut parsed_files = Vec::new();
        for result in results {
            match result {
                Ok(ast) => parsed_files.push(ast),
                Err(e) => log::warn!("Failed to parse file: {}", e),
            }
        }
        
        Ok(parsed_files)
    }
    
    /// Extract code metrics from AST
    fn extract_metrics(&self, tree: &Tree, content: &str, language: Language) -> CodeMetrics {
        let mut metrics = CodeMetrics::default();
        let root = tree.root_node();
        
        // Count lines of code (non-empty, non-comment)
        metrics.lines_of_code = content.lines()
            .filter(|line| !line.trim().is_empty())
            .count();
        
        // Walk the tree to extract metrics
        self.walk_node(&root, &mut metrics, language);
        
        metrics
    }
    
    /// Recursively walk AST nodes
    fn walk_node(&self, node: &Node, metrics: &mut CodeMetrics, language: Language) {
        match language {
            Language::Rust => self.analyze_rust_node(node, metrics),
            Language::Python => self.analyze_python_node(node, metrics),
            Language::Go => self.analyze_go_node(node, metrics),
            Language::TypeScript => self.analyze_typescript_node(node, metrics),
            _ => {} // Add more language-specific analysis
        }
        
        // Recurse through children
        for i in 0..node.child_count() {
            if let Some(child) = node.child(i) {
                self.walk_node(&child, metrics, language);
            }
        }
    }
    
    /// Language-specific node analysis for Rust
    fn analyze_rust_node(&self, node: &Node, metrics: &mut CodeMetrics) {
        match node.kind() {
            "function_item" | "async_function_item" => {
                metrics.function_count += 1;
                metrics.cyclomatic_complexity += self.calculate_rust_complexity(node);
            }
            "struct_item" | "enum_item" | "trait_item" => {
                metrics.class_count += 1;
            }
            _ => {}
        }
    }
    
    /// Calculate cyclomatic complexity for Rust functions
    fn calculate_rust_complexity(&self, func_node: &Node) -> usize {
        let mut complexity = 1; // Base complexity
        
        // Count decision points
        self.count_decision_points(func_node, &mut complexity, &[
            "if_expression",
            "match_expression",
            "while_expression",
            "for_expression",
            "loop_expression",
            "&&",
            "||",
        ]);
        
        complexity
    }
    
    /// Helper to count decision points in AST
    fn count_decision_points(&self, node: &Node, complexity: &mut usize, decision_types: &[&str]) {
        if decision_types.contains(&node.kind()) {
            *complexity += 1;
        }
        
        for i in 0..node.child_count() {
            if let Some(child) = node.child(i) {
                self.count_decision_points(&child, complexity, decision_types);
            }
        }
    }
    
    /// Detect language from file extension
    fn detect_language(path: &Path) -> Result<Language> {
        let ext = path.extension()
            .and_then(|e| e.to_str())
            .ok_or_else(|| anyhow::anyhow!("No file extension"))?;
        
        match ext {
            "rs" => Ok(Language::Rust),
            "py" => Ok(Language::Python),
            "go" => Ok(Language::Go),
            "ts" | "tsx" => Ok(Language::TypeScript),
            "js" | "jsx" => Ok(Language::JavaScript),
            "java" => Ok(Language::Java),
            _ => Err(anyhow::anyhow!("Unsupported file extension: {}", ext)),
        }
    }
    
    // Language-specific parser initialization methods
    fn init_rust_parser() -> Result<Parser> {
        let mut parser = Parser::new();
        parser.set_language(tree_sitter_rust::language())
            .context("Failed to set Rust language")?;
        Ok(parser)
    }
    
    fn init_python_parser() -> Result<Parser> {
        let mut parser = Parser::new();
        parser.set_language(tree_sitter_python::language())
            .context("Failed to set Python language")?;
        Ok(parser)
    }
    
    // ... similar for other languages
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use tokio::fs;
    
    #[tokio::test]
    async fn test_parse_rust_file() {
        // Create a temporary file with Rust code
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("test.rs");
        
        let rust_code = r#"
fn main() {
    println!("Hello, world!");
}

fn calculate(x: i32, y: i32) -> i32 {
    if x > y {
        x - y
    } else {
        y - x
    }
}
"#;
        
        fs::write(&file_path, rust_code).await.unwrap();
        
        // Parse the file
        let parser = AstParser::new().unwrap();
        let result = parser.parse_file(&file_path).await.unwrap();
        
        // Verify results
        assert_eq!(result.language, Language::Rust);
        assert_eq!(result.metrics.function_count, 2);
        assert!(result.metrics.lines_of_code > 0);
        assert_eq!(result.metrics.cyclomatic_complexity, 2); // if-else adds 1
    }
    
    #[tokio::test]
    async fn test_parse_directory() {
        // Create a temporary directory with multiple files
        let temp_dir = TempDir::new().unwrap();
        
        // Create Rust file
        let rust_file = temp_dir.path().join("main.rs");
        fs::write(&rust_file, "fn main() {}").await.unwrap();
        
        // Create Python file
        let python_file = temp_dir.path().join("script.py");
        fs::write(&python_file, "def hello(): pass").await.unwrap();
        
        // Parse directory
        let parser = AstParser::new().unwrap();
        let results = parser.parse_directory(temp_dir.path()).await.unwrap();
        
        // Should parse both files
        assert_eq!(results.len(), 2);
        
        // Verify languages
        let languages: Vec<Language> = results.iter()
            .map(|ast| ast.language.clone())
            .collect();
        assert!(languages.contains(&Language::Rust));
        assert!(languages.contains(&Language::Python));
    }
}