// Package handlers provides HTTP handlers for the CCL Marketplace API
// This example demonstrates RESTful API patterns with proper error handling,
// validation, and middleware integration.
package handlers

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"

	"github.com/ccl/marketplace/internal/models"
	"github.com/ccl/marketplace/internal/services"
	"github.com/ccl/marketplace/pkg/errors"
	"github.com/ccl/marketplace/pkg/validation"
)

var tracer = otel.Tracer("marketplace-api")

// PatternHandler handles pattern-related HTTP requests
type PatternHandler struct {
	patternService *services.PatternService
	validator      *validation.Validator
}

// NewPatternHandler creates a new pattern handler
func NewPatternHandler(patternService *services.PatternService) *PatternHandler {
	return &PatternHandler{
		patternService: patternService,
		validator:      validation.NewValidator(),
	}
}

// Routes registers all pattern routes
func (h *PatternHandler) Routes() chi.Router {
	r := chi.NewRouter()

	// Middleware
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	r.Use(middleware.Timeout(60 * time.Second))

	// Routes
	r.Get("/", h.ListPatterns)
	r.Post("/", h.CreatePattern)
	r.Route("/{patternID}", func(r chi.Router) {
		r.Use(h.PatternCtx) // Load pattern into context
		r.Get("/", h.GetPattern)
		r.Put("/", h.UpdatePattern)
		r.Delete("/", h.DeletePattern)
		r.Post("/purchase", h.PurchasePattern)
		r.Get("/download", h.DownloadPattern)
	})

	return r
}

// ListPatterns handles GET /patterns
func (h *PatternHandler) ListPatterns(w http.ResponseWriter, r *http.Request) {
	ctx, span := tracer.Start(r.Context(), "ListPatterns")
	defer span.End()

	// Parse query parameters
	params := &models.ListPatternsParams{
		Page:     getIntParam(r, "page", 1),
		PageSize: getIntParam(r, "page_size", 20),
		Sort:     r.URL.Query().Get("sort"),
		Filter:   r.URL.Query().Get("filter"),
		Category: r.URL.Query().Get("category"),
	}

	// Validate parameters
	if err := h.validator.Struct(params); err != nil {
		respondError(w, errors.ErrInvalidInput.WithDetails(err.Error()), http.StatusBadRequest)
		return
	}

	// Get patterns from service
	patterns, total, err := h.patternService.ListPatterns(ctx, params)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list patterns")
		respondError(w, errors.ErrInternal, http.StatusInternalServerError)
		return
	}

	// Build response
	response := &models.ListPatternsResponse{
		Patterns: patterns,
		Pagination: models.Pagination{
			Page:       params.Page,
			PageSize:   params.PageSize,
			Total:      total,
			TotalPages: (total + params.PageSize - 1) / params.PageSize,
		},
	}

	respondJSON(w, response, http.StatusOK)
}

// CreatePattern handles POST /patterns
func (h *PatternHandler) CreatePattern(w http.ResponseWriter, r *http.Request) {
	ctx, span := tracer.Start(r.Context(), "CreatePattern")
	defer span.End()

	// Get user from context (set by auth middleware)
	user, ok := getUserFromContext(ctx)
	if !ok {
		respondError(w, errors.ErrUnauthorized, http.StatusUnauthorized)
		return
	}

	// Parse request body
	var req models.CreatePatternRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		respondError(w, errors.ErrInvalidInput.WithDetails("Invalid JSON"), http.StatusBadRequest)
		return
	}

	// Validate request
	if err := h.validator.Struct(req); err != nil {
		respondError(w, errors.ErrInvalidInput.WithDetails(err.Error()), http.StatusBadRequest)
		return
	}

	// Create pattern
	pattern, err := h.patternService.CreatePattern(ctx, user.ID, &req)
	if err != nil {
		switch {
		case errors.Is(err, errors.ErrDuplicatePattern):
			respondError(w, err, http.StatusConflict)
		case errors.Is(err, errors.ErrQuotaExceeded):
			respondError(w, err, http.StatusTooManyRequests)
		default:
			log.Error().Err(err).Msg("Failed to create pattern")
			respondError(w, errors.ErrInternal, http.StatusInternalServerError)
		}
		return
	}

	// Log successful creation
	log.Info().
		Str("pattern_id", pattern.ID).
		Str("user_id", user.ID).
		Msg("Pattern created successfully")

	respondJSON(w, pattern, http.StatusCreated)
}

// GetPattern handles GET /patterns/{patternID}
func (h *PatternHandler) GetPattern(w http.ResponseWriter, r *http.Request) {
	ctx, span := tracer.Start(r.Context(), "GetPattern")
	defer span.End()

	// Get pattern from context (loaded by middleware)
	pattern, ok := getPatternFromContext(ctx)
	if !ok {
		respondError(w, errors.ErrPatternNotFound, http.StatusNotFound)
		return
	}

	// Check if user has access
	user, _ := getUserFromContext(ctx)
	canAccess, err := h.patternService.CanAccessPattern(ctx, user, pattern.ID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to check pattern access")
		respondError(w, errors.ErrInternal, http.StatusInternalServerError)
		return
	}

	if !canAccess {
		respondError(w, errors.ErrForbidden, http.StatusForbidden)
		return
	}

	// Add view tracking
	go h.patternService.TrackView(context.Background(), pattern.ID, user.ID)

	respondJSON(w, pattern, http.StatusOK)
}

// PurchasePattern handles POST /patterns/{patternID}/purchase
func (h *PatternHandler) PurchasePattern(w http.ResponseWriter, r *http.Request) {
	ctx, span := tracer.Start(r.Context(), "PurchasePattern")
	defer span.End()

	// Get authenticated user
	user, ok := getUserFromContext(ctx)
	if !ok {
		respondError(w, errors.ErrUnauthorized, http.StatusUnauthorized)
		return
	}

	// Get pattern from context
	pattern, ok := getPatternFromContext(ctx)
	if !ok {
		respondError(w, errors.ErrPatternNotFound, http.StatusNotFound)
		return
	}

	// Parse purchase request
	var req models.PurchaseRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		respondError(w, errors.ErrInvalidInput, http.StatusBadRequest)
		return
	}

	// Process purchase
	purchase, err := h.patternService.PurchasePattern(ctx, user.ID, pattern.ID, &req)
	if err != nil {
		switch {
		case errors.Is(err, errors.ErrAlreadyPurchased):
			respondError(w, err, http.StatusConflict)
		case errors.Is(err, errors.ErrInsufficientFunds):
			respondError(w, err, http.StatusPaymentRequired)
		case errors.Is(err, errors.ErrPaymentFailed):
			respondError(w, err, http.StatusBadRequest)
		default:
			log.Error().
				Err(err).
				Str("user_id", user.ID).
				Str("pattern_id", pattern.ID).
				Msg("Purchase failed")
			respondError(w, errors.ErrInternal, http.StatusInternalServerError)
		}
		return
	}

	// Log successful purchase
	log.Info().
		Str("purchase_id", purchase.ID).
		Str("user_id", user.ID).
		Str("pattern_id", pattern.ID).
		Float64("amount", purchase.Amount).
		Msg("Pattern purchased successfully")

	respondJSON(w, purchase, http.StatusCreated)
}

// PatternCtx middleware loads pattern into context
func (h *PatternHandler) PatternCtx(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		patternID := chi.URLParam(r, "patternID")
		
		// Validate UUID format
		if _, err := uuid.Parse(patternID); err != nil {
			respondError(w, errors.ErrInvalidInput.WithDetails("Invalid pattern ID format"), http.StatusBadRequest)
			return
		}

		// Get pattern from service
		pattern, err := h.patternService.GetPattern(r.Context(), patternID)
		if err != nil {
			if errors.Is(err, errors.ErrPatternNotFound) {
				respondError(w, err, http.StatusNotFound)
			} else {
				log.Error().Err(err).Str("pattern_id", patternID).Msg("Failed to load pattern")
				respondError(w, errors.ErrInternal, http.StatusInternalServerError)
			}
			return
		}

		// Add to context
		ctx := context.WithValue(r.Context(), patternContextKey, pattern)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// Helper functions

type contextKey string

const (
	userContextKey    contextKey = "user"
	patternContextKey contextKey = "pattern"
)

func getUserFromContext(ctx context.Context) (*models.User, bool) {
	user, ok := ctx.Value(userContextKey).(*models.User)
	return user, ok
}

func getPatternFromContext(ctx context.Context) (*models.Pattern, bool) {
	pattern, ok := ctx.Value(patternContextKey).(*models.Pattern)
	return pattern, ok
}

func getIntParam(r *http.Request, key string, defaultValue int) int {
	// Implementation to parse int from query params
	// Returns defaultValue if not present or invalid
	return defaultValue
}

func respondJSON(w http.ResponseWriter, data interface{}, status int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	if err := json.NewEncoder(w).Encode(data); err != nil {
		log.Error().Err(err).Msg("Failed to encode response")
	}
}

func respondError(w http.ResponseWriter, err error, status int) {
	var apiErr *errors.APIError
	if errors.As(err, &apiErr) {
		respondJSON(w, apiErr, status)
	} else {
		respondJSON(w, errors.NewAPIError(err.Error(), "INTERNAL_ERROR"), status)
	}
}

// Example test
func ExamplePatternHandler_CreatePattern() {
	// Create handler with mock service
	handler := NewPatternHandler(mockPatternService)
	
	// Create test request
	req := httptest.NewRequest("POST", "/patterns", strings.NewReader(`{
		"name": "Repository Pattern",
		"description": "Implementation of repository pattern for Go",
		"category": "architecture",
		"price": 9.99,
		"tags": ["go", "patterns", "architecture"]
	}`))
	
	// Add auth header
	req.Header.Set("Authorization", "Bearer test-token")
	
	// Create response recorder
	w := httptest.NewRecorder()
	
	// Handle request
	handler.CreatePattern(w, req)
	
	// Check response
	if w.Code != http.StatusCreated {
		fmt.Printf("Expected status 201, got %d", w.Code)
	}
}