{"last_update": "2025-07-06T17:17:04.064782", "documents": {"PLANNING.md": {"modified": 1751820641.0428483, "size": 13022}, "INITIAL.md": {"modified": 1751820597.3526177, "size": 8842}, "README.md": {"modified": 1751820701.3219981, "size": 9895}, "TASK.md": {"modified": 1751820776.4454882, "size": 8899}, "CLAUDE.md": {"modified": 1751820511.531147, "size": 15934}, "PRPs/architecture-patterns.md": {"modified": 1751818973.9388044, "size": 26756}, "PRPs/feature-specifications.md": {"modified": 1751797043.0023954, "size": 25585}, "PRPs/implementation-guide.md": {"modified": 1751820091.8019388, "size": 42903}, "PRPs/README.md": {"modified": 1751799165.3206782, "size": 5654}, "PRPs/database/bigquery-analytics.md": {"modified": 1751820218.6639495, "size": 30662}, "PRPs/database/firestore-collections.md": {"modified": 1751816544.7533042, "size": 23243}, "PRPs/database/spanner-schema.md": {"modified": 1751818554.96613, "size": 52960}, "PRPs/security/authentication.md": {"modified": 1751819302.8185537, "size": 58794}, "PRPs/security/authorization.md": {"modified": 1751816733.2568762, "size": 33914}, "PRPs/security/zero-trust-architecture.md": {"modified": 1751819167.6920228, "size": 33572}, "PRPs/security/incident-response.md": {"modified": 1751816421.7119331, "size": 3259}, "PRPs/security/encryption.md": {"modified": 1751806233.7863553, "size": 30434}, "PRPs/security/compliance.md": {"modified": 1751818720.63626, "size": 82210}, "PRPs/development/dev-environment.md": {"modified": 1751815542.3167846, "size": 12407}, "PRPs/development/performance-optimization.md": {"modified": 1751819422.713786, "size": 32173}, "PRPs/development/language-parsers.md": {"modified": 1751802451.6707864, "size": 34530}, "PRPs/development/testing-strategy.md": {"modified": 1751816373.0942981, "size": 3381}, "PRPs/ai-ml/embeddings.md": {"modified": 1751815542.3118598, "size": 36594}, "PRPs/ai-ml/pattern-recognition.md": {"modified": 1751815542.3125334, "size": 38200}, "PRPs/ai-ml/gemini-integration.md": {"modified": 1751815542.312137, "size": 29952}, "PRPs/sdk/javascript-sdk.md": {"modified": 1751816599.3646986, "size": 37484}, "PRPs/sdk/python-sdk.md": {"modified": 1751816635.9759846, "size": 51186}, "PRPs/sdk/go-sdk.md": {"modified": 1751816679.698833, "size": 48218}, "PRPs/deployment/ci-cd-pipeline.md": {"modified": 1751816354.0796711, "size": 4017}, "PRPs/api/code-generation-api.md": {"modified": 1751816319.1036968, "size": 2713}, "PRPs/api/oauth-implementation.md": {"modified": 1751815542.3149998, "size": 31896}, "PRPs/api/simulations-api.md": {"modified": 1751819380.4307666, "size": 8434}, "PRPs/api/endpoints-specification.md": {"modified": 1751818393.2513807, "size": 75043}, "PRPs/api/webhooks.md": {"modified": 1751815542.3158143, "size": 28714}, "PRPs/api/graphql-api.md": {"modified": 1751815542.3142693, "size": 30527}, "PRPs/api/rest-api.md": {"modified": 1751820108.5450091, "size": 35091}, "PRPs/templates/service-prp.md": {"modified": 1751799214.9232624, "size": 7798}, "PRPs/infrastructure/gcp-setup.md": {"modified": 1751800182.9033766, "size": 14154}, "PRPs/services/collaboration.md": {"modified": 1751800099.8164842, "size": 13544}, "PRPs/services/query-intelligence.md": {"modified": 1751820374.7749925, "size": 12613}, "PRPs/services/marketplace.md": {"modified": 1751819616.576909, "size": 16438}, "PRPs/services/pattern-mining.md": {"modified": 1751809768.606574, "size": 13551}, "PRPs/services/analysis-engine.md": {"modified": 1751820327.6514516, "size": 11250}}, "summary": {"total": 43, "recently_modified": ["PLANNING.md", "INITIAL.md", "README.md", "TASK.md", "CLAUDE.md", "PRPs/architecture-patterns.md", "PRPs/feature-specifications.md", "PRPs/implementation-guide.md", "PRPs/README.md", "PRPs/database/bigquery-analytics.md", "PRPs/database/firestore-collections.md", "PRPs/database/spanner-schema.md", "PRPs/security/authentication.md", "PRPs/security/authorization.md", "PRPs/security/zero-trust-architecture.md", "PRPs/security/incident-response.md", "PRPs/security/encryption.md", "PRPs/security/compliance.md", "PRPs/development/dev-environment.md", "PRPs/development/performance-optimization.md", "PRPs/development/language-parsers.md", "PRPs/development/testing-strategy.md", "PRPs/ai-ml/embeddings.md", "PRPs/ai-ml/pattern-recognition.md", "PRPs/ai-ml/gemini-integration.md", "PRPs/sdk/javascript-sdk.md", "PRPs/sdk/python-sdk.md", "PRPs/sdk/go-sdk.md", "PRPs/deployment/ci-cd-pipeline.md", "PRPs/api/code-generation-api.md", "PRPs/api/oauth-implementation.md", "PRPs/api/simulations-api.md", "PRPs/api/endpoints-specification.md", "PRPs/api/webhooks.md", "PRPs/api/graphql-api.md", "PRPs/api/rest-api.md", "PRPs/templates/service-prp.md", "PRPs/infrastructure/gcp-setup.md", "PRPs/services/collaboration.md", "PRPs/services/query-intelligence.md", "PRPs/services/marketplace.md", "PRPs/services/pattern-mining.md", "PRPs/services/analysis-engine.md"]}}