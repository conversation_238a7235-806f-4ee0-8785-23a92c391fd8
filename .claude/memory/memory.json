{"version": "1.0", "initialized": "2025-01-06T19:30:00Z", "sessions": {"session_20250706_200410": {"created": "2025-07-06T17:04:10.612638", "context": {}, "tasks": [], "notes": []}, "session_20250706_200435": {"created": "2025-07-06T17:04:35.528759", "context": {}, "tasks": [], "notes": []}, "session_20250706_200442": {"created": "2025-07-06T17:04:42.010930", "context": {}, "tasks": [], "notes": []}, "session_20250706_200443": {"created": "2025-07-06T17:04:43.090987", "context": {}, "tasks": [], "notes": []}, "session_20250706_201636": {"created": "2025-07-06T17:16:36.218126", "context": {}, "tasks": [], "notes": []}}, "persistent_context": {"project_overview": {}, "architecture_patterns": {}, "development_standards": {}, "current_tasks": {}}, "context_priorities": {"high": ["PLANNING.md", "CLAUDE.md", "TASK.md"], "medium": ["PRPs/", "examples/", "docs/architecture/"], "low": ["docs/business/", "docs/guides/"]}}