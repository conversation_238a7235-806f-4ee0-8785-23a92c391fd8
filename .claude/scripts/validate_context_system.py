#!/usr/bin/env python3
"""
Context System Validation Script
Tests all components of the CCL context system
"""

import json
import os
import subprocess
import sys
import yaml
from pathlib import Path
from typing import Dict, Any, List, Tuple

class ContextSystemValidator:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.claude_dir = self.project_root / ".claude"
        self.validation_results = []
        
    def validate_all(self) -> Dict[str, Any]:
        """Run all validation tests"""
        print("🔍 Validating CCL Context System...")
        
        results = {
            "timestamp": self._get_timestamp(),
            "overall_status": "unknown",
            "tests": {
                "directory_structure": self._validate_directory_structure(),
                "memory_system": self._validate_memory_system(),
                "context_loader": self._validate_context_loader(),
                "session_manager": self._validate_session_manager(),
                "integration_manager": self._validate_integration_manager(),
                "configuration": self._validate_configuration(),
                "file_permissions": self._validate_file_permissions(),
                "dependencies": self._validate_dependencies()
            }
        }
        
        # Determine overall status
        failed_tests = [name for name, result in results["tests"].items() 
                       if result["status"] == "failed"]
        
        if not failed_tests:
            results["overall_status"] = "passed"
            print("✅ All validation tests passed!")
        else:
            results["overall_status"] = "failed"
            print(f"❌ {len(failed_tests)} validation tests failed:")
            for test in failed_tests:
                print(f"  - {test}")
        
        return results
    
    def _validate_directory_structure(self) -> Dict[str, Any]:
        """Validate directory structure"""
        print("  📁 Validating directory structure...")
        
        required_dirs = [
            ".claude",
            ".claude/memory",
            ".claude/memory/sessions",
            ".claude/memory/context", 
            ".claude/memory/patterns",
            ".claude/memory/tasks",
            ".claude/cache",
            ".claude/cache/prps",
            ".claude/cache/docs",
            ".claude/cache/code",
            ".claude/scripts",
            ".claude/config",
            ".claude/logs"
        ]
        
        missing_dirs = []
        for dir_path in required_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                missing_dirs.append(dir_path)
        
        if missing_dirs:
            return {
                "status": "failed",
                "error": f"Missing directories: {', '.join(missing_dirs)}",
                "missing_dirs": missing_dirs
            }
        
        return {
            "status": "passed",
            "message": "All required directories exist"
        }
    
    def _validate_memory_system(self) -> Dict[str, Any]:
        """Validate memory system"""
        print("  🧠 Validating memory system...")
        
        memory_file = self.claude_dir / "memory/memory.json"
        
        if not memory_file.exists():
            return {
                "status": "failed",
                "error": "Memory file does not exist"
            }
        
        try:
            with open(memory_file, 'r') as f:
                memory_data = json.load(f)
            
            # Check required fields
            required_fields = ["version", "initialized", "sessions", "persistent_context"]
            missing_fields = [field for field in required_fields 
                            if field not in memory_data]
            
            if missing_fields:
                return {
                    "status": "failed",
                    "error": f"Missing required fields: {', '.join(missing_fields)}"
                }
            
            return {
                "status": "passed",
                "message": "Memory system is valid",
                "version": memory_data.get("version"),
                "initialized": memory_data.get("initialized")
            }
            
        except json.JSONDecodeError as e:
            return {
                "status": "failed",
                "error": f"Invalid JSON in memory file: {e}"
            }
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Error reading memory file: {e}"
            }
    
    def _validate_context_loader(self) -> Dict[str, Any]:
        """Validate context loader"""
        print("  🔄 Validating context loader...")
        
        loader_script = self.claude_dir / "scripts/context_loader.py"
        
        if not loader_script.exists():
            return {
                "status": "failed",
                "error": "Context loader script does not exist"
            }
        
        if not os.access(loader_script, os.X_OK):
            return {
                "status": "failed",
                "error": "Context loader script is not executable"
            }
        
        # Test basic functionality
        try:
            result = subprocess.run(
                [sys.executable, str(loader_script), "--load-context"],
                capture_output=True, text=True, cwd=self.project_root
            )
            
            if result.returncode != 0:
                return {
                    "status": "failed",
                    "error": f"Context loader failed: {result.stderr}"
                }
            
            # Try to parse output as JSON
            try:
                output_data = json.loads(result.stdout)
                return {
                    "status": "passed",
                    "message": "Context loader is functional",
                    "output_keys": list(output_data.keys()) if isinstance(output_data, dict) else None
                }
            except json.JSONDecodeError:
                return {
                    "status": "warning",
                    "message": "Context loader runs but output is not valid JSON"
                }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Error testing context loader: {e}"
            }
    
    def _validate_session_manager(self) -> Dict[str, Any]:
        """Validate session manager"""
        print("  📋 Validating session manager...")
        
        session_script = self.claude_dir / "scripts/session_manager.sh"
        
        if not session_script.exists():
            return {
                "status": "failed",
                "error": "Session manager script does not exist"
            }
        
        if not os.access(session_script, os.X_OK):
            return {
                "status": "failed",
                "error": "Session manager script is not executable"
            }
        
        # Test help command
        try:
            result = subprocess.run(
                ["bash", str(session_script), "help"],
                capture_output=True, text=True, cwd=self.project_root
            )
            
            if result.returncode != 0:
                return {
                    "status": "failed",
                    "error": f"Session manager failed: {result.stderr}"
                }
            
            # Check if help output contains expected commands
            expected_commands = ["create", "restore", "list", "current", "cleanup"]
            missing_commands = [cmd for cmd in expected_commands 
                              if cmd not in result.stdout]
            
            if missing_commands:
                return {
                    "status": "warning",
                    "message": f"Missing commands in help: {', '.join(missing_commands)}"
                }
            
            return {
                "status": "passed",
                "message": "Session manager is functional"
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Error testing session manager: {e}"
            }
    
    def _validate_integration_manager(self) -> Dict[str, Any]:
        """Validate integration manager"""
        print("  🔗 Validating integration manager...")
        
        integration_script = self.claude_dir / "scripts/integration_manager.py"
        
        if not integration_script.exists():
            return {
                "status": "failed",
                "error": "Integration manager script does not exist"
            }
        
        if not os.access(integration_script, os.X_OK):
            return {
                "status": "failed",
                "error": "Integration manager script is not executable"
            }
        
        # Test basic functionality
        try:
            result = subprocess.run(
                [sys.executable, str(integration_script), "--sync-all"],
                capture_output=True, text=True, cwd=self.project_root
            )
            
            if result.returncode != 0:
                return {
                    "status": "failed",
                    "error": f"Integration manager failed: {result.stderr}"
                }
            
            # Try to parse output as JSON
            try:
                output_data = json.loads(result.stdout)
                return {
                    "status": "passed",
                    "message": "Integration manager is functional",
                    "integrations": list(output_data.keys()) if isinstance(output_data, dict) else None
                }
            except json.JSONDecodeError:
                return {
                    "status": "warning",
                    "message": "Integration manager runs but output is not valid JSON"
                }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Error testing integration manager: {e}"
            }
    
    def _validate_configuration(self) -> Dict[str, Any]:
        """Validate configuration files"""
        print("  ⚙️  Validating configuration...")
        
        config_file = self.claude_dir / "config/context_integration.yaml"
        
        if not config_file.exists():
            return {
                "status": "failed",
                "error": "Configuration file does not exist"
            }
        
        try:
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f)
            
            # Check required sections
            required_sections = ["integrations", "context_priorities"]
            missing_sections = [section for section in required_sections 
                              if section not in config_data]
            
            if missing_sections:
                return {
                    "status": "failed",
                    "error": f"Missing configuration sections: {', '.join(missing_sections)}"
                }
            
            return {
                "status": "passed",
                "message": "Configuration is valid",
                "sections": list(config_data.keys())
            }
            
        except yaml.YAMLError as e:
            return {
                "status": "failed",
                "error": f"Invalid YAML in configuration: {e}"
            }
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Error reading configuration: {e}"
            }
    
    def _validate_file_permissions(self) -> Dict[str, Any]:
        """Validate file permissions"""
        print("  🔒 Validating file permissions...")
        
        executable_files = [
            ".claude/scripts/context_loader.py",
            ".claude/scripts/session_manager.sh",
            ".claude/scripts/integration_manager.py"
        ]
        
        permission_errors = []
        for file_path in executable_files:
            full_path = self.project_root / file_path
            if full_path.exists() and not os.access(full_path, os.X_OK):
                permission_errors.append(file_path)
        
        if permission_errors:
            return {
                "status": "failed",
                "error": f"Files not executable: {', '.join(permission_errors)}",
                "non_executable_files": permission_errors
            }
        
        return {
            "status": "passed",
            "message": "All files have correct permissions"
        }
    
    def _validate_dependencies(self) -> Dict[str, Any]:
        """Validate dependencies"""
        print("  📦 Validating dependencies...")
        
        # Check Python modules
        required_modules = ["json", "yaml", "subprocess", "pathlib", "datetime"]
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)
        
        if missing_modules:
            return {
                "status": "failed",
                "error": f"Missing Python modules: {', '.join(missing_modules)}"
            }
        
        # Check external tools
        external_tools = ["git", "bash"]
        missing_tools = []
        
        for tool in external_tools:
            try:
                subprocess.run([tool, "--version"], capture_output=True)
            except FileNotFoundError:
                missing_tools.append(tool)
        
        if missing_tools:
            return {
                "status": "warning",
                "message": f"Optional tools not available: {', '.join(missing_tools)}"
            }
        
        return {
            "status": "passed",
            "message": "All dependencies are available"
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.utcnow().isoformat()
    
    def save_validation_report(self, results: Dict[str, Any]) -> None:
        """Save validation report"""
        report_file = self.claude_dir / "logs/validation_report.json"
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"📊 Validation report saved to: {report_file}")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Validate CCL Context System")
    parser.add_argument("--save-report", action="store_true", 
                       help="Save validation report to file")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    
    args = parser.parse_args()
    
    validator = ContextSystemValidator()
    results = validator.validate_all()
    
    if args.save_report:
        validator.save_validation_report(results)
    
    if args.verbose:
        print("\n📋 Detailed Results:")
        print(json.dumps(results, indent=2))
    
    # Exit with appropriate code
    sys.exit(0 if results["overall_status"] == "passed" else 1)

if __name__ == "__main__":
    main()