#!/bin/bash
# Session Manager for CCL Development
# Manages development sessions and context restoration

set -euo pipefail

# Configuration
SESSION_DIR=".claude/memory/sessions"
CURRENT_SESSION_FILE=".claude/memory/current_session"
CONTEXT_LOADER=".claude/scripts/context_loader.py"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# Create directories if they don't exist
init_directories() {
    mkdir -p "$SESSION_DIR"
    mkdir -p "$(dirname "$CURRENT_SESSION_FILE")"
}

# Create new development session
create_session() {
    init_directories
    
    local session_id="session_$(date +%Y%m%d_%H%M%S)"
    echo "$session_id" > "$CURRENT_SESSION_FILE"
    
    # Initialize session context using Python loader
    if [ -f "$CONTEXT_LOADER" ]; then
        log "Initializing session context..."
        python3 "$CONTEXT_LOADER" --create-session "$session_id"
        
        # Load initial context
        local context_file="$SESSION_DIR/$session_id.json"
        if [ -f "$context_file" ]; then
            log "Session context loaded successfully"
        else
            warn "Session context file not created"
        fi
    else
        error "Context loader not found: $CONTEXT_LOADER"
        return 1
    fi
    
    log "Created development session: $session_id"
    return 0
}

# Restore previous session
restore_session() {
    local session_id="$1"
    
    if [ -z "$session_id" ]; then
        if [ -f "$CURRENT_SESSION_FILE" ]; then
            session_id=$(cat "$CURRENT_SESSION_FILE")
            info "Using current session: $session_id"
        else
            error "No session ID provided and no current session found"
            return 1
        fi
    fi
    
    local session_file="$SESSION_DIR/$session_id.json"
    if [ -f "$session_file" ]; then
        log "Restoring session: $session_id"
        echo "$session_id" > "$CURRENT_SESSION_FILE"
        
        # Load session context
        python3 "$CONTEXT_LOADER" --restore-session "$session_id"
        
        log "Session restored successfully"
        return 0
    else
        error "Session not found: $session_id"
        return 1
    fi
}

# List available sessions
list_sessions() {
    init_directories
    
    info "Available development sessions:"
    if [ -d "$SESSION_DIR" ] && [ "$(ls -A "$SESSION_DIR" 2>/dev/null)" ]; then
        local current_session=""
        if [ -f "$CURRENT_SESSION_FILE" ]; then
            current_session=$(cat "$CURRENT_SESSION_FILE")
        fi
        
        for session_file in "$SESSION_DIR"/*.json; do
            if [ -f "$session_file" ]; then
                local session_id=$(basename "$session_file" .json)
                local created=$(jq -r '.created // "unknown"' "$session_file" 2>/dev/null || echo "unknown")
                
                if [ "$session_id" = "$current_session" ]; then
                    echo -e "  ${GREEN}* $session_id${NC} (current) - Created: $created"
                else
                    echo -e "    $session_id - Created: $created"
                fi
            fi
        done
    else
        warn "No sessions found"
    fi
}

# Get current session
get_current_session() {
    if [ -f "$CURRENT_SESSION_FILE" ]; then
        cat "$CURRENT_SESSION_FILE"
    else
        echo "No current session"
        return 1
    fi
}

# Clean up old sessions
cleanup_sessions() {
    local days_old="${1:-7}"
    
    info "Cleaning up sessions older than $days_old days..."
    
    if [ -d "$SESSION_DIR" ]; then
        find "$SESSION_DIR" -name "*.json" -type f -mtime +$days_old -delete
        log "Cleanup completed"
    else
        warn "Session directory not found"
    fi
}

# Save current session context
save_current_context() {
    local session_id
    if [ -f "$CURRENT_SESSION_FILE" ]; then
        session_id=$(cat "$CURRENT_SESSION_FILE")
        
        info "Saving context for session: $session_id"
        
        # Update session with current context
        python3 "$CONTEXT_LOADER" --load-context > "$SESSION_DIR/$session_id.json"
        
        log "Context saved successfully"
    else
        error "No current session to save"
        return 1
    fi
}

# Show session status
show_status() {
    info "Session Status:"
    
    if [ -f "$CURRENT_SESSION_FILE" ]; then
        local current_session=$(cat "$CURRENT_SESSION_FILE")
        local session_file="$SESSION_DIR/$current_session.json"
        
        echo "Current Session: $current_session"
        
        if [ -f "$session_file" ]; then
            local created=$(jq -r '.created // "unknown"' "$session_file" 2>/dev/null || echo "unknown")
            echo "Created: $created"
            
            local tasks=$(jq -r '.tasks | length' "$session_file" 2>/dev/null || echo "0")
            echo "Tasks: $tasks"
            
            local notes=$(jq -r '.notes | length' "$session_file" 2>/dev/null || echo "0")
            echo "Notes: $notes"
        else
            warn "Session file not found"
        fi
    else
        warn "No current session"
    fi
}

# Main function
main() {
    case "${1:-}" in
        "create"|"new")
            create_session
            ;;
        "restore"|"load")
            restore_session "${2:-}"
            ;;
        "list"|"ls")
            list_sessions
            ;;
        "current")
            get_current_session
            ;;
        "cleanup")
            cleanup_sessions "${2:-7}"
            ;;
        "save")
            save_current_context
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 {create|restore|list|current|cleanup|save|status|help}"
            echo ""
            echo "Commands:"
            echo "  create          Create new development session"
            echo "  restore [id]    Restore session (uses current if no ID provided)"
            echo "  list            List available sessions"
            echo "  current         Show current session ID"
            echo "  cleanup [days]  Clean up old sessions (default: 7 days)"
            echo "  save            Save current session context"
            echo "  status          Show session status"
            echo "  help            Show this help message"
            ;;
        *)
            if [ -z "${1:-}" ]; then
                # Default action: create new session
                create_session
            else
                error "Unknown command: $1"
                echo "Use '$0 help' for usage information"
                exit 1
            fi
            ;;
    esac
}

# Run main function with all arguments
main "$@"