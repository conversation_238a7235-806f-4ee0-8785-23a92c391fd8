#!/usr/bin/env python3
"""
Integration Test Suite for CCL Context System
Tests integration between different components
"""

import json
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

class IntegrationTestSuite:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.claude_dir = self.project_root / ".claude"
        self.test_results = []
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all integration tests"""
        print("🧪 Running CCL Context System Integration Tests...")
        
        results = {
            "timestamp": self._get_timestamp(),
            "overall_status": "unknown",
            "tests": {
                "memory_persistence": self._test_memory_persistence(),
                "session_lifecycle": self._test_session_lifecycle(),
                "context_restoration": self._test_context_restoration(),
                "task_integration": self._test_task_integration(),
                "prp_workflow": self._test_prp_workflow(),
                "cross_component": self._test_cross_component_integration()
            }
        }
        
        # Determine overall status
        failed_tests = [name for name, result in results["tests"].items() 
                       if result["status"] == "failed"]
        
        if not failed_tests:
            results["overall_status"] = "passed"
            print("✅ All integration tests passed!")
        else:
            results["overall_status"] = "failed"
            print(f"❌ {len(failed_tests)} integration tests failed:")
            for test in failed_tests:
                print(f"  - {test}")
        
        return results
    
    def _test_memory_persistence(self) -> Dict[str, Any]:
        """Test memory persistence functionality"""
        print("  🧠 Testing memory persistence...")
        
        try:
            # Create test session
            session_result = self._run_script("session_manager.sh", ["create"])
            if session_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to create test session"
                }
            
            # Wait a moment for session to be created
            time.sleep(1)
            
            # Check if session file exists
            session_dir = self.claude_dir / "memory/sessions"
            if not session_dir.exists():
                return {
                    "status": "failed",
                    "error": "Session directory not created"
                }
            
            session_files = list(session_dir.glob("*.json"))
            if not session_files:
                return {
                    "status": "failed",
                    "error": "No session files created"
                }
            
            # Verify session file content
            latest_session = max(session_files, key=lambda p: p.stat().st_mtime)
            with open(latest_session, 'r') as f:
                session_data = json.load(f)
            
            required_fields = ["created", "context", "tasks", "notes"]
            missing_fields = [field for field in required_fields 
                            if field not in session_data]
            
            if missing_fields:
                return {
                    "status": "failed",
                    "error": f"Missing session fields: {', '.join(missing_fields)}"
                }
            
            return {
                "status": "passed",
                "message": "Memory persistence working correctly",
                "session_file": str(latest_session)
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Memory persistence test failed: {e}"
            }
    
    def _test_session_lifecycle(self) -> Dict[str, Any]:
        """Test complete session lifecycle"""
        print("  🔄 Testing session lifecycle...")
        
        try:
            # Create session
            create_result = self._run_script("session_manager.sh", ["create"])
            if create_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to create session"
                }
            
            # List sessions
            list_result = self._run_script("session_manager.sh", ["list"])
            if list_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to list sessions"
                }
            
            # Get current session
            current_result = self._run_script("session_manager.sh", ["current"])
            if current_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to get current session"
                }
            
            current_session = current_result["stdout"].strip()
            
            # Test session restoration
            restore_result = self._run_script("session_manager.sh", ["restore", current_session])
            if restore_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to restore session"
                }
            
            return {
                "status": "passed",
                "message": "Session lifecycle working correctly",
                "current_session": current_session
            }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Session lifecycle test failed: {e}"
            }
    
    def _test_context_restoration(self) -> Dict[str, Any]:
        """Test context restoration functionality"""
        print("  📋 Testing context restoration...")
        
        try:
            # Load context
            context_result = self._run_script("context_loader.py", ["--load-context"])
            if context_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to load context"
                }
            
            # Parse context output
            try:
                context_data = json.loads(context_result["stdout"])
                
                # Check for expected context sections
                expected_sections = ["planning", "rules", "architecture"]
                available_sections = [section for section in expected_sections 
                                    if section in context_data]
                
                if not available_sections:
                    return {
                        "status": "warning",
                        "message": "No expected context sections found"
                    }
                
                return {
                    "status": "passed",
                    "message": "Context restoration working correctly",
                    "sections_loaded": available_sections
                }
                
            except json.JSONDecodeError:
                return {
                    "status": "failed",
                    "error": "Context output is not valid JSON"
                }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Context restoration test failed: {e}"
            }
    
    def _test_task_integration(self) -> Dict[str, Any]:
        """Test task management integration"""
        print("  📋 Testing task integration...")
        
        try:
            # Run task sync
            sync_result = self._run_script("integration_manager.py", ["--sync-tasks"])
            if sync_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to sync tasks"
                }
            
            # Parse sync output
            try:
                sync_data = json.loads(sync_result["stdout"])
                
                if sync_data.get("status") == "no_task_file":
                    return {
                        "status": "warning",
                        "message": "No TASK.md file found for integration testing"
                    }
                
                if sync_data.get("status") == "success":
                    return {
                        "status": "passed",
                        "message": "Task integration working correctly",
                        "tasks_found": sync_data.get("tasks_found", 0)
                    }
                
                return {
                    "status": "failed",
                    "error": f"Task sync failed: {sync_data.get('error', 'unknown')}"
                }
                
            except json.JSONDecodeError:
                return {
                    "status": "failed",
                    "error": "Task sync output is not valid JSON"
                }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Task integration test failed: {e}"
            }
    
    def _test_prp_workflow(self) -> Dict[str, Any]:
        """Test PRP workflow integration"""
        print("  📚 Testing PRP workflow...")
        
        try:
            # Run PRP sync
            sync_result = self._run_script("integration_manager.py", ["--sync-prps"])
            if sync_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to sync PRPs"
                }
            
            # Parse sync output
            try:
                sync_data = json.loads(sync_result["stdout"])
                
                if sync_data.get("status") == "no_prp_directory":
                    return {
                        "status": "warning",
                        "message": "No PRPs directory found for integration testing"
                    }
                
                if sync_data.get("status") == "success":
                    return {
                        "status": "passed",
                        "message": "PRP workflow integration working correctly",
                        "prps_found": sync_data.get("prps_found", 0)
                    }
                
                return {
                    "status": "failed",
                    "error": f"PRP sync failed: {sync_data.get('error', 'unknown')}"
                }
                
            except json.JSONDecodeError:
                return {
                    "status": "failed",
                    "error": "PRP sync output is not valid JSON"
                }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"PRP workflow test failed: {e}"
            }
    
    def _test_cross_component_integration(self) -> Dict[str, Any]:
        """Test cross-component integration"""
        print("  🔗 Testing cross-component integration...")
        
        try:
            # Run full integration sync
            sync_result = self._run_script("integration_manager.py", ["--sync-all"])
            if sync_result["returncode"] != 0:
                return {
                    "status": "failed",
                    "error": "Failed to run full integration sync"
                }
            
            # Parse sync output
            try:
                sync_data = json.loads(sync_result["stdout"])
                
                # Check all integration components
                integration_components = [
                    "task_management", "prp_workflow", 
                    "documentation", "development_tools"
                ]
                
                failed_components = []
                for component in integration_components:
                    if component in sync_data:
                        component_status = sync_data[component].get("status")
                        if component_status == "error":
                            failed_components.append(component)
                
                if failed_components:
                    return {
                        "status": "failed",
                        "error": f"Integration components failed: {', '.join(failed_components)}"
                    }
                
                return {
                    "status": "passed",
                    "message": "Cross-component integration working correctly",
                    "components_tested": len(integration_components)
                }
                
            except json.JSONDecodeError:
                return {
                    "status": "failed",
                    "error": "Integration sync output is not valid JSON"
                }
            
        except Exception as e:
            return {
                "status": "failed",
                "error": f"Cross-component integration test failed: {e}"
            }
    
    def _run_script(self, script_name: str, args: List[str] = None) -> Dict[str, Any]:
        """Run a script and return results"""
        script_path = self.claude_dir / "scripts" / script_name
        
        if script_name.endswith('.py'):
            cmd = [sys.executable, str(script_path)]
        else:
            cmd = ["bash", str(script_path)]
        
        if args:
            cmd.extend(args)
        
        result = subprocess.run(
            cmd, capture_output=True, text=True, cwd=self.project_root
        )
        
        return {
            "returncode": result.returncode,
            "stdout": result.stdout,
            "stderr": result.stderr
        }
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.utcnow().isoformat()
    
    def save_test_report(self, results: Dict[str, Any]) -> None:
        """Save test report"""
        report_file = self.claude_dir / "logs/integration_test_report.json"
        report_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"📊 Integration test report saved to: {report_file}")

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run CCL Context System Integration Tests")
    parser.add_argument("--save-report", action="store_true", 
                       help="Save test report to file")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Verbose output")
    
    args = parser.parse_args()
    
    test_suite = IntegrationTestSuite()
    results = test_suite.run_all_tests()
    
    if args.save_report:
        test_suite.save_test_report(results)
    
    if args.verbose:
        print("\n📋 Detailed Results:")
        print(json.dumps(results, indent=2))
    
    # Exit with appropriate code
    sys.exit(0 if results["overall_status"] == "passed" else 1)

if __name__ == "__main__":
    main()