#!/usr/bin/env python3
"""
Integration Manager for CCL Context System
Manages integrations with task management, PRPs, and development tools
"""

import json
import os
import subprocess
import yaml
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

class IntegrationManager:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.config_path = self.project_root / ".claude/config/context_integration.yaml"
        self.memory_path = self.project_root / ".claude/memory"
        self.logs_path = self.project_root / ".claude/logs"
        
        # Ensure directories exist
        self.memory_path.mkdir(parents=True, exist_ok=True)
        self.logs_path.mkdir(parents=True, exist_ok=True)
        
        # Load configuration
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """Load integration configuration"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r') as f:
                    return yaml.safe_load(f)
            except yaml.YAMLError as e:
                self.log_error(f"Error loading config: {e}")
                return self.get_default_config()
        return self.get_default_config()
    
    def get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "integrations": {
                "task_management": {"enabled": True},
                "prp_workflow": {"enabled": True},
                "documentation": {"enabled": True},
                "development_tools": {"enabled": True}
            },
            "context_priorities": {
                "always_load": ["PLANNING.md", "CLAUDE.md", "TASK.md"],
                "load_on_demand": ["PRPs/", "docs/"],
                "cache_frequently_used": ["PRPs/templates/"]
            }
        }
    
    def sync_task_management(self) -> Dict[str, Any]:
        """Sync with task management system"""
        if not self.config["integrations"]["task_management"]["enabled"]:
            return {"status": "disabled"}
        
        task_file = self.project_root / "TASK.md"
        if not task_file.exists():
            return {"status": "no_task_file"}
        
        try:
            task_content = task_file.read_text()
            
            # Parse task content
            tasks = self.parse_tasks(task_content)
            
            # Update memory with task state
            self.update_task_memory(tasks)
            
            return {
                "status": "success",
                "tasks_found": len(tasks),
                "last_sync": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Task sync error: {e}")
            return {"status": "error", "error": str(e)}
    
    def sync_prp_workflow(self) -> Dict[str, Any]:
        """Sync with PRP workflow system"""
        if not self.config["integrations"]["prp_workflow"]["enabled"]:
            return {"status": "disabled"}
        
        prp_dir = self.project_root / "PRPs"
        if not prp_dir.exists():
            return {"status": "no_prp_directory"}
        
        try:
            prp_files = list(prp_dir.glob("*.md"))
            prp_context = {}
            
            for prp_file in prp_files:
                prp_context[prp_file.stem] = {
                    "path": str(prp_file),
                    "modified": prp_file.stat().st_mtime,
                    "size": prp_file.stat().st_size
                }
            
            # Update memory with PRP context
            self.update_prp_memory(prp_context)
            
            return {
                "status": "success",
                "prps_found": len(prp_files),
                "last_sync": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"PRP sync error: {e}")
            return {"status": "error", "error": str(e)}
    
    def sync_documentation(self) -> Dict[str, Any]:
        """Sync with documentation system"""
        if not self.config["integrations"]["documentation"]["enabled"]:
            return {"status": "disabled"}
        
        try:
            doc_files = []
            
            # Find all documentation files
            for pattern in self.config["integrations"]["documentation"].get("watch_files", ["*.md"]):
                doc_files.extend(self.project_root.glob(pattern))
            
            doc_context = {}
            for doc_file in doc_files:
                if doc_file.is_file():
                    doc_context[str(doc_file)] = {
                        "modified": doc_file.stat().st_mtime,
                        "size": doc_file.stat().st_size
                    }
            
            # Update memory with documentation context
            self.update_doc_memory(doc_context)
            
            return {
                "status": "success",
                "docs_found": len(doc_files),
                "last_sync": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Documentation sync error: {e}")
            return {"status": "error", "error": str(e)}
    
    def sync_development_tools(self) -> Dict[str, Any]:
        """Sync with development tools"""
        if not self.config["integrations"]["development_tools"]["enabled"]:
            return {"status": "disabled"}
        
        try:
            tool_status = {}
            
            # Check git status
            if "git" in self.config["integrations"]["development_tools"].get("tool_integration", []):
                tool_status["git"] = self.check_git_status()
            
            # Check docker status
            if "docker" in self.config["integrations"]["development_tools"].get("tool_integration", []):
                tool_status["docker"] = self.check_docker_status()
            
            # Check gcloud status
            if "gcloud" in self.config["integrations"]["development_tools"].get("tool_integration", []):
                tool_status["gcloud"] = self.check_gcloud_status()
            
            # Update memory with tool status
            self.update_tool_memory(tool_status)
            
            return {
                "status": "success",
                "tools_checked": len(tool_status),
                "last_sync": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"Development tools sync error: {e}")
            return {"status": "error", "error": str(e)}
    
    def parse_tasks(self, content: str) -> List[Dict[str, Any]]:
        """Parse tasks from TASK.md content"""
        tasks = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if any(status in line.lower() for status in ['todo', 'in progress', 'done', 'blocked']):
                task = {
                    "text": line,
                    "status": self.extract_task_status(line),
                    "priority": self.extract_task_priority(line),
                    "tags": self.extract_task_tags(line)
                }
                tasks.append(task)
        
        return tasks
    
    def extract_task_status(self, line: str) -> str:
        """Extract task status from line"""
        line_lower = line.lower()
        if 'done' in line_lower or '✅' in line:
            return 'done'
        elif 'in progress' in line_lower or '🔄' in line:
            return 'in_progress'
        elif 'blocked' in line_lower or '🚫' in line:
            return 'blocked'
        else:
            return 'todo'
    
    def extract_task_priority(self, line: str) -> str:
        """Extract task priority from line"""
        line_lower = line.lower()
        if 'high' in line_lower or '🔴' in line:
            return 'high'
        elif 'low' in line_lower or '🟢' in line:
            return 'low'
        else:
            return 'medium'
    
    def extract_task_tags(self, line: str) -> List[str]:
        """Extract task tags from line"""
        import re
        tags = re.findall(r'#(\w+)', line)
        return tags
    
    def check_git_status(self) -> Dict[str, Any]:
        """Check git repository status"""
        try:
            result = subprocess.run(['git', 'status', '--porcelain'], 
                                  capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                changes = result.stdout.strip().split('\n') if result.stdout.strip() else []
                return {
                    "status": "clean" if not changes else "dirty",
                    "changes": len(changes),
                    "files": changes[:10]  # Limit to first 10 files
                }
            else:
                return {"status": "error", "error": result.stderr}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def check_docker_status(self) -> Dict[str, Any]:
        """Check Docker status"""
        try:
            result = subprocess.run(['docker', 'ps', '--format', 'table {{.Names}}'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                containers = result.stdout.strip().split('\n')[1:]  # Skip header
                return {
                    "status": "available",
                    "containers": len(containers),
                    "running": [c for c in containers if c.strip()]
                }
            else:
                return {"status": "unavailable", "error": result.stderr}
        except Exception as e:
            return {"status": "unavailable", "error": str(e)}
    
    def check_gcloud_status(self) -> Dict[str, Any]:
        """Check Google Cloud SDK status"""
        try:
            result = subprocess.run(['gcloud', 'config', 'get-value', 'project'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                project = result.stdout.strip()
                return {
                    "status": "authenticated",
                    "project": project
                }
            else:
                return {"status": "not_authenticated", "error": result.stderr}
        except Exception as e:
            return {"status": "unavailable", "error": str(e)}
    
    def update_task_memory(self, tasks: List[Dict[str, Any]]) -> None:
        """Update memory with task information"""
        memory_file = self.memory_path / "task_memory.json"
        
        task_memory = {
            "last_update": datetime.utcnow().isoformat(),
            "tasks": tasks,
            "summary": {
                "total": len(tasks),
                "todo": len([t for t in tasks if t["status"] == "todo"]),
                "in_progress": len([t for t in tasks if t["status"] == "in_progress"]),
                "done": len([t for t in tasks if t["status"] == "done"]),
                "blocked": len([t for t in tasks if t["status"] == "blocked"])
            }
        }
        
        with open(memory_file, 'w') as f:
            json.dump(task_memory, f, indent=2)
    
    def update_prp_memory(self, prp_context: Dict[str, Any]) -> None:
        """Update memory with PRP information"""
        memory_file = self.memory_path / "prp_memory.json"
        
        prp_memory = {
            "last_update": datetime.utcnow().isoformat(),
            "prps": prp_context,
            "summary": {
                "total": len(prp_context),
                "recently_modified": [
                    name for name, info in prp_context.items()
                    if (datetime.utcnow().timestamp() - info["modified"]) < 86400  # 24 hours
                ]
            }
        }
        
        with open(memory_file, 'w') as f:
            json.dump(prp_memory, f, indent=2)
    
    def update_doc_memory(self, doc_context: Dict[str, Any]) -> None:
        """Update memory with documentation information"""
        memory_file = self.memory_path / "doc_memory.json"
        
        doc_memory = {
            "last_update": datetime.utcnow().isoformat(),
            "documents": doc_context,
            "summary": {
                "total": len(doc_context),
                "recently_modified": [
                    path for path, info in doc_context.items()
                    if (datetime.utcnow().timestamp() - info["modified"]) < 86400  # 24 hours
                ]
            }
        }
        
        with open(memory_file, 'w') as f:
            json.dump(doc_memory, f, indent=2)
    
    def update_tool_memory(self, tool_status: Dict[str, Any]) -> None:
        """Update memory with development tool information"""
        memory_file = self.memory_path / "tool_memory.json"
        
        tool_memory = {
            "last_update": datetime.utcnow().isoformat(),
            "tools": tool_status,
            "summary": {
                "total_tools": len(tool_status),
                "available": [name for name, info in tool_status.items() 
                            if info.get("status") not in ["error", "unavailable"]]
            }
        }
        
        with open(memory_file, 'w') as f:
            json.dump(tool_memory, f, indent=2)
    
    def log_error(self, message: str) -> None:
        """Log error message"""
        log_file = self.logs_path / "context_system.log"
        
        with open(log_file, 'a') as f:
            f.write(f"[{datetime.utcnow().isoformat()}] ERROR: {message}\n")
    
    def sync_all(self) -> Dict[str, Any]:
        """Sync all integrations"""
        results = {
            "timestamp": datetime.utcnow().isoformat(),
            "task_management": self.sync_task_management(),
            "prp_workflow": self.sync_prp_workflow(),
            "documentation": self.sync_documentation(),
            "development_tools": self.sync_development_tools()
        }
        
        # Save sync results
        sync_file = self.memory_path / "last_sync.json"
        with open(sync_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        return results

def main():
    """Main entry point for CLI usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Integration Manager for CCL Context System")
    parser.add_argument("--sync-all", action="store_true", help="Sync all integrations")
    parser.add_argument("--sync-tasks", action="store_true", help="Sync task management")
    parser.add_argument("--sync-prps", action="store_true", help="Sync PRP workflow")
    parser.add_argument("--sync-docs", action="store_true", help="Sync documentation")
    parser.add_argument("--sync-tools", action="store_true", help="Sync development tools")
    
    args = parser.parse_args()
    
    manager = IntegrationManager()
    
    if args.sync_all:
        results = manager.sync_all()
        print(json.dumps(results, indent=2))
    elif args.sync_tasks:
        result = manager.sync_task_management()
        print(json.dumps(result, indent=2))
    elif args.sync_prps:
        result = manager.sync_prp_workflow()
        print(json.dumps(result, indent=2))
    elif args.sync_docs:
        result = manager.sync_documentation()
        print(json.dumps(result, indent=2))
    elif args.sync_tools:
        result = manager.sync_development_tools()
        print(json.dumps(result, indent=2))
    else:
        # Default: sync all
        results = manager.sync_all()
        print(json.dumps(results, indent=2))

if __name__ == "__main__":
    main()