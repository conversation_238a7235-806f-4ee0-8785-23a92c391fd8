#!/usr/bin/env python3
"""
Context Loader for CCL Development Sessions
Manages project context loading and session management
"""

import json
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

class ContextLoader:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.memory_path = self.project_root / ".claude/memory"
        self.cache_path = self.project_root / ".claude/cache"
        self.memory_file = self.memory_path / "memory.json"
        
        # Ensure directories exist
        self.memory_path.mkdir(parents=True, exist_ok=True)
        self.cache_path.mkdir(parents=True, exist_ok=True)
        
    def load_session_context(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """Load context for development session"""
        if not session_id:
            session_id = self.create_new_session()
            
        context = {
            "session_id": session_id,
            "timestamp": datetime.utcnow().isoformat(),
            "project_context": self.load_project_context(),
            "task_context": self.load_task_context(),
            "prp_context": self.load_prp_context(),
            "memory_context": self.load_memory_context(session_id)
        }
        
        return context
    
    def load_project_context(self) -> Dict[str, Any]:
        """Load essential project context"""
        context = {}
        
        # Load planning document
        planning_path = self.project_root / "PLANNING.md"
        if planning_path.exists():
            context["planning"] = planning_path.read_text()
            
        # Load Claude rules
        claude_path = self.project_root / "CLAUDE.md"
        if claude_path.exists():
            context["rules"] = claude_path.read_text()
            
        # Load architecture patterns
        arch_patterns_path = self.project_root / "PRPs/architecture-patterns.md"
        if arch_patterns_path.exists():
            context["architecture"] = arch_patterns_path.read_text()
            
        return context
    
    def load_task_context(self) -> Dict[str, Any]:
        """Load current task context"""
        task_path = self.project_root / "TASK.md"
        if task_path.exists():
            task_content = task_path.read_text()
            return {
                "current_tasks": task_content,
                "active_sprint": self.extract_active_sprint(task_content),
                "in_progress": self.extract_in_progress_tasks(task_content),
                "blockers": self.extract_blockers(task_content)
            }
        return {}
    
    def load_prp_context(self) -> Dict[str, Any]:
        """Load PRP context"""
        prp_context = {}
        prp_dir = self.project_root / "PRPs"
        
        if prp_dir.exists():
            for prp_file in prp_dir.glob("*.md"):
                prp_context[prp_file.stem] = prp_file.read_text()
                
        return prp_context
    
    def load_memory_context(self, session_id: str) -> Dict[str, Any]:
        """Load memory context for session"""
        memory_data = self.load_memory()
        
        if session_id in memory_data.get("sessions", {}):
            return memory_data["sessions"][session_id]
        
        return {}
    
    def load_memory(self) -> Dict[str, Any]:
        """Load memory database"""
        if self.memory_file.exists():
            try:
                with open(self.memory_file, 'r') as f:
                    return json.load(f)
            except json.JSONDecodeError:
                return self.create_default_memory()
        return self.create_default_memory()
    
    def create_default_memory(self) -> Dict[str, Any]:
        """Create default memory structure"""
        return {
            "version": "1.0",
            "initialized": datetime.utcnow().isoformat(),
            "sessions": {},
            "persistent_context": {
                "project_overview": {},
                "architecture_patterns": {},
                "development_standards": {},
                "current_tasks": {}
            },
            "context_priorities": {
                "high": ["PLANNING.md", "CLAUDE.md", "TASK.md"],
                "medium": ["PRPs/", "examples/", "docs/architecture/"],
                "low": ["docs/business/", "docs/guides/"]
            }
        }
    
    def save_memory(self, memory_data: Dict[str, Any]) -> None:
        """Save memory database"""
        with open(self.memory_file, 'w') as f:
            json.dump(memory_data, f, indent=2)
    
    def create_new_session(self) -> str:
        """Create new development session"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        memory_data = self.load_memory()
        memory_data["sessions"][session_id] = {
            "created": datetime.utcnow().isoformat(),
            "context": {},
            "tasks": [],
            "notes": []
        }
        
        self.save_memory(memory_data)
        
        # Also create individual session file
        session_file = self.memory_path / "sessions" / f"{session_id}.json"
        session_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(session_file, 'w') as f:
            json.dump(memory_data["sessions"][session_id], f, indent=2)
        
        return session_id
    
    def save_session_memory(self, session_id: str, memory_data: Dict[str, Any]) -> None:
        """Save session memory for future reference"""
        session_file = self.memory_path / "sessions" / f"{session_id}.json"
        session_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(session_file, 'w') as f:
            json.dump(memory_data, f, indent=2)
    
    def extract_active_sprint(self, task_content: str) -> str:
        """Extract active sprint from task content"""
        lines = task_content.split('\n')
        for line in lines:
            if 'sprint' in line.lower() or 'active' in line.lower():
                return line.strip()
        return ""
    
    def extract_in_progress_tasks(self, task_content: str) -> list:
        """Extract in-progress tasks"""
        in_progress = []
        lines = task_content.split('\n')
        for line in lines:
            if 'in progress' in line.lower() or '🔄' in line:
                in_progress.append(line.strip())
        return in_progress
    
    def extract_blockers(self, task_content: str) -> list:
        """Extract blockers from task content"""
        blockers = []
        lines = task_content.split('\n')
        for line in lines:
            if 'blocked' in line.lower() or 'blocker' in line.lower() or '🚫' in line:
                blockers.append(line.strip())
        return blockers

def main():
    """Main entry point for CLI usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Context Loader for CCL Development")
    parser.add_argument("--create-session", help="Create new session")
    parser.add_argument("--restore-session", help="Restore session")
    parser.add_argument("--load-context", action="store_true", help="Load project context")
    
    args = parser.parse_args()
    
    loader = ContextLoader()
    
    if args.create_session:
        session_id = loader.create_new_session()
        print(f"Created session: {session_id}")
    elif args.restore_session:
        context = loader.load_session_context(args.restore_session)
        print(f"Restored session: {args.restore_session}")
        print(json.dumps(context, indent=2))
    elif args.load_context:
        context = loader.load_project_context()
        print(json.dumps(context, indent=2))
    else:
        # Default: create new session and load context
        session_id = loader.create_new_session()
        context = loader.load_session_context(session_id)
        print(f"Initialized context system with session: {session_id}")
        print(json.dumps(context, indent=2))

if __name__ == "__main__":
    main()