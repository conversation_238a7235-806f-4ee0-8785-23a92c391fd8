#!/bin/bash
# Work on Specific Task Command
# Focuses development session on a specific task from TASK.md

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CLAUDE_DIR="$PROJECT_ROOT/.claude"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"; }
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }

# Change to project root
cd "$PROJECT_ROOT"

# Extract task information from TASK.md
extract_task_info() {
    local task_name="$1"
    local temp_file=$(mktemp)
    
    # Find the task section
    awk -v task="$task_name" '
    BEGIN { in_task = 0; task_found = 0 }
    /^\s*-\s*\[\s*\]\s*\*\*.*\*\*/ {
        if (tolower($0) ~ tolower(task)) {
            task_found = 1
            in_task = 1
            print $0
            next
        } else {
            in_task = 0
        }
    }
    /^\s*-\s*\[\s*\]\s*\*\*.*\*\*/ && in_task {
        in_task = 0
    }
    in_task && task_found {
        print $0
    }
    /^###/ && in_task {
        in_task = 0
    }
    ' TASK.md > "$temp_file"
    
    if [[ ! -s "$temp_file" ]]; then
        rm "$temp_file"
        return 1
    fi
    
    echo "$temp_file"
}

# Parse validation commands from task info
extract_validation_commands() {
    local task_file="$1"
    
    sed -n '/```bash/,/```/p' "$task_file" | \
    grep -v '```' | \
    grep -E '^\s*(make|npm|python|cargo|go)' | \
    sed 's/^\s*//'
}

# Parse service name from task info
extract_service() {
    local task_file="$1"
    
    grep -E "Service.*:" "$task_file" | \
    sed 's/.*Service.*:\s*//' | \
    sed 's/\s*(.*)//' | \
    head -1
}

# Parse dependencies from task info
extract_dependencies() {
    local task_file="$1"
    
    grep -A 3 "Dependencies" "$task_file" | \
    tail -n +2 | \
    sed 's/^\s*-\s*//'
}

# Parse success criteria from task info
extract_success_criteria() {
    local task_file="$1"
    
    grep -A 3 "Success Criteria" "$task_file" | \
    tail -n +2 | \
    sed 's/^\s*-\s*//'
}

main() {
    local task_name="$1"
    
    if [[ -z "$task_name" ]]; then
        error "Task name is required"
        show_help
        exit 1
    fi
    
    echo "🎯 Focusing on Task: $task_name"
    
    # 1. Check if TASK.md exists
    if [[ ! -f "TASK.md" ]]; then
        error "TASK.md not found"
        exit 1
    fi
    
    # 2. Extract task information
    log "Extracting task information..."
    local task_info_file
    task_info_file=$(extract_task_info "$task_name")
    
    if [[ -z "$task_info_file" ]]; then
        error "Task '$task_name' not found in TASK.md"
        echo ""
        info "Available tasks:"
        grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" TASK.md | \
        sed 's/.*\*\*\(.*\)\*\*.*/  - \1/' | head -10
        exit 1
    fi
    
    # 3. Parse task details
    local service=$(extract_service "$task_info_file")
    local validation_commands=$(extract_validation_commands "$task_info_file")
    local dependencies=$(extract_dependencies "$task_info_file")
    local success_criteria=$(extract_success_criteria "$task_info_file")
    
    # 4. Display task context
    echo ""
    info "📋 Task Context:"
    echo ""
    cat "$task_info_file"
    echo ""
    
    # 5. Show service focus
    if [[ -n "$service" ]]; then
        info "🏗️  Service Focus: $service"
        
        # Check if service directory exists
        if [[ -d "$service" ]]; then
            info "✅ Service directory found: $service/"
        else
            warn "⚠️  Service directory not found: $service/"
            echo "  You may need to create the service structure first"
        fi
        echo ""
    fi
    
    # 6. Show validation commands
    if [[ -n "$validation_commands" ]]; then
        info "🧪 Validation Commands:"
        echo "$validation_commands" | while read -r cmd; do
            echo "  $cmd"
        done
        echo ""
    fi
    
    # 7. Check dependencies
    if [[ -n "$dependencies" ]]; then
        info "🔗 Dependencies:"
        echo "$dependencies" | while read -r dep; do
            echo "  - $dep"
        done
        echo ""
    fi
    
    # 8. Show success criteria
    if [[ -n "$success_criteria" ]]; then
        info "🎯 Success Criteria:"
        echo "$success_criteria" | while read -r criteria; do
            echo "  - $criteria"
        done
        echo ""
    fi
    
    # 9. Get current session
    local current_session
    current_session=$("$CLAUDE_DIR/scripts/session_manager.sh" current 2>/dev/null || echo "")
    
    if [[ -n "$current_session" && "$current_session" != "No current session" ]]; then
        # 10. Update session context with focused task
        log "Updating session context..."
        local session_context
        session_context=$(cat << EOF
{
  "session_id": "$current_session",
  "focused_task": "$task_name",
  "service": "$service",
  "updated": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "validation_commands": $(echo "$validation_commands" | jq -R -s 'split("\n") | map(select(length > 0))'),
  "dependencies": $(echo "$dependencies" | jq -R -s 'split("\n") | map(select(length > 0))'),
  "success_criteria": $(echo "$success_criteria" | jq -R -s 'split("\n") | map(select(length > 0))')
}
EOF
)
        
        echo "$session_context" > "$CLAUDE_DIR/memory/sessions/${current_session}_task_focus.json"
        
        info "💾 Session updated with task focus"
    else
        warn "No active session found. Consider running: .claude/commands/start-dev-session"
    fi
    
    # 11. Show next steps
    echo ""
    info "🚀 Next Steps:"
    echo "  1. Review the task requirements above"
    echo "  2. Check dependencies are satisfied"
    
    if [[ -n "$service" && -d "$service" ]]; then
        echo "  3. Navigate to service: cd $service"
    elif [[ -n "$service" ]]; then
        echo "  3. Create service structure: mkdir -p $service"
    fi
    
    if [[ -n "$validation_commands" ]]; then
        echo "  4. Run validation commands to test current state"
        echo "  5. Use: .claude/commands/validate-current-work"
    fi
    
    echo "  6. Update TASK.md when making progress"
    echo "  7. Use: .claude/commands/update-task-status [status]"
    echo ""
    
    # Cleanup
    rm -f "$task_info_file"
    
    log "Ready to work on: $task_name"
}

# Show available tasks
show_available_tasks() {
    if [[ ! -f "TASK.md" ]]; then
        error "TASK.md not found"
        return 1
    fi
    
    echo ""
    info "📋 Available Tasks:"
    echo ""
    
    # In Progress tasks
    local in_progress
    in_progress=$(grep -A 20 "### In Progress" TASK.md | grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" | head -5)
    if [[ -n "$in_progress" ]]; then
        echo "🔄 IN PROGRESS:"
        echo "$in_progress" | sed 's/.*\*\*\(.*\)\*\*.*/  - \1/'
        echo ""
    fi
    
    # Ready tasks
    local ready
    ready=$(grep -A 30 "### Ready" TASK.md | grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" | head -5)
    if [[ -n "$ready" ]]; then
        echo "✅ READY:"
        echo "$ready" | sed 's/.*\*\*\(.*\)\*\*.*/  - \1/'
        echo ""
    fi
    
    # Backlog tasks
    local backlog
    backlog=$(grep -A 20 "### Backlog" TASK.md | grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" | head -5)
    if [[ -n "$backlog" ]]; then
        echo "📦 BACKLOG:"
        echo "$backlog" | sed 's/.*\*\*\(.*\)\*\*.*/  - \1/'
        echo ""
    fi
}

# Show help
show_help() {
    cat << EOF
Usage: .claude/commands/work-on-task [TASK_NAME] [OPTIONS]

Focus development session on a specific task from TASK.md

ARGUMENTS:
  TASK_NAME      Name or partial name of the task to work on

OPTIONS:
  -l, --list     List available tasks
  -h, --help     Show this help message
  -v, --verbose  Verbose output

DESCRIPTION:
  This command:
  1. Finds the specified task in TASK.md
  2. Extracts task details (service, validation commands, dependencies)
  3. Updates current session context
  4. Provides focused development guidance

EXAMPLES:
  .claude/commands/work-on-task "Repository Analysis"     # Work on specific task
  .claude/commands/work-on-task analysis                 # Partial match
  .claude/commands/work-on-task --list                   # List available tasks

EOF
}

# Parse command line arguments
if [[ $# -eq 0 ]]; then
    show_available_tasks
    exit 0
fi

while [[ $# -gt 0 ]]; do
    case $1 in
        -l|--list)
            show_available_tasks
            exit 0
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        -*)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            # This is the task name
            main "$1"
            exit 0
            ;;
    esac
done