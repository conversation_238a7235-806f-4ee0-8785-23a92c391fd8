#!/bin/bash
# Update Task Status Command
# Updates task status in TASK.md and session context

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CLAUDE_DIR="$PROJECT_ROOT/.claude"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"; }
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }

# Change to project root
cd "$PROJECT_ROOT"

# Update task status in TASK.md
update_task_in_file() {
    local task_name="$1"
    local new_status="$2"
    local confidence="${3:-}"
    local notes="${4:-}"
    
    if [[ ! -f "TASK.md" ]]; then
        error "TASK.md not found"
        return 1
    fi
    
    # Create backup
    cp TASK.md TASK.md.backup
    
    # Get current date
    local current_date=$(date +%Y-%m-%d)
    
    # Find and update the task
    local temp_file=$(mktemp)
    local task_found=false
    local in_task=false
    
    while IFS= read -r line; do
        # Check if this line starts a task that matches our search
        if [[ "$line" =~ ^[[:space:]]*-[[:space:]]*\[[[:space:]]*\][[:space:]]*\*\*.*\*\* ]]; then
            if [[ "$line" =~ $task_name ]]; then
                task_found=true
                in_task=true
                
                # Update the task line based on status
                case "$new_status" in
                    "started"|"in_progress"|"working")
                        # Mark as in progress and update date
                        local updated_line
                        if [[ "$line" =~ Started:[[:space:]]*[0-9-]+ ]]; then
                            # Keep existing start date
                            updated_line="$line"
                        else
                            # Add start date
                            updated_line=$(echo "$line" | sed "s/\*\*$/\*\* - Started: $current_date/")
                        fi
                        echo "$updated_line" >> "$temp_file"
                        ;;
                    "completed"|"done"|"finished")
                        # Mark as completed with date and confidence
                        local completed_line="$line"
                        completed_line=$(echo "$completed_line" | sed 's/\[\s*\]/[x]/')
                        completed_line=$(echo "$completed_line" | sed "s/\*\*$/\*\* - Completed: $current_date/")
                        if [[ -n "$confidence" ]]; then
                            completed_line="$completed_line (Confidence: $confidence%)"
                        fi
                        echo "$completed_line" >> "$temp_file"
                        ;;
                    "blocked"|"waiting")
                        # Mark as blocked with date
                        local blocked_line
                        blocked_line=$(echo "$line" | sed "s/\*\*$/\*\* - Blocked: $current_date/")
                        echo "$blocked_line" >> "$temp_file"
                        ;;
                    "paused"|"on_hold")
                        # Mark as paused with date
                        local paused_line
                        paused_line=$(echo "$line" | sed "s/\*\*$/\*\* - Paused: $current_date/")
                        echo "$paused_line" >> "$temp_file"
                        ;;
                    *)
                        error "Unknown status: $new_status"
                        rm "$temp_file"
                        return 1
                        ;;
                esac
                
                # Add notes if provided
                if [[ -n "$notes" ]]; then
                    echo "  - **Update**: $notes" >> "$temp_file"
                fi
                
            else
                echo "$line" >> "$temp_file"
                in_task=false
            fi
        elif [[ "$line" =~ ^[[:space:]]*-[[:space:]]*\[[[:space:]]*\][[:space:]]*\*\*.*\*\* ]] && [[ "$in_task" == true ]]; then
            # Found another task, stop processing current task
            in_task=false
            echo "$line" >> "$temp_file"
        else
            echo "$line" >> "$temp_file"
        fi
    done < TASK.md
    
    if [[ "$task_found" == false ]]; then
        error "Task '$task_name' not found in TASK.md"
        rm "$temp_file"
        return 1
    fi
    
    # Replace original file
    mv "$temp_file" TASK.md
    
    log "✅ Updated task '$task_name' status to '$new_status'"
    return 0
}

# Move task between sections if needed
move_task_section() {
    local task_name="$1"
    local new_status="$2"
    
    local source_section=""
    local target_section=""
    
    case "$new_status" in
        "started"|"in_progress"|"working")
            target_section="### In Progress"
            ;;
        "completed"|"done"|"finished")
            target_section="### Completed Tasks"
            ;;
        "blocked"|"waiting")
            target_section="### Blocked"
            ;;
        "ready")
            target_section="### Ready"
            ;;
        *)
            # Don't move for other statuses
            return 0
            ;;
    esac
    
    # This is a simplified version - in practice, you'd need more complex logic
    # to move tasks between sections while preserving formatting
    info "Note: Task section movement not fully implemented yet"
    warn "You may need to manually move the task to the '$target_section' section"
}

# Update session context
update_session_context() {
    local task_name="$1"
    local new_status="$2"
    local confidence="${3:-}"
    
    local current_session
    current_session=$("$CLAUDE_DIR/scripts/session_manager.sh" current 2>/dev/null || echo "")
    
    if [[ -n "$current_session" && "$current_session" != "No current session" ]]; then
        local task_focus_file="$CLAUDE_DIR/memory/sessions/${current_session}_task_focus.json"
        
        if [[ -f "$task_focus_file" ]]; then
            # Update task focus file with new status
            local updated_context
            updated_context=$(jq --arg status "$new_status" \
                                --arg confidence "$confidence" \
                                --arg updated "$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
                                '.task_status = $status | .confidence = $confidence | .last_updated = $updated' \
                                "$task_focus_file")
            
            echo "$updated_context" > "$task_focus_file"
            
            info "📱 Updated session context"
        fi
    fi
}

# Get current focused task
get_focused_task() {
    local current_session
    current_session=$("$CLAUDE_DIR/scripts/session_manager.sh" current 2>/dev/null || echo "")
    
    if [[ -n "$current_session" && "$current_session" != "No current session" ]]; then
        local task_focus_file="$CLAUDE_DIR/memory/sessions/${current_session}_task_focus.json"
        
        if [[ -f "$task_focus_file" ]]; then
            jq -r '.focused_task // ""' "$task_focus_file" 2>/dev/null || echo ""
        fi
    fi
}

# Show task status options
show_status_help() {
    cat << EOF

📋 Available Status Updates:

PROGRESS STATUSES:
  started, in_progress, working    Mark task as started/in progress
  completed, done, finished       Mark task as completed
  blocked, waiting                Mark task as blocked
  paused, on_hold                 Mark task as paused
  ready                          Mark task as ready to start

EXAMPLES:
  .claude/commands/update-task-status "Repository Analysis" started
  .claude/commands/update-task-status "Query Intelligence" completed 85
  .claude/commands/update-task-status "Pattern Detection" blocked "Waiting for API"

EOF
}

main() {
    local task_name="$1"
    local new_status="$2"
    local confidence="${3:-}"
    local notes="${4:-}"
    
    if [[ -z "$task_name" ]]; then
        # Try to get focused task
        local focused_task
        focused_task=$(get_focused_task)
        
        if [[ -n "$focused_task" ]]; then
            info "Using focused task: $focused_task"
            task_name="$focused_task"
        else
            error "Task name is required"
            show_help
            exit 1
        fi
    fi
    
    if [[ -z "$new_status" ]]; then
        error "Status is required"
        show_status_help
        exit 1
    fi
    
    echo "📝 Updating Task Status..."
    echo "Task: $task_name"
    echo "Status: $new_status"
    if [[ -n "$confidence" ]]; then
        echo "Confidence: $confidence%"
    fi
    if [[ -n "$notes" ]]; then
        echo "Notes: $notes"
    fi
    echo ""
    
    # 1. Update task in TASK.md
    if update_task_in_file "$task_name" "$new_status" "$confidence" "$notes"; then
        log "✅ TASK.md updated successfully"
    else
        error "Failed to update TASK.md"
        exit 1
    fi
    
    # 2. Move task between sections if needed
    move_task_section "$task_name" "$new_status"
    
    # 3. Update session context
    update_session_context "$task_name" "$new_status" "$confidence"
    
    # 4. Sync integrations
    log "Syncing integrations..."
    python3 "$CLAUDE_DIR/scripts/integration_manager.py" --sync-tasks > /dev/null 2>&1
    
    # 5. Show next steps based on status
    echo ""
    case "$new_status" in
        "started"|"in_progress"|"working")
            info "🚀 Next Steps:"
            echo "  1. Use: .claude/commands/validate-current-work"
            echo "  2. Make progress on the task"
            echo "  3. Update status when significant progress is made"
            ;;
        "completed"|"done"|"finished")
            info "🎉 Task Completed!"
            echo "  1. Run final validations"
            echo "  2. Consider starting next task"
            echo "  3. Use: .claude/commands/work-on-task --list"
            ;;
        "blocked"|"waiting")
            info "🚫 Task Blocked:"
            echo "  1. Document blocker details"
            echo "  2. Work on unblocked tasks"
            echo "  3. Follow up on blocker resolution"
            ;;
        "paused"|"on_hold")
            info "⏸️  Task Paused:"
            echo "  1. Document current state"
            echo "  2. Switch to another task"
            echo "  3. Plan when to resume"
            ;;
    esac
    
    log "Task status update complete!"
}

# Show help
show_help() {
    cat << EOF
Usage: .claude/commands/update-task-status [TASK_NAME] STATUS [CONFIDENCE] [NOTES]

Update task status in TASK.md and session context

ARGUMENTS:
  TASK_NAME      Name of the task (optional if task is focused)
  STATUS         New status for the task
  CONFIDENCE     Completion confidence percentage (0-100, optional)
  NOTES          Additional notes about the update (optional)

OPTIONS:
  -h, --help     Show this help message
  -v, --verbose  Verbose output

DESCRIPTION:
  Updates task status in TASK.md with timestamp and optionally moves
  the task to the appropriate section. Also updates session context.

EXAMPLES:
  .claude/commands/update-task-status "Repository Analysis" started
  .claude/commands/update-task-status "Query Intelligence" completed 85
  .claude/commands/update-task-status "Pattern Detection" blocked "Waiting for API setup"
  .claude/commands/update-task-status "" completed 90    # Use focused task

EOF
    show_status_help
}

# Parse command line arguments
if [[ $# -eq 0 ]]; then
    show_help
    exit 1
fi

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        -*)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            # Arguments: task_name, status, confidence, notes
            main "$@"
            exit $?
            ;;
    esac
done