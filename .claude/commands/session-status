#!/bin/bash
# Session Status Command
# Shows current development session status and context

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CLAUDE_DIR="$PROJECT_ROOT/.claude"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"; }
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }
header() { echo -e "${CYAN}$1${NC}"; }

# Change to project root
cd "$PROJECT_ROOT"

# Get current session info
get_session_info() {
    local current_session
    current_session=$("$CLAUDE_DIR/scripts/session_manager.sh" current 2>/dev/null || echo "")
    
    if [[ -z "$current_session" || "$current_session" == "No current session" ]]; then
        echo "none"
        return 1
    fi
    
    echo "$current_session"
    return 0
}

# Show session details
show_session_details() {
    local session_id="$1"
    
    header "📋 Session: $session_id"
    echo ""
    
    # Session metadata
    local session_file="$CLAUDE_DIR/memory/sessions/$session_id.json"
    if [[ -f "$session_file" ]]; then
        local created
        created=$(jq -r '.created // "unknown"' "$session_file" 2>/dev/null || echo "unknown")
        echo "Created: $created"
        
        local tasks
        tasks=$(jq -r '.tasks | length' "$session_file" 2>/dev/null || echo "0")
        echo "Tasks: $tasks"
        
        local notes
        notes=$(jq -r '.notes | length' "$session_file" 2>/dev/null || echo "0")
        echo "Notes: $notes"
        echo ""
    fi
    
    # Task focus if available
    local task_focus_file="$CLAUDE_DIR/memory/sessions/${session_id}_task_focus.json"
    if [[ -f "$task_focus_file" ]]; then
        header "🎯 Current Focus:"
        local focused_task
        focused_task=$(jq -r '.focused_task // "None"' "$task_focus_file" 2>/dev/null || echo "None")
        echo "Task: $focused_task"
        
        local service
        service=$(jq -r '.service // "None"' "$task_focus_file" 2>/dev/null || echo "None")
        echo "Service: $service"
        
        local task_status
        task_status=$(jq -r '.task_status // "Unknown"' "$task_focus_file" 2>/dev/null || echo "Unknown")
        echo "Status: $task_status"
        
        local confidence
        confidence=$(jq -r '.confidence // "N/A"' "$task_focus_file" 2>/dev/null || echo "N/A")
        echo "Confidence: $confidence"
        
        local last_updated
        last_updated=$(jq -r '.last_updated // "Never"' "$task_focus_file" 2>/dev/null || echo "Never")
        echo "Updated: $last_updated"
        echo ""
    fi
    
    # Context information
    local context_file="$CLAUDE_DIR/memory/sessions/${session_id}_context.json"
    if [[ -f "$context_file" ]]; then
        header "📚 Session Context:"
        local session_type
        session_type=$(jq -r '.session_type // "unknown"' "$context_file" 2>/dev/null || echo "unknown")
        echo "Type: $session_type"
        
        local started
        started=$(jq -r '.started // "unknown"' "$context_file" 2>/dev/null || echo "unknown")
        echo "Started: $started"
        echo ""
    fi
}

# Show integration status
show_integration_status() {
    header "🔗 Integration Status:"
    
    local last_sync_file="$CLAUDE_DIR/memory/last_sync.json"
    if [[ -f "$last_sync_file" ]]; then
        local timestamp
        timestamp=$(jq -r '.timestamp // "Never"' "$last_sync_file" 2>/dev/null || echo "Never")
        echo "Last Sync: $timestamp"
        echo ""
        
        # Show integration statuses
        local task_status
        task_status=$(jq -r '.task_management.status // "unknown"' "$last_sync_file" 2>/dev/null || echo "unknown")
        echo "Task Management: $task_status"
        
        local prp_status
        prp_status=$(jq -r '.prp_workflow.status // "unknown"' "$last_sync_file" 2>/dev/null || echo "unknown")
        echo "PRP Workflow: $prp_status"
        
        local doc_status
        doc_status=$(jq -r '.documentation.status // "unknown"' "$last_sync_file" 2>/dev/null || echo "unknown")
        echo "Documentation: $doc_status"
        
        local tools_status
        tools_status=$(jq -r '.development_tools.status // "unknown"' "$last_sync_file" 2>/dev/null || echo "unknown")
        echo "Development Tools: $tools_status"
        echo ""
    else
        warn "No sync information available"
        echo ""
    fi
}

# Show task summary from TASK.md
show_task_summary() {
    if [[ ! -f "TASK.md" ]]; then
        warn "TASK.md not found"
        return
    fi
    
    header "📋 Task Summary:"
    
    # Count tasks in different sections
    local in_progress
    in_progress=$(grep -A 20 "### In Progress" TASK.md | grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" | wc -l || echo "0")
    echo "In Progress: $in_progress"
    
    local ready
    ready=$(grep -A 30 "### Ready" TASK.md | grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" | wc -l || echo "0")
    echo "Ready: $ready"
    
    local backlog
    backlog=$(grep -A 20 "### Backlog" TASK.md | grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" | wc -l || echo "0")
    echo "Backlog: $backlog"
    
    local completed
    completed=$(grep -A 50 "### Completed Tasks" TASK.md | grep -E "^\s*-\s*\[x\]\s*\*\*.*\*\*" | wc -l || echo "0")
    echo "Completed: $completed"
    echo ""
}

# Show recent activity
show_recent_activity() {
    header "📈 Recent Activity:"
    
    # Show recent sessions
    if [[ -d "$CLAUDE_DIR/memory/sessions" ]]; then
        local recent_sessions
        recent_sessions=$(ls -t "$CLAUDE_DIR/memory/sessions"/*.json 2>/dev/null | head -3 || echo "")
        
        if [[ -n "$recent_sessions" ]]; then
            echo "Recent Sessions:"
            echo "$recent_sessions" | while read -r session_file; do
                local session_name
                session_name=$(basename "$session_file" .json)
                local created
                created=$(jq -r '.created // "unknown"' "$session_file" 2>/dev/null || echo "unknown")
                echo "  - $session_name ($created)"
            done
            echo ""
        fi
    fi
    
    # Show memory usage
    if [[ -d "$CLAUDE_DIR/memory" ]]; then
        local memory_size
        memory_size=$(du -sh "$CLAUDE_DIR/memory" 2>/dev/null | cut -f1 || echo "unknown")
        echo "Memory Usage: $memory_size"
        echo ""
    fi
}

# Show available commands
show_available_commands() {
    header "🛠️  Available Commands:"
    
    if [[ -d "$CLAUDE_DIR/commands" ]]; then
        for cmd in "$CLAUDE_DIR/commands"/*; do
            if [[ -x "$cmd" ]]; then
                local cmd_name
                cmd_name=$(basename "$cmd")
                echo "  .claude/commands/$cmd_name"
            fi
        done
        echo ""
    else
        warn "No commands directory found"
        echo ""
    fi
}

# Show next steps
show_next_steps() {
    header "🚀 Suggested Next Steps:"
    
    local current_session
    current_session=$(get_session_info)
    
    if [[ "$current_session" == "none" ]]; then
        echo "  1. Start a development session:"
        echo "     .claude/commands/start-dev-session"
        echo ""
        return
    fi
    
    # Check if there's a focused task
    local task_focus_file="$CLAUDE_DIR/memory/sessions/${current_session}_task_focus.json"
    if [[ -f "$task_focus_file" ]]; then
        local focused_task
        focused_task=$(jq -r '.focused_task // ""' "$task_focus_file" 2>/dev/null || echo "")
        
        if [[ -n "$focused_task" ]]; then
            echo "  1. Continue working on: $focused_task"
            echo "  2. Validate current work:"
            echo "     .claude/commands/validate-current-work"
            echo "  3. Update task status when making progress:"
            echo "     .claude/commands/update-task-status [status]"
        else
            echo "  1. Choose a task to work on:"
            echo "     .claude/commands/work-on-task --list"
            echo "  2. Focus on a specific task:"
            echo "     .claude/commands/work-on-task [task-name]"
        fi
    else
        echo "  1. Choose a task to work on:"
        echo "     .claude/commands/work-on-task --list"
        echo "  2. Focus on a specific task:"
        echo "     .claude/commands/work-on-task [task-name]"
    fi
    
    echo ""
}

main() {
    local mode="${1:-full}"
    
    case "$mode" in
        "full"|"detailed"|"complete")
            echo "🔍 CCL Development Session Status"
            echo "=================================="
            echo ""
            
            local current_session
            current_session=$(get_session_info)
            
            if [[ "$current_session" == "none" ]]; then
                warn "No active development session"
                echo ""
                show_available_commands
                show_next_steps
            else
                show_session_details "$current_session"
                show_integration_status
                show_task_summary
                show_recent_activity
                show_available_commands
                show_next_steps
            fi
            ;;
        "quick"|"brief"|"summary")
            local current_session
            current_session=$(get_session_info)
            
            if [[ "$current_session" == "none" ]]; then
                echo "❌ No active session"
            else
                echo "✅ Session: $current_session"
                
                local task_focus_file="$CLAUDE_DIR/memory/sessions/${current_session}_task_focus.json"
                if [[ -f "$task_focus_file" ]]; then
                    local focused_task
                    focused_task=$(jq -r '.focused_task // "None"' "$task_focus_file" 2>/dev/null || echo "None")
                    echo "🎯 Focus: $focused_task"
                fi
            fi
            ;;
        "session"|"current")
            local current_session
            current_session=$(get_session_info)
            
            if [[ "$current_session" == "none" ]]; then
                error "No active development session"
                exit 1
            else
                show_session_details "$current_session"
            fi
            ;;
        *)
            error "Unknown mode: $mode"
            show_help
            exit 1
            ;;
    esac
}

# Show help
show_help() {
    cat << EOF
Usage: .claude/commands/session-status [MODE]

Show current development session status and context

MODES:
  full           Show complete session status (default)
  quick          Show brief session summary
  session        Show current session details only

OPTIONS:
  -h, --help     Show this help message

DESCRIPTION:
  Displays information about the current development session including:
  - Session metadata and focus
  - Integration status
  - Task summary
  - Recent activity
  - Available commands
  - Suggested next steps

EXAMPLES:
  .claude/commands/session-status           # Full status
  .claude/commands/session-status quick     # Brief summary
  .claude/commands/session-status session   # Session details only

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -*)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            main "$1"
            exit 0
            ;;
    esac
done

# Default: show full status
main "full"