#!/bin/bash
# Start Development Session Command
# Creates a new development session based on current TASK.md

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CLAUDE_DIR="$PROJECT_ROOT/.claude"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Logging functions
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"; }
info() { echo -e "${BLUE}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1" >&2; }

# Change to project root
cd "$PROJECT_ROOT"

main() {
    echo "🚀 Starting CCL Development Session..."
    
    # 1. Check if TASK.md exists
    if [[ ! -f "TASK.md" ]]; then
        error "TASK.md not found. Cannot start task-based session."
        exit 1
    fi
    
    # 2. Create new development session
    log "Creating new development session..."
    local session_id
    session_id=$("$CLAUDE_DIR/scripts/session_manager.sh" create 2>&1 | grep "Created development session:" | cut -d':' -f2 | xargs)
    
    if [[ -z "$session_id" ]]; then
        error "Failed to create development session"
        exit 1
    fi
    
    log "Session created: $session_id"
    
    # 3. Parse current tasks from TASK.md
    log "Analyzing current tasks..."
    local in_progress_tasks
    in_progress_tasks=$(grep -A 20 "### In Progress" TASK.md | grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" | head -5)
    
    local ready_tasks
    ready_tasks=$(grep -A 30 "### Ready" TASK.md | grep -E "^\s*-\s*\[\s*\]\s*\*\*.*\*\*" | head -5)
    
    # 4. Display current task context
    echo ""
    info "📋 Current Task Context:"
    echo ""
    
    if [[ -n "$in_progress_tasks" ]]; then
        echo "🔄 IN PROGRESS:"
        echo "$in_progress_tasks" | sed 's/^/  /'
        echo ""
    fi
    
    if [[ -n "$ready_tasks" ]]; then
        echo "✅ READY TO START:"
        echo "$ready_tasks" | sed 's/^/  /'
        echo ""
    fi
    
    # 5. Load full context
    log "Loading project context..."
    python3 "$CLAUDE_DIR/scripts/context_loader.py" --load-context > /dev/null
    
    # 6. Sync all integrations
    log "Syncing integrations..."
    python3 "$CLAUDE_DIR/scripts/integration_manager.py" --sync-all > /dev/null
    
    # 7. Show suggested next actions
    echo ""
    info "🎯 Suggested Actions:"
    
    if [[ -n "$in_progress_tasks" ]]; then
        echo "  1. Continue working on in-progress tasks"
        echo "  2. Run validation commands for current work"
        echo "  3. Update task status when complete"
    fi
    
    if [[ -n "$ready_tasks" ]]; then
        echo "  4. Start a new ready task"
        echo "  5. Review dependencies before beginning"
    fi
    
    echo "  6. Use: .claude/commands/work-on-task [task-name]"
    echo "  7. Use: .claude/commands/validate-current-work"
    echo ""
    
    # 8. Show available commands
    info "📚 Available Commands:"
    if [[ -d "$CLAUDE_DIR/commands" ]]; then
        for cmd in "$CLAUDE_DIR/commands"/*; do
            if [[ -x "$cmd" ]]; then
                local cmd_name=$(basename "$cmd")
                echo "  .claude/commands/$cmd_name"
            fi
        done
    fi
    echo ""
    
    # 9. Save session context with current tasks
    log "Saving session context..."
    local session_context
    session_context=$(cat << EOF
{
  "session_id": "$session_id",
  "started": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "task_context": {
    "in_progress": $(echo "$in_progress_tasks" | jq -R -s 'split("\n") | map(select(length > 0))'),
    "ready": $(echo "$ready_tasks" | jq -R -s 'split("\n") | map(select(length > 0))')
  },
  "session_type": "task_based_development"
}
EOF
)
    
    echo "$session_context" > "$CLAUDE_DIR/memory/sessions/${session_id}_context.json"
    
    log "Development session ready!"
    echo ""
    info "Session ID: $session_id"
    info "Use '/commands' in Claude Code to see available development commands"
    echo ""
}

# Show help
show_help() {
    cat << EOF
Usage: .claude/commands/start-dev-session [OPTIONS]

Start a new CCL development session based on current TASK.md

OPTIONS:
  -h, --help     Show this help message
  -v, --verbose  Verbose output

DESCRIPTION:
  This command:
  1. Creates a new development session
  2. Analyzes current tasks from TASK.md
  3. Loads project context
  4. Syncs all integrations
  5. Provides task-based guidance
  6. Shows available development commands

EXAMPLES:
  .claude/commands/start-dev-session           # Start normal session
  .claude/commands/start-dev-session -v        # Start with verbose output

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            set -x
            shift
            ;;
        *)
            error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main "$@"