# CCL (Codebase Context Layer) - AI-Powered Developer Onboarding

Welcome to the CCL platform development environment! This README is your **complete onboarding guide** for contributing to the CCL codebase using AI coding assistants.

## 🚀 Quick Start for AI Agents

```bash
# 1. Load complete CCL context
/initialize-context-system

# 2. Integrate with current tasks
/integrate-task-system

# 3. Set up development environment
/setup-dev-environment

# 4. Start development session
/start-dev-session
```

## 🏗️ Platform Overview

CCL is a cloud-native, AI-powered architectural intelligence platform that transforms how developers understand and interact with codebases. Built entirely on Google Cloud Platform, CCL provides instant, conversational access to codebase knowledge through:

*   **Advanced Pattern Recognition** - ML-powered detection of architectural patterns
*   **Real-time Analysis** - Sub-30s analysis for 1M+ lines of code
*   **Natural Language Queries** - Conversational interface to codebase knowledge
*   **Pattern Marketplace** - Monetization platform for sharing coding patterns

## 📋 Essential Context Files

**Read these files in order before any development:**

1. **[CLAUDE.md](CLAUDE.md)** - Complete project context and AI assistant instructions
2. **[PLANNING.md](PLANNING.md)** - Architecture overview, technology stack, and development standards
3. **[TASK.md](TASK.md)** - Current tasks, sprint progress, and work tracking
4. **[PRPs/implementation-guide.md](PRPs/implementation-guide.md)** - Comprehensive implementation patterns
5. **[INITIAL.md](INITIAL.md)** - Template for new feature requests

## 🏗️ Architecture & Service Boundaries

### Microservices Architecture
```
ccl/
├── analysis-engine/     # Rust - Code parsing and AST analysis
├── query-intelligence/ # Python - AI/ML natural language processing  
├── pattern-mining/     # Python - ML-powered pattern detection
├── marketplace/        # Go - Pattern sharing and commerce
├── collaboration/      # TypeScript - Real-time collaborative features
├── web/               # TypeScript/React - Frontend application
├── sdk/               # TypeScript - Client SDK
└── shared/            # Shared utilities and types
```

### Technology Constraints (STRICTLY ENFORCED)
- **Rust**: analysis-engine only (AST parsing, code analysis)
- **Python**: query-intelligence, pattern-mining only (AI/ML operations)
- **Go**: marketplace only (high-performance commerce APIs)
- **TypeScript**: web, sdk, collaboration only (frontend and client libraries)

## 🛠️ Development Environment Setup

### Prerequisites
```bash
# Required tools (exact versions)
- Go 1.21+
- Rust 1.75+
- Python 3.11+
- Node.js 20+
- gcloud CLI
- Docker Desktop
- Terraform 1.5+
```

### Local Development
```bash
# Clone repository
git clone https://github.com/your-org/ccl.git
cd ccl

# Install dependencies
make install-deps

# Configure GCP
gcloud auth login
gcloud config set project ccl-development

# Set up local secrets
./scripts/setup-local-secrets.sh

# Start complete development environment
make dev-up
```

### Validation Commands
```bash
# Always run before committing
make lint-ccl                    # CCL-specific linting
make test-ccl                    # Full test suite
make security-scan-ccl           # Security validation
make performance-test-ccl        # Performance benchmarks
```

## 🎯 Development Workflow

### Starting Any Task
1. **Read Context**: Review CLAUDE.md, PLANNING.md, and relevant PRPs
2. **Check Tasks**: Update TASK.md with your work
3. **Load Context**: Run `/initialize-context-system`
4. **Understand Boundaries**: Respect service language restrictions
5. **Follow Patterns**: Use examples from `PRPs/implementation-guide.md`

### During Development
1. **Validate Continuously**: Run validation commands frequently
2. **Test Comprehensively**: >90% coverage required
3. **Document Thoroughly**: Update relevant PRPs and docs
4. **Monitor Performance**: Meet CCL platform SLAs
5. **Secure by Design**: Follow zero-trust principles

### Before Deployment
1. **All Validation Passes**: Every `make validate-*` command succeeds
2. **Security Approved**: Security scan clean
3. **Performance Verified**: Benchmarks meet targets
4. **Documentation Updated**: All changes documented
5. **Team Reviewed**: Code review approval

## 🔧 Common Development Commands

### Service Management
```bash
# Start/stop services
make dev-up                      # Start all services
make dev-down                    # Stop all services
make dev-logs SERVICE=analysis-engine  # View service logs

# Build services
make build-all                   # Build all services
make build SERVICE=marketplace   # Build specific service
```

### Testing & Quality
```bash
# Testing
make test                        # All tests
make test-service SERVICE=query-intelligence
make test-integration            # Integration tests
make test-e2e                   # End-to-end tests

# Quality
make lint                       # All linting
make fmt                        # Format code
make coverage                   # Coverage report
```

### Deployment
```bash
# Deploy to environments
make deploy ENV=development
make deploy ENV=staging SERVICE=analysis-engine
make deploy ENV=production      # Requires approval
```

## 🧠 CCL-Specific Gotchas

### Critical Implementation Details
```yaml
analysis_engine:
  - Memory usage can spike with large codebases (use streaming)
  - Language detection must precede parser selection
  - WebAssembly required for pattern plugins

query_intelligence:
  - Gemini 2.0 rate limits: 60 requests/minute in development
  - Context window: 2M tokens (chunk large codebases)
  - Confidence scores <0.7 should trigger clarification

pattern_mining:
  - Minimum 5 examples required per pattern cluster
  - Feature vectors must be cached to avoid recomputation
  - DBSCAN epsilon tuning critical for quality

marketplace:
  - Spanner transaction limit: 20,000 mutations
  - Pattern artifacts limited to 10MB
  - Stripe webhook validation required

collaboration:
  - WebSocket connections don't auto-scale
  - Firestore document limit: 1MB
  - Session conflict resolution required
```

## 📊 Performance Targets

### Service SLAs
- **Analysis Engine**: <30s for 1M LOC, 99.9% uptime
- **Query Intelligence**: <100ms response (p95), 99.95% uptime  
- **Pattern Mining**: <5min processing, 99% uptime
- **Marketplace**: <50ms response (p95), 99.99% uptime
- **Collaboration**: <50ms real-time updates, 99.9% uptime

### Scalability Requirements
- 100K+ concurrent users
- 1M+ repositories analyzed
- 1B+ queries per month
- Auto-scale 0-1000 instances per service

## 🔐 Security & Compliance

### Zero Trust Architecture
- Authenticate every request
- Authorize at service boundaries  
- Encrypt all data (transit & rest)
- Audit log everything

### Compliance Standards
- SOC2 Type II compliant
- HIPAA ready for healthcare customers
- GDPR compliant for EU users
- CCPA compliant for California users

## 🚨 Critical Rules

### NEVER DO:
1. Mix service languages (Rust only in analysis-engine, etc.)
2. Direct database access across services
3. Bypass authentication/authorization
4. Ignore rate limits for external APIs
5. Hardcode secrets or configuration
6. Skip security reviews
7. Deploy without passing all validation

### ALWAYS DO:
1. Follow strict service boundaries
2. Use Pub/Sub for service communication
3. Implement comprehensive testing
4. Add monitoring and observability
5. Use environment variables for config
6. Get security approval for changes
7. Update documentation with changes

## 📚 Key Resources

### Internal Documentation
- **Architecture**: [PRPs/architecture-patterns.md](PRPs/architecture-patterns.md)
- **Implementation**: [PRPs/implementation-guide.md](PRPs/implementation-guide.md)
- **Features**: [PRPs/feature-specifications.md](PRPs/feature-specifications.md)
- **Security**: [PRPs/security/](PRPs/security/)
- **APIs**: [PRPs/api/](PRPs/api/)

### External References
- [Google Cloud Best Practices](https://cloud.google.com/docs/enterprise/best-practices-for-enterprise-organizations)
- [Vertex AI Documentation](https://cloud.google.com/vertex-ai/docs)
- [Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Domain-Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)

## 🆘 Troubleshooting

### Common Issues
1. **Service Won't Start**: Check `make dev-logs SERVICE=<name>` for errors
2. **Authentication Failures**: Verify `gcloud auth list` and project setup
3. **Build Failures**: Run `make clean && make install-deps`
4. **Performance Issues**: Check Cloud Monitoring dashboards
5. **Test Failures**: Review test logs and update test data

### Getting Help
- **Code Issues**: Create GitHub issue with detailed reproduction steps
- **Architecture Questions**: Review PRPs or ask in team channels
- **Security Concerns**: Contact security team immediately
- **Performance Problems**: Check monitoring dashboards first

## 📈 Contributing

### Pull Request Process
1. **Create Feature Branch**: `feature/analysis-api-enhancement`
2. **Follow Commit Format**: `feat: add repository analysis caching`
3. **Pass All Checks**: Validation, tests, security scans
4. **Get Reviews**: 2 approvals required
5. **Squash Merge**: Clean commit history

### Code Review Checklist
- [ ] Follows CCL service boundaries and language restrictions
- [ ] Includes comprehensive tests (>90% coverage)
- [ ] Performance targets met
- [ ] Security implications considered
- [ ] Documentation updated
- [ ] All validation commands pass

---

**Welcome to CCL development! Use this comprehensive context to build production-ready features efficiently. Remember: CCL is an enterprise platform serving real developers - maintain the highest quality standards.**