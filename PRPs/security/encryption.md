# Encryption Implementation

name: "Encryption Implementation"
description: |
  Comprehensive encryption system for CCL platform ensuring data protection at rest, in transit, and in processing with enterprise-grade security standards.
  
  Core Principles:
  - **End-to-End Encryption**: Protect data throughout its lifecycle
  - **Zero-Knowledge Architecture**: Server cannot access user's encrypted data
  - **Key Management**: Secure key generation, rotation, and storage
  - **Compliance Ready**: Meet SOC2, HIPAA, and GDPR encryption requirements
  - **Performance Optimized**: Fast encryption without compromising security

## Goal

Implement a robust encryption system that protects all sensitive data while maintaining system performance and enabling secure data sharing and collaboration.

## Why

Encryption is essential for:
- Protecting sensitive source code and intellectual property
- Meeting enterprise security and compliance requirements
- Ensuring data privacy and confidentiality
- Preventing data breaches and unauthorized access
- Building customer trust and confidence

This provides:
- Comprehensive data protection
- Compliance with regulations
- Secure data sharing capabilities
- Enterprise-grade security
- Zero-trust architecture foundation

## What

### User-Visible Behavior
- Transparent encryption/decryption
- Secure file uploads and downloads
- Encrypted communication channels
- Key management interface
- Compliance reporting dashboard

### Technical Requirements
- [ ] AES-256-GCM encryption for data at rest
- [ ] TLS 1.3 for data in transit
- [ ] Application-level encryption for sensitive fields
- [ ] End-to-end encryption for file transfers
- [ ] Secure key management with rotation
- [ ] Hardware Security Module (HSM) integration
- [ ] Field-level encryption in databases
- [ ] Encrypted backup and disaster recovery

### Success Criteria
- [ ] All sensitive data encrypted at rest
- [ ] All communications use TLS 1.3+
- [ ] Key rotation automated every 90 days
- [ ] Zero unencrypted sensitive data exposure
- [ ] Performance impact <5% overhead

## All Needed Context

### Documentation & References
- url: https://cloud.google.com/kms/docs
  why: Google Cloud KMS integration patterns
- url: https://tools.ietf.org/html/rfc8446
  why: TLS 1.3 specification
- url: https://csrc.nist.gov/publications/detail/fips/197/final
  why: AES encryption standard
- file: docs/security/README.md
  why: Current security requirements

### Encryption Standards

```yaml
Encryption Standards:
  symmetric:
    algorithm: AES-256-GCM
    key_size: 256 bits
    iv_size: 96 bits
    tag_size: 128 bits
    
  asymmetric:
    algorithm: RSA-4096 / ECC P-384
    key_size: 4096 bits (RSA) / 384 bits (ECC)
    
  hashing:
    algorithm: SHA-256 / SHA-3
    iterations: 100000+ (PBKDF2)
    
  transport:
    protocol: TLS 1.3
    cipher_suites: 
      - TLS_AES_256_GCM_SHA384
      - TLS_CHACHA20_POLY1305_SHA256
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Never reuse IVs/nonces with same key
- **CRITICAL**: Always authenticate encrypted data
- **GOTCHA**: GCM mode vulnerable to IV reuse
- **GOTCHA**: Key derivation must use strong randomness
- **WARNING**: Timing attacks on decryption operations
- **TIP**: Use constant-time operations for crypto
- **TIP**: Implement proper key rotation strategies

## Implementation Blueprint

### Core Encryption Service

```typescript
// services/encryptionService.ts
import { randomBytes, createCipher, createDecipher } from 'crypto';
import { KMSKeyRing, KMSClient } from '@google-cloud/kms';

export interface EncryptionConfig {
  kmsProjectId: string;
  kmsLocationId: string;
  kmsKeyRingId: string;
  masterKeyId: string;
}

export interface EncryptedData {
  ciphertext: string;
  iv: string;
  tag: string;
  keyId: string;
  algorithm: string;
  version: number;
}

export interface EncryptionKey {
  id: string;
  key: Buffer;
  algorithm: string;
  createdAt: Date;
  expiresAt: Date;
  active: boolean;
}

export class EncryptionService {
  private readonly ALGORITHM = 'aes-256-gcm';
  private readonly KEY_SIZE = 32; // 256 bits
  private readonly IV_SIZE = 12;  // 96 bits
  private readonly TAG_SIZE = 16; // 128 bits
  
  constructor(
    private config: EncryptionConfig,
    private kmsClient: KMSClient,
    private keyRepository: EncryptionKeyRepository,
    private cache: CacheService
  ) {}
  
  async encrypt(data: string | Buffer, context?: string): Promise<EncryptedData> {
    if (!data || data.length === 0) {
      throw new Error('Data cannot be empty');
    }
    
    // Get current encryption key
    const encryptionKey = await this.getCurrentEncryptionKey(context);
    
    // Generate random IV
    const iv = randomBytes(this.IV_SIZE);
    
    // Create cipher
    const cipher = crypto.createCipher(this.ALGORITHM, encryptionKey.key, { ivLength: this.IV_SIZE });
    cipher.setAAD(Buffer.from(context || ''));
    
    // Encrypt data
    const chunks: Buffer[] = [];
    chunks.push(cipher.update(Buffer.isBuffer(data) ? data : Buffer.from(data, 'utf8')));
    chunks.push(cipher.final());
    
    const ciphertext = Buffer.concat(chunks);
    const tag = cipher.getAuthTag();
    
    return {
      ciphertext: ciphertext.toString('base64'),
      iv: iv.toString('base64'),
      tag: tag.toString('base64'),
      keyId: encryptionKey.id,
      algorithm: this.ALGORITHM,
      version: 1
    };
  }
  
  async decrypt(encryptedData: EncryptedData, context?: string): Promise<Buffer> {
    // Get decryption key
    const decryptionKey = await this.getEncryptionKey(encryptedData.keyId);
    if (!decryptionKey) {
      throw new Error('Encryption key not found');
    }
    
    // Prepare cipher data
    const ciphertext = Buffer.from(encryptedData.ciphertext, 'base64');
    const iv = Buffer.from(encryptedData.iv, 'base64');
    const tag = Buffer.from(encryptedData.tag, 'base64');
    
    // Create decipher
    const decipher = crypto.createDecipher(encryptedData.algorithm, decryptionKey.key, { ivLength: this.IV_SIZE });
    decipher.setAAD(Buffer.from(context || ''));
    decipher.setAuthTag(tag);
    
    try {
      // Decrypt data
      const chunks: Buffer[] = [];
      chunks.push(decipher.update(ciphertext));
      chunks.push(decipher.final());
      
      return Buffer.concat(chunks);
    } catch (error) {
      throw new Error('Decryption failed: Invalid ciphertext or key');
    }
  }
  
  async encryptField(value: any, fieldName: string, entityId: string): Promise<string> {
    if (value == null) {
      return value;
    }
    
    const context = `${fieldName}:${entityId}`;
    const plaintext = typeof value === 'string' ? value : JSON.stringify(value);
    const encrypted = await this.encrypt(plaintext, context);
    
    // Return as JSON string for database storage
    return JSON.stringify(encrypted);
  }
  
  async decryptField(encryptedValue: string, fieldName: string, entityId: string): Promise<any> {
    if (!encryptedValue) {
      return encryptedValue;
    }
    
    try {
      const encryptedData: EncryptedData = JSON.parse(encryptedValue);
      const context = `${fieldName}:${entityId}`;
      const decrypted = await this.decrypt(encryptedData, context);
      
      // Try to parse as JSON, fall back to string
      try {
        return JSON.parse(decrypted.toString('utf8'));
      } catch {
        return decrypted.toString('utf8');
      }
    } catch (error) {
      throw new Error(`Failed to decrypt field ${fieldName}: ${error.message}`);
    }
  }
  
  async encryptFile(fileBuffer: Buffer, fileName: string, userId: string): Promise<EncryptedData> {
    const context = `file:${fileName}:${userId}`;
    return this.encrypt(fileBuffer, context);
  }
  
  async decryptFile(encryptedData: EncryptedData, fileName: string, userId: string): Promise<Buffer> {
    const context = `file:${fileName}:${userId}`;
    return this.decrypt(encryptedData, context);
  }
  
  private async getCurrentEncryptionKey(context?: string): Promise<EncryptionKey> {
    const cacheKey = `current_encryption_key:${context || 'default'}`;
    
    // Try cache first
    let key = await this.cache.get<EncryptionKey>(cacheKey);
    if (key && key.active && key.expiresAt > new Date()) {
      return key;
    }
    
    // Get from repository
    key = await this.keyRepository.getCurrentKey(context);
    if (!key || !key.active || key.expiresAt <= new Date()) {
      // Generate new key
      key = await this.generateNewEncryptionKey(context);
    }
    
    // Cache for 1 hour
    await this.cache.set(cacheKey, key, 3600);
    return key;
  }
  
  private async getEncryptionKey(keyId: string): Promise<EncryptionKey | null> {
    const cacheKey = `encryption_key:${keyId}`;
    
    let key = await this.cache.get<EncryptionKey>(cacheKey);
    if (key) {
      return key;
    }
    
    key = await this.keyRepository.getKey(keyId);
    if (key) {
      // Cache for 24 hours
      await this.cache.set(cacheKey, key, 86400);
    }
    
    return key;
  }
  
  private async generateNewEncryptionKey(context?: string): Promise<EncryptionKey> {
    // Generate random key
    const keyMaterial = randomBytes(this.KEY_SIZE);
    
    // Encrypt with KMS
    const encryptedKey = await this.encryptWithKMS(keyMaterial);
    
    const key: EncryptionKey = {
      id: crypto.randomUUID(),
      key: keyMaterial,
      algorithm: this.ALGORITHM,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
      active: true
    };
    
    // Store encrypted key
    await this.keyRepository.storeKey({
      ...key,
      key: encryptedKey // Store encrypted version
    });
    
    return key;
  }
  
  private async encryptWithKMS(keyMaterial: Buffer): Promise<Buffer> {
    const keyName = this.kmsClient.cryptoKeyPath(
      this.config.kmsProjectId,
      this.config.kmsLocationId,
      this.config.kmsKeyRingId,
      this.config.masterKeyId
    );
    
    const [encryptResponse] = await this.kmsClient.encrypt({
      name: keyName,
      plaintext: keyMaterial
    });
    
    return Buffer.from(encryptResponse.ciphertext!);
  }
  
  private async decryptWithKMS(encryptedKey: Buffer): Promise<Buffer> {
    const keyName = this.kmsClient.cryptoKeyPath(
      this.config.kmsProjectId,
      this.config.kmsLocationId,
      this.config.kmsKeyRingId,
      this.config.masterKeyId
    );
    
    const [decryptResponse] = await this.kmsClient.decrypt({
      name: keyName,
      ciphertext: encryptedKey
    });
    
    return Buffer.from(decryptResponse.plaintext!);
  }
}
```

### Database Field Encryption

```typescript
// decorators/encrypted.ts
export function Encrypted(options?: { context?: string; searchable?: boolean }) {
  return function (target: any, propertyKey: string) {
    // Mark field as encrypted for ORM
    const encryptedFields = Reflect.getMetadata('encrypted_fields', target.constructor) || [];
    encryptedFields.push({
      field: propertyKey,
      context: options?.context,
      searchable: options?.searchable || false
    });
    Reflect.defineMetadata('encrypted_fields', encryptedFields, target.constructor);
  };
}

// Usage in models
export class User {
  @Column()
  id: string;
  
  @Column()
  email: string;
  
  @Column()
  @Encrypted({ context: 'pii' })
  socialSecurityNumber?: string;
  
  @Column()
  @Encrypted({ context: 'pii' })
  phoneNumber?: string;
  
  @Column()
  @Encrypted({ searchable: true })
  fullName: string;
}

// ORM Integration
export class EncryptedRepository<T> {
  constructor(
    private baseRepository: Repository<T>,
    private encryptionService: EncryptionService
  ) {}
  
  async save(entity: T): Promise<T> {
    const encryptedEntity = await this.encryptFields(entity);
    const saved = await this.baseRepository.save(encryptedEntity);
    return this.decryptFields(saved);
  }
  
  async findOne(criteria: any): Promise<T | null> {
    const found = await this.baseRepository.findOne(criteria);
    if (!found) return null;
    return this.decryptFields(found);
  }
  
  async find(criteria: any): Promise<T[]> {
    const found = await this.baseRepository.find(criteria);
    return Promise.all(found.map(entity => this.decryptFields(entity)));
  }
  
  private async encryptFields(entity: T): Promise<T> {
    const encryptedFields = Reflect.getMetadata('encrypted_fields', entity.constructor) || [];
    const encrypted = { ...entity };
    
    for (const fieldConfig of encryptedFields) {
      const value = (entity as any)[fieldConfig.field];
      if (value != null) {
        const entityId = (entity as any).id || 'new';
        (encrypted as any)[fieldConfig.field] = await this.encryptionService.encryptField(
          value,
          fieldConfig.field,
          entityId
        );
      }
    }
    
    return encrypted;
  }
  
  private async decryptFields(entity: T): Promise<T> {
    const encryptedFields = Reflect.getMetadata('encrypted_fields', entity.constructor) || [];
    const decrypted = { ...entity };
    
    for (const fieldConfig of encryptedFields) {
      const encryptedValue = (entity as any)[fieldConfig.field];
      if (encryptedValue != null) {
        const entityId = (entity as any).id;
        (decrypted as any)[fieldConfig.field] = await this.encryptionService.decryptField(
          encryptedValue,
          fieldConfig.field,
          entityId
        );
      }
    }
    
    return decrypted;
  }
}
```

### File Encryption Service

```typescript
// services/fileEncryptionService.ts
export interface EncryptedFileMetadata {
  originalName: string;
  mimeType: string;
  size: number;
  encryptedSize: number;
  checksum: string;
  encryptionKeyId: string;
  uploadedBy: string;
  uploadedAt: Date;
}

export class FileEncryptionService {
  constructor(
    private encryptionService: EncryptionService,
    private storageService: StorageService,
    private metadataRepository: FileMetadataRepository
  ) {}
  
  async uploadEncryptedFile(
    file: Express.Multer.File,
    userId: string,
    options?: { folder?: string; public?: boolean }
  ): Promise<{ fileId: string; metadata: EncryptedFileMetadata }> {
    
    // Calculate checksum of original file
    const checksum = crypto.createHash('sha256').update(file.buffer).digest('hex');
    
    // Encrypt file
    const encryptedData = await this.encryptionService.encryptFile(
      file.buffer,
      file.originalname,
      userId
    );
    
    // Generate unique file ID
    const fileId = crypto.randomUUID();
    
    // Store encrypted file
    const storagePath = this.generateStoragePath(fileId, options?.folder);
    await this.storageService.uploadFile(storagePath, Buffer.from(encryptedData.ciphertext, 'base64'));
    
    // Store metadata
    const metadata: EncryptedFileMetadata = {
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      encryptedSize: Buffer.from(encryptedData.ciphertext, 'base64').length,
      checksum,
      encryptionKeyId: encryptedData.keyId,
      uploadedBy: userId,
      uploadedAt: new Date()
    };
    
    await this.metadataRepository.store(fileId, {
      ...metadata,
      encryptionData: encryptedData,
      storagePath,
      public: options?.public || false
    });
    
    return { fileId, metadata };
  }
  
  async downloadEncryptedFile(
    fileId: string,
    userId: string
  ): Promise<{ buffer: Buffer; metadata: EncryptedFileMetadata }> {
    
    // Get file metadata
    const fileRecord = await this.metadataRepository.get(fileId);
    if (!fileRecord) {
      throw new Error('File not found');
    }
    
    // Check permissions
    if (!fileRecord.public && fileRecord.uploadedBy !== userId) {
      // Check if user has access to this file
      const hasAccess = await this.checkFileAccess(fileId, userId);
      if (!hasAccess) {
        throw new Error('Access denied');
      }
    }
    
    // Download encrypted file
    const encryptedBuffer = await this.storageService.downloadFile(fileRecord.storagePath);
    
    // Decrypt file
    const decryptedBuffer = await this.encryptionService.decryptFile(
      {
        ...fileRecord.encryptionData,
        ciphertext: encryptedBuffer.toString('base64')
      },
      fileRecord.originalName,
      fileRecord.uploadedBy
    );
    
    // Verify checksum
    const checksum = crypto.createHash('sha256').update(decryptedBuffer).digest('hex');
    if (checksum !== fileRecord.checksum) {
      throw new Error('File integrity check failed');
    }
    
    return {
      buffer: decryptedBuffer,
      metadata: {
        originalName: fileRecord.originalName,
        mimeType: fileRecord.mimeType,
        size: fileRecord.size,
        encryptedSize: fileRecord.encryptedSize,
        checksum: fileRecord.checksum,
        encryptionKeyId: fileRecord.encryptionKeyId,
        uploadedBy: fileRecord.uploadedBy,
        uploadedAt: fileRecord.uploadedAt
      }
    };
  }
  
  async shareEncryptedFile(
    fileId: string,
    ownerId: string,
    shareWithUserId: string,
    permissions: 'read' | 'write' = 'read'
  ): Promise<void> {
    
    const fileRecord = await this.metadataRepository.get(fileId);
    if (!fileRecord || fileRecord.uploadedBy !== ownerId) {
      throw new Error('File not found or access denied');
    }
    
    // Create file share record
    await this.metadataRepository.createShare({
      fileId,
      sharedBy: ownerId,
      sharedWith: shareWithUserId,
      permissions,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
    });
  }
  
  private generateStoragePath(fileId: string, folder?: string): string {
    const prefix = folder ? `${folder}/` : '';
    // Use first 2 chars of fileId for sharding
    const shard = fileId.substring(0, 2);
    return `${prefix}encrypted/${shard}/${fileId}`;
  }
  
  private async checkFileAccess(fileId: string, userId: string): Promise<boolean> {
    const share = await this.metadataRepository.getShare(fileId, userId);
    return share && (!share.expiresAt || share.expiresAt > new Date());
  }
}
```

### Key Rotation Service

```typescript
// services/keyRotationService.ts
export class KeyRotationService {
  constructor(
    private encryptionService: EncryptionService,
    private keyRepository: EncryptionKeyRepository,
    private auditLogger: AuditLogger
  ) {}
  
  async rotateKeys(): Promise<void> {
    console.log('Starting key rotation process...');
    
    try {
      // Find keys that need rotation (older than 90 days or expiring soon)
      const keysToRotate = await this.keyRepository.getKeysForRotation();
      
      for (const oldKey of keysToRotate) {
        await this.rotateKey(oldKey);
      }
      
      await this.auditLogger.log({
        event: 'key_rotation_completed',
        keysRotated: keysToRotate.length,
        timestamp: new Date()
      });
      
      console.log(`Key rotation completed. Rotated ${keysToRotate.length} keys.`);
      
    } catch (error) {
      await this.auditLogger.log({
        event: 'key_rotation_failed',
        error: error.message,
        timestamp: new Date()
      });
      
      throw error;
    }
  }
  
  private async rotateKey(oldKey: EncryptionKey): Promise<void> {
    console.log(`Rotating key ${oldKey.id}...`);
    
    // Generate new key
    const newKey = await this.generateNewKey(oldKey);
    
    // Mark old key as inactive (but keep for decryption)
    await this.keyRepository.updateKey(oldKey.id, {
      active: false,
      rotatedAt: new Date(),
      replacedBy: newKey.id
    });
    
    // Set new key as active
    await this.keyRepository.updateKey(newKey.id, {
      active: true
    });
    
    await this.auditLogger.log({
      event: 'key_rotated',
      oldKeyId: oldKey.id,
      newKeyId: newKey.id,
      timestamp: new Date()
    });
  }
  
  private async generateNewKey(oldKey: EncryptionKey): Promise<EncryptionKey> {
    const keyMaterial = randomBytes(32); // 256 bits
    
    const newKey: EncryptionKey = {
      id: crypto.randomUUID(),
      key: keyMaterial,
      algorithm: oldKey.algorithm,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
      active: false // Will be set to true after old key is deactivated
    };
    
    await this.keyRepository.storeKey(newKey);
    return newKey;
  }
  
  async scheduleKeyRotation(): Promise<void> {
    // Schedule rotation every 30 days
    setInterval(async () => {
      try {
        await this.rotateKeys();
      } catch (error) {
        console.error('Scheduled key rotation failed:', error);
      }
    }, 30 * 24 * 60 * 60 * 1000); // 30 days
  }
}
```

### Transport Layer Security

```typescript
// config/tlsConfig.ts
export interface TLSConfig {
  minVersion: string;
  maxVersion: string;
  cipherSuites: string[];
  honorCipherOrder: boolean;
  sessionTimeout: number;
}

export const tlsConfig: TLSConfig = {
  minVersion: 'TLSv1.3',
  maxVersion: 'TLSv1.3',
  cipherSuites: [
    'TLS_AES_256_GCM_SHA384',
    'TLS_CHACHA20_POLY1305_SHA256',
    'TLS_AES_128_GCM_SHA256'
  ],
  honorCipherOrder: true,
  sessionTimeout: 3600 // 1 hour
};

// Express HTTPS configuration
export function createSecureServer(app: Express): https.Server {
  const options: https.ServerOptions = {
    cert: fs.readFileSync(process.env.TLS_CERT_PATH!),
    key: fs.readFileSync(process.env.TLS_KEY_PATH!),
    
    // TLS configuration
    secureProtocol: 'TLS_method',
    minVersion: 'TLSv1.3',
    maxVersion: 'TLSv1.3',
    honorCipherOrder: true,
    
    // Cipher suites
    ciphers: tlsConfig.cipherSuites.join(':'),
    
    // Session settings
    sessionTimeout: tlsConfig.sessionTimeout,
    
    // Security headers
    secureOptions: crypto.constants.SSL_OP_NO_SSLv3 | 
                   crypto.constants.SSL_OP_NO_TLSv1 |
                   crypto.constants.SSL_OP_NO_TLSv1_1 |
                   crypto.constants.SSL_OP_NO_TLSv1_2
  };
  
  return https.createServer(options, app);
}

// Security headers middleware
export function securityHeaders(req: Request, res: Response, next: NextFunction) {
  // HSTS
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  
  // CSP
  res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
  
  // Other security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  next();
}
```

### End-to-End Encryption for Real-Time Communication

```typescript
// services/e2eEncryptionService.ts
export class E2EEncryptionService {
  private readonly ALGORITHM = 'aes-256-gcm';
  
  async generateKeyPair(): Promise<{ publicKey: string; privateKey: string }> {
    const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 4096,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });
    
    return { publicKey, privateKey };
  }
  
  async generateSharedSecret(privateKey: string, publicKey: string): Promise<Buffer> {
    // Use ECDH for key exchange
    const ecdh = crypto.createECDH('secp384r1');
    ecdh.setPrivateKey(privateKey, 'pem');
    
    const sharedSecret = ecdh.computeSecret(publicKey, 'pem');
    
    // Derive encryption key using HKDF
    return crypto.hkdfSync('sha256', sharedSecret, '', '', 32);
  }
  
  async encryptMessage(message: string, sharedSecret: Buffer): Promise<{
    ciphertext: string;
    iv: string;
    tag: string;
  }> {
    const iv = randomBytes(12);
    const cipher = crypto.createCipher(this.ALGORITHM, sharedSecret, { ivLength: 12 });
    
    let ciphertext = cipher.update(message, 'utf8', 'base64');
    ciphertext += cipher.final('base64');
    
    const tag = cipher.getAuthTag();
    
    return {
      ciphertext,
      iv: iv.toString('base64'),
      tag: tag.toString('base64')
    };
  }
  
  async decryptMessage(
    encryptedMessage: { ciphertext: string; iv: string; tag: string },
    sharedSecret: Buffer
  ): Promise<string> {
    const decipher = crypto.createDecipher(
      this.ALGORITHM,
      sharedSecret,
      { ivLength: 12 }
    );
    
    decipher.setAuthTag(Buffer.from(encryptedMessage.tag, 'base64'));
    
    let plaintext = decipher.update(encryptedMessage.ciphertext, 'base64', 'utf8');
    plaintext += decipher.final('utf8');
    
    return plaintext;
  }
}

// WebSocket encryption middleware
export function encryptWebSocketMessages(socket: WebSocket, e2eService: E2EEncryptionService) {
  const originalSend = socket.send;
  
  socket.send = function(data: any) {
    if (socket.sharedSecret) {
      // Encrypt outgoing messages
      const encrypted = e2eService.encryptMessage(JSON.stringify(data), socket.sharedSecret);
      originalSend.call(this, JSON.stringify({
        type: 'encrypted',
        data: encrypted
      }));
    } else {
      originalSend.call(this, data);
    }
  };
  
  socket.on('message', async (data: string) => {
    try {
      const message = JSON.parse(data);
      
      if (message.type === 'encrypted' && socket.sharedSecret) {
        // Decrypt incoming messages
        const decrypted = await e2eService.decryptMessage(message.data, socket.sharedSecret);
        socket.emit('decrypted-message', JSON.parse(decrypted));
      } else {
        socket.emit('message', message);
      }
    } catch (error) {
      console.error('Message decryption failed:', error);
    }
  });
}
```

## Validation Loop

### Level 1: Encryption Testing
```typescript
// Test encryption functionality
describe('Encryption Service', () => {
  test('encrypts and decrypts data correctly', async () => {
    const plaintext = 'sensitive data';
    const context = 'test-context';
    
    const encrypted = await encryptionService.encrypt(plaintext, context);
    expect(encrypted.ciphertext).toBeDefined();
    expect(encrypted.iv).toBeDefined();
    expect(encrypted.tag).toBeDefined();
    
    const decrypted = await encryptionService.decrypt(encrypted, context);
    expect(decrypted.toString('utf8')).toBe(plaintext);
  });
  
  test('fails decryption with wrong context', async () => {
    const plaintext = 'sensitive data';
    const encrypted = await encryptionService.encrypt(plaintext, 'correct-context');
    
    await expect(
      encryptionService.decrypt(encrypted, 'wrong-context')
    ).rejects.toThrow('Decryption failed');
  });
  
  test('field encryption works transparently', async () => {
    const user = {
      id: 'user-123',
      email: '<EMAIL>',
      socialSecurityNumber: '***********'
    };
    
    const encrypted = await userRepository.save(user);
    expect(encrypted.socialSecurityNumber).not.toBe('***********');
    
    const retrieved = await userRepository.findOne({ id: 'user-123' });
    expect(retrieved.socialSecurityNumber).toBe('***********');
  });
});
```

### Level 2: Key Management Testing
```typescript
// Test key rotation
describe('Key Rotation', () => {
  test('rotates keys automatically', async () => {
    const oldKey = await keyRepository.getCurrentKey();
    
    await keyRotationService.rotateKeys();
    
    const newKey = await keyRepository.getCurrentKey();
    expect(newKey.id).not.toBe(oldKey.id);
    expect(newKey.active).toBe(true);
    
    const oldKeyAfterRotation = await keyRepository.getKey(oldKey.id);
    expect(oldKeyAfterRotation.active).toBe(false);
  });
  
  test('can decrypt with old keys after rotation', async () => {
    const plaintext = 'test data';
    const encrypted = await encryptionService.encrypt(plaintext);
    
    // Rotate keys
    await keyRotationService.rotateKeys();
    
    // Should still be able to decrypt with old key
    const decrypted = await encryptionService.decrypt(encrypted);
    expect(decrypted.toString('utf8')).toBe(plaintext);
  });
});
```

### Level 3: Performance Testing
```typescript
// Test encryption performance
describe('Encryption Performance', () => {
  test('encryption performance meets requirements', async () => {
    const data = 'x'.repeat(1024); // 1KB of data
    const iterations = 1000;
    
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      await encryptionService.encrypt(data);
    }
    
    const duration = Date.now() - startTime;
    const avgTime = duration / iterations;
    
    expect(avgTime).toBeLessThan(5); // Less than 5ms per encryption
  });
  
  test('file encryption handles large files', async () => {
    const largeFile = Buffer.alloc(10 * 1024 * 1024); // 10MB
    
    const startTime = Date.now();
    const encrypted = await encryptionService.encryptFile(largeFile, 'test.bin', 'user-123');
    const encryptionTime = Date.now() - startTime;
    
    expect(encryptionTime).toBeLessThan(1000); // Less than 1 second
    expect(encrypted.ciphertext).toBeDefined();
  });
});
```

## Final Validation Checklist

- [ ] All sensitive data encrypted at rest
- [ ] TLS 1.3 configured for transport security
- [ ] Application-level encryption implemented
- [ ] Key management system operational
- [ ] Key rotation automated
- [ ] File encryption/decryption working
- [ ] End-to-end encryption for real-time communication
- [ ] Performance requirements met
- [ ] Security testing passed
- [ ] Compliance requirements satisfied

## Anti-Patterns to Avoid

1. **DON'T reuse IVs/nonces** - Breaks encryption security
2. **DON'T hardcode encryption keys** - Use secure key management
3. **DON'T skip authentication** - Always verify encrypted data integrity
4. **DON'T use weak algorithms** - Stick to AES-256-GCM and RSA-4096
5. **DON'T ignore key rotation** - Keys must be rotated regularly
6. **DON'T store keys with data** - Separate key storage from data storage
7. **DON'T decrypt unnecessarily** - Only decrypt when needed for processing
8. **DON'T log sensitive data** - Encrypted data should not appear in logs
9. **DON'T trust client-side encryption** - Always validate server-side
10. **DON'T implement custom crypto** - Use established libraries and standards