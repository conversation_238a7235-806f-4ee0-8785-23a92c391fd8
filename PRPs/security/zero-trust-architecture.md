# Zero Trust Architecture

name: "Zero Trust Architecture"
description: |
  Comprehensive Zero Trust security implementation for the CCL platform with advanced threat detection, policy enforcement, and continuous verification.
  
  Core Principles:
  - **Never Trust, Always Verify**: Every request authenticated and authorized
  - **Enforce Least Privilege**: Minimal access rights with continuous validation
  - **Assume Breach**: Design for containment and rapid response
  - **Continuous Verification**: Real-time risk assessment and policy enforcement
  - **Device Trust**: Comprehensive device security and compliance validation

## Goal

Implement a production-ready Zero Trust security architecture that provides comprehensive protection against modern threats while maintaining usability and performance for the CCL platform.

## Why

Zero Trust architecture enables:
- Protection against insider threats and lateral movement
- Compliance with enterprise security requirements
- Reduced attack surface and blast radius
- Granular access control and audit capabilities
- Continuous security posture assessment

This provides:
- Enterprise-grade security for all customers
- Compliance with SOC2, HIPAA, and FedRAMP requirements
- Real-time threat detection and response
- Comprehensive audit trails for security investigations
- Scalable security controls that grow with the platform

## What

### User-Visible Behavior
- Seamless multi-factor authentication
- Device compliance validation
- Contextual access decisions
- Security notifications and alerts
- Granular permission management

### Technical Requirements
- [ ] VPC Service Controls perimeter
- [ ] Identity-based network segmentation
- [ ] Device trust and compliance validation
- [ ] Continuous risk assessment engine
- [ ] Policy-based access control
- [ ] Real-time threat detection
- [ ] Encrypted communication channels
- [ ] Comprehensive audit logging

### Success Criteria
- [ ] 100% of traffic authenticated and authorized
- [ ] Zero lateral movement capability
- [ ] <100ms access decision latency
- [ ] 99.9% uptime for security services
- [ ] Complete audit trail coverage

## All Needed Context

### Documentation & References
- url: https://cloud.google.com/security/zero-trust
  why: Google Cloud Zero Trust implementation guide
- url: https://www.nist.gov/publications/zero-trust-architecture
  why: NIST Zero Trust Architecture standard
- url: https://cloud.google.com/vpc-service-controls/docs
  why: VPC Service Controls implementation
- file: prp_docs/ccl-security-compliance.md
  why: Complete security architecture specification

### Zero Trust Principles

```yaml
Core Principles:
  never_trust_always_verify:
    - Authenticate every user and device
    - Authorize every access request
    - Validate security posture continuously
    - Apply policies consistently
    
  least_privilege_access:
    - Grant minimum required permissions
    - Time-bound access grants
    - Just-in-time privilege elevation
    - Regular access reviews
    
  assume_breach:
    - Design for containment
    - Limit blast radius
    - Enable rapid detection
    - Facilitate quick response
    
  continuous_verification:
    - Real-time risk assessment
    - Dynamic policy enforcement
    - Behavioral analysis
    - Anomaly detection
```

### Known Gotchas & Library Quirks
- **CRITICAL**: VPC Service Controls can block legitimate API calls if misconfigured
- **CRITICAL**: Device compliance policies must account for developer workflows
- **GOTCHA**: Network policies can conflict with auto-scaling requirements
- **GOTCHA**: Certificate-based authentication requires robust PKI management
- **WARNING**: Performance impact of real-time policy evaluation
- **TIP**: Use policy-as-code for consistent enforcement
- **TIP**: Implement gradual rollout for new security policies
- **TIP**: Monitor user experience metrics during security changes

## Implementation Blueprint

### Zero Trust Policy Engine

```typescript
// services/zeroTrustPolicyEngine.ts
export interface ZeroTrustPolicy {
  id: string;
  name: string;
  version: string;
  scope: {
    services: string[];
    resources: string[];
    users: string[];
    networks: string[];
  };
  rules: ZeroTrustRule[];
  enforcement: 'audit' | 'enforce' | 'block';
  priority: number;
  createdAt: Date;
  updatedAt: Date;
  activatedAt?: Date;
  deactivatedAt?: Date;
}

export interface ZeroTrustRule {
  id: string;
  type: 'identity' | 'device' | 'network' | 'application' | 'data';
  conditions: ZeroTrustCondition[];
  actions: ZeroTrustAction[];
  risk_score_threshold: number;
  continuous_verification: boolean;
}

export interface ZeroTrustCondition {
  attribute: string; // user.role, device.compliance_status, network.location
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'contains' | 'gt' | 'lt';
  value: any;
  weight: number; // For risk calculation
}

export interface ZeroTrustAction {
  type: 'allow' | 'deny' | 'challenge' | 'step_up_auth' | 'quarantine' | 'audit';
  parameters: Record<string, any>;
  notification_required: boolean;
}

export class ZeroTrustPolicyEngine {
  constructor(
    private policyRepository: PolicyRepository,
    private deviceService: DeviceService,
    private identityService: IdentityService,
    private networkService: NetworkService,
    private threatIntelligence: ThreatIntelligenceService,
    private auditLogger: AuditLogger
  ) {}

  async evaluateAccess(request: AccessRequest): Promise<AccessDecision> {
    const startTime = Date.now();
    
    // Step 1: Identity verification and risk assessment
    const identityScore = await this.evaluateIdentityRisk(request.identity);
    
    // Step 2: Device trust evaluation
    const deviceScore = await this.evaluateDeviceRisk(request.device);
    
    // Step 3: Network context analysis
    const networkScore = await this.evaluateNetworkRisk(request.network);
    
    // Step 4: Application context evaluation
    const applicationScore = await this.evaluateApplicationRisk(request.application);
    
    // Step 5: Data sensitivity assessment
    const dataScore = await this.evaluateDataRisk(request.resource);
    
    // Step 6: Real-time threat intelligence
    const threatScore = await this.evaluateThreatRisk(request);
    
    // Step 7: Composite risk calculation
    const riskScore = this.calculateCompositeRisk({
      identity: identityScore,
      device: deviceScore,
      network: networkScore,
      application: applicationScore,
      data: dataScore,
      threat: threatScore
    });
    
    // Step 8: Policy evaluation
    const applicablePolicies = await this.getApplicablePolicies(request);
    const decision = await this.evaluatePolicies(applicablePolicies, request, riskScore);
    
    // Step 9: Continuous verification setup
    if (decision.granted) {
      await this.setupContinuousVerification(request, decision);
    }
    
    // Step 10: Audit logging
    await this.auditLogger.log({
      event: 'zero_trust_access_evaluation',
      requestId: request.id,
      identity: request.identity.id,
      resource: request.resource.id,
      decision: decision.granted ? 'allow' : 'deny',
      riskScore,
      policies: applicablePolicies.map(p => p.id),
      processingTimeMs: Date.now() - startTime,
      metadata: {
        scores: { identityScore, deviceScore, networkScore, applicationScore, dataScore, threatScore },
        challenge_required: decision.challengeRequired,
        step_up_auth_required: decision.stepUpAuthRequired
      }
    });
    
    return decision;
  }

  private async evaluateIdentityRisk(identity: Identity): Promise<number> {
    let riskScore = 0;
    
    // Base identity verification
    if (!identity.verified) riskScore += 40;
    if (!identity.mfaEnabled) riskScore += 30;
    
    // Historical behavior analysis
    const behaviorMetrics = await this.identityService.getBehaviorMetrics(identity.id);
    
    // Unusual login patterns
    if (behaviorMetrics.unusualLoginTime) riskScore += 15;
    if (behaviorMetrics.unusualLocation) riskScore += 25;
    if (behaviorMetrics.velocityAnomaly) riskScore += 20;
    
    // Recent security events
    const recentIncidents = await this.identityService.getRecentSecurityIncidents(identity.id);
    riskScore += recentIncidents.length * 10;
    
    // Account age and activity
    const accountAge = Date.now() - identity.createdAt.getTime();
    if (accountAge < 7 * 24 * 60 * 60 * 1000) riskScore += 20; // Less than 7 days old
    
    // Privilege escalation patterns
    const privilegeChanges = await this.identityService.getRecentPrivilegeChanges(identity.id);
    if (privilegeChanges.length > 0) riskScore += 15;
    
    return Math.min(riskScore, 100);
  }

  private async evaluateDeviceRisk(device: Device): Promise<number> {
    let riskScore = 0;
    
    // Device trust status
    if (!device.managed) riskScore += 30;
    if (!device.encrypted) riskScore += 25;
    if (!device.compliancePolicyMet) riskScore += 35;
    
    // Device security posture
    if (!device.antivirusActive) riskScore += 20;
    if (!device.firewallEnabled) riskScore += 15;
    if (device.jailbroken || device.rooted) riskScore += 50;
    
    // Certificate and registration status
    if (!device.certificateValid) riskScore += 40;
    if (!device.registeredInMDM) riskScore += 30;
    
    // Recent device changes
    const recentChanges = await this.deviceService.getRecentChanges(device.id);
    if (recentChanges.osDowngrade) riskScore += 25;
    if (recentChanges.securitySettingsChanged) riskScore += 20;
    
    // Geolocation anomalies
    const locationHistory = await this.deviceService.getLocationHistory(device.id);
    if (this.detectLocationAnomaly(locationHistory)) riskScore += 30;
    
    return Math.min(riskScore, 100);
  }
}

```

### VPC Service Controls Implementation

```yaml
# terraform/vpc-service-controls.tf
resource "google_access_context_manager_service_perimeter" "ccl_perimeter" {
  parent = "accessPolicies/${google_access_context_manager_access_policy.policy.name}"
  name   = "accessPolicies/${google_access_context_manager_access_policy.policy.name}/servicePerimeters/ccl_security_perimeter"
  title  = "CCL Security Perimeter"
  
  status {
    restricted_services = [
      "storage.googleapis.com",
      "spanner.googleapis.com",
      "bigquery.googleapis.com",
      "aiplatform.googleapis.com",
      "firestore.googleapis.com",
      "redis.googleapis.com",
      "secretmanager.googleapis.com"
    ]
    
    resources = [
      "projects/${var.project_id}",
      "projects/${var.analytics_project_id}",
      "projects/${var.security_project_id}"
    ]
    
    access_levels = [
      google_access_context_manager_access_level.ccl_internal.name,
      google_access_context_manager_access_level.ccl_developer.name,
      google_access_context_manager_access_level.ccl_admin.name
    ]
    
    vpc_accessible_services {
      enable_restriction = true
      allowed_services = [
        "storage.googleapis.com",
        "spanner.googleapis.com",
        "bigquery.googleapis.com",
        "aiplatform.googleapis.com"
      ]
    }
    
    ingress_policies {
      ingress_from {
        sources {
          access_level = google_access_context_manager_access_level.ccl_internal.name
        }
        identity_type = "ANY_IDENTITY"
      }
      
      ingress_to {
        resources = ["*"]
        operations {
          service_name = "storage.googleapis.com"
          method_selectors {
            method = "*"
          }
        }
      }
    }
    
    egress_policies {
      egress_from {
        identity_type = "ANY_USER_ACCOUNT"
      }
      
      egress_to {
        resources = ["*"]
        operations {
          service_name = "aiplatform.googleapis.com"
          method_selectors {
            method = "google.cloud.aiplatform.v1.PredictionService.*"
          }
        }
      }
    }
  }
  
  perimeter_type = "PERIMETER_TYPE_REGULAR"
}

resource "google_access_context_manager_access_level" "ccl_internal" {
  parent = "accessPolicies/${google_access_context_manager_access_policy.policy.name}"
  name   = "accessPolicies/${google_access_context_manager_access_policy.policy.name}/accessLevels/ccl_internal_access"
  title  = "CCL Internal Access"
  
  basic {
    conditions {
      ip_subnetworks = ["10.0.0.0/16", "**********/12"]
      members = [
        "serviceAccount:ccl-analysis-sa@${var.project_id}.iam.gserviceaccount.com",
        "serviceAccount:ccl-query-sa@${var.project_id}.iam.gserviceaccount.com",
        "serviceAccount:ccl-marketplace-sa@${var.project_id}.iam.gserviceaccount.com"
      ]
    }
  }
}

resource "google_access_context_manager_access_level" "ccl_developer" {
  parent = "accessPolicies/${google_access_context_manager_access_policy.policy.name}"
  name   = "accessPolicies/${google_access_context_manager_access_policy.policy.name}/accessLevels/ccl_developer_access"
  title  = "CCL Developer Access"
  
  basic {
    conditions {
      device_policy {
        require_screen_lock              = true
        require_admin_approval           = true
        require_corp_owned               = true
        allowed_encryption_statuses      = ["ENCRYPTED"]
        allowed_device_management_levels = ["MANAGED"]
        
        os_constraints {
          os_type                    = "DESKTOP_MAC"
          minimum_version           = "10.15"
          require_verified_chrome_os = false
        }
        
        os_constraints {
          os_type                    = "DESKTOP_WINDOWS"
          minimum_version           = "10.0.19041"
          require_verified_chrome_os = false
        }
        
        os_constraints {
          os_type                    = "DESKTOP_LINUX"
          minimum_version           = "18.04"
          require_verified_chrome_os = false
        }
      }
      
      regions = ["US", "EU", "CA"]
    }
  }
}
```

### Network Segmentation

```typescript
// services/networkSegmentation.ts
export interface NetworkSegment {
  id: string;
  name: string;
  type: 'dmz' | 'service' | 'data' | 'management';
  subnet: string;
  allowedServices: string[];
  securityPolicies: string[];
  monitoring: boolean;
}

export class NetworkSegmentationService {
  private segments: Map<string, NetworkSegment> = new Map();
  
  constructor(
    private firewallService: FirewallService,
    private monitoringService: MonitoringService
  ) {
    this.initializeSegments();
  }
  
  private initializeSegments() {
    // DMZ Segment - External facing services
    this.segments.set('dmz', {
      id: 'dmz',
      name: 'DMZ Segment',
      type: 'dmz',
      subnet: '********/24',
      allowedServices: ['apigee-gateway', 'load-balancer'],
      securityPolicies: ['external-access-policy', 'ddos-protection'],
      monitoring: true
    });
    
    // Service Segment - Application services
    this.segments.set('services', {
      id: 'services',
      name: 'Services Segment',
      type: 'service',
      subnet: '10.0.2.0/24',
      allowedServices: [
        'ccl-analysis-engine',
        'ccl-query-intelligence',
        'ccl-pattern-mining',
        'ccl-marketplace',
        'ccl-collaboration'
      ],
      securityPolicies: ['service-access-policy', 'lateral-movement-prevention'],
      monitoring: true
    });
    
    // Data Segment - Database and storage services
    this.segments.set('data', {
      id: 'data',
      name: 'Data Segment',
      type: 'data',
      subnet: '10.0.3.0/24',
      allowedServices: ['spanner', 'bigquery', 'firestore', 'cloud-storage'],
      securityPolicies: ['data-access-policy', 'encryption-enforcement'],
      monitoring: true
    });
    
    // Management Segment - Admin and monitoring
    this.segments.set('management', {
      id: 'management',
      name: 'Management Segment',
      type: 'management',
      subnet: '********/24',
      allowedServices: ['monitoring', 'logging', 'secret-manager'],
      securityPolicies: ['admin-access-policy', 'privileged-access-management'],
      monitoring: true
    });
  }
  
  async validateNetworkAccess(request: NetworkAccessRequest): Promise<boolean> {
    const sourceSegment = this.getSegmentByIP(request.sourceIP);
    const targetSegment = this.getSegmentByIP(request.targetIP);
    
    if (!sourceSegment || !targetSegment) {
      return false;
    }
    
    // Check if cross-segment access is allowed
    const isAllowed = await this.evaluateSegmentPolicy(
      sourceSegment,
      targetSegment,
      request.service,
      request.port
    );
    
    // Log all network access attempts
    await this.monitoringService.logNetworkAccess({
      sourceSegment: sourceSegment.id,
      targetSegment: targetSegment.id,
      service: request.service,
      allowed: isAllowed,
      timestamp: new Date()
    });
    
    return isAllowed;
  }
}
```

### Device Trust Management

```typescript
// services/deviceTrustService.ts
export interface DeviceProfile {
  deviceId: string;
  userId: string;
  deviceType: 'laptop' | 'desktop' | 'mobile' | 'tablet';
  operatingSystem: string;
  osVersion: string;
  manufacturer: string;
  model: string;
  serialNumber: string;
  lastSeen: Date;
  trustScore: number;
  complianceStatus: DeviceComplianceStatus;
  certificates: DeviceCertificate[];
  securityFeatures: SecurityFeature[];
}

export interface DeviceComplianceStatus {
  overall: 'compliant' | 'non_compliant' | 'unknown';
  encryption: boolean;
  antivirus: boolean;
  firewall: boolean;
  osUpdated: boolean;
  screenLock: boolean;
  jailbroken: boolean;
  managedByMDM: boolean;
  lastComplianceCheck: Date;
  violations: ComplianceViolation[];
}

export class DeviceTrustService {
  constructor(
    private deviceRepository: DeviceRepository,
    private certificateService: CertificateService,
    private complianceService: ComplianceService,
    private auditLogger: AuditLogger
  ) {}
  
  async evaluateDeviceTrust(deviceId: string): Promise<DeviceTrustResult> {
    const device = await this.deviceRepository.findById(deviceId);
    if (!device) {
      throw new Error(`Device ${deviceId} not found`);
    }
    
    // Check device certificate validity
    const certificateValid = await this.validateDeviceCertificates(device);
    
    // Evaluate compliance status
    const complianceStatus = await this.evaluateCompliance(device);
    
    // Calculate trust score
    const trustScore = this.calculateTrustScore(device, certificateValid, complianceStatus);
    
    // Check for anomalies
    const anomalies = await this.detectAnomalies(device);
    
    const result: DeviceTrustResult = {
      deviceId: device.deviceId,
      trustScore,
      certificateValid,
      compliant: complianceStatus.overall === 'compliant',
      anomalies,
      recommendedActions: this.generateRecommendations(device, complianceStatus, anomalies),
      lastEvaluated: new Date()
    };
    
    // Update device trust score
    await this.deviceRepository.updateTrustScore(deviceId, trustScore);
    
    // Log trust evaluation
    await this.auditLogger.log({
      event: 'device_trust_evaluation',
      deviceId,
      trustScore,
      compliant: result.compliant,
      anomaliesCount: anomalies.length
    });
    
    return result;
  }
  
  private calculateTrustScore(
    device: DeviceProfile,
    certificateValid: boolean,
    compliance: DeviceComplianceStatus
  ): number {
    let score = 100;
    
    // Certificate validation
    if (!certificateValid) score -= 30;
    
    // Compliance factors
    if (!compliance.encryption) score -= 20;
    if (!compliance.antivirus) score -= 15;
    if (!compliance.firewall) score -= 10;
    if (!compliance.osUpdated) score -= 15;
    if (!compliance.screenLock) score -= 10;
    if (compliance.jailbroken) score -= 40;
    if (!compliance.managedByMDM) score -= 20;
    
    // Device age factor
    const deviceAge = Date.now() - device.lastSeen.getTime();
    const daysOld = deviceAge / (1000 * 60 * 60 * 24);
    if (daysOld > 90) score -= 10;
    if (daysOld > 365) score -= 20;
    
    // Compliance violations
    score -= compliance.violations.length * 5;
    
    return Math.max(0, Math.min(100, score));
  }
  
  async enforceDevicePolicy(deviceId: string, policyType: DevicePolicyType): Promise<void> {
    const device = await this.deviceRepository.findById(deviceId);
    if (!device) {
      throw new Error(`Device ${deviceId} not found`);
    }
    
    switch (policyType) {
      case 'quarantine':
        await this.quarantineDevice(device);
        break;
      case 'require_update':
        await this.requireSecurityUpdate(device);
        break;
      case 'revoke_certificates':
        await this.revokeCertificates(device);
        break;
      case 'enhanced_monitoring':
        await this.enableEnhancedMonitoring(device);
        break;
    }
    
    await this.auditLogger.log({
      event: 'device_policy_enforced',
      deviceId,
      policyType,
      timestamp: new Date()
    });
  }
}
```

### Continuous Risk Assessment

```typescript
// services/continuousRiskAssessment.ts
export interface RiskFactors {
  identity: IdentityRisk;
  device: DeviceRisk;
  network: NetworkRisk;
  behavior: BehaviorRisk;
  threat: ThreatRisk;
}

export interface RiskAssessmentResult {
  overallScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  factors: RiskFactors;
  recommendations: string[];
  requiresAction: boolean;
  expiresAt: Date;
}

export class ContinuousRiskAssessmentService {
  constructor(
    private identityService: IdentityService,
    private deviceService: DeviceService,
    private networkService: NetworkService,
    private behaviorAnalytics: BehaviorAnalyticsService,
    private threatIntelligence: ThreatIntelligenceService,
    private policyEngine: ZeroTrustPolicyEngine
  ) {}
  
  async assessContinuousRisk(sessionId: string): Promise<RiskAssessmentResult> {
    const session = await this.getActiveSession(sessionId);
    
    // Evaluate all risk factors in parallel
    const [identityRisk, deviceRisk, networkRisk, behaviorRisk, threatRisk] = await Promise.all([
      this.assessIdentityRisk(session.userId),
      this.assessDeviceRisk(session.deviceId),
      this.assessNetworkRisk(session.networkContext),
      this.assessBehaviorRisk(session.userId, session.activities),
      this.assessThreatRisk(session)
    ]);
    
    const factors: RiskFactors = {
      identity: identityRisk,
      device: deviceRisk,
      network: networkRisk,
      behavior: behaviorRisk,
      threat: threatRisk
    };
    
    // Calculate composite risk score
    const overallScore = this.calculateCompositeRisk(factors);
    const riskLevel = this.determineRiskLevel(overallScore);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(factors, riskLevel);
    
    const result: RiskAssessmentResult = {
      overallScore,
      riskLevel,
      factors,
      recommendations,
      requiresAction: riskLevel === 'high' || riskLevel === 'critical',
      expiresAt: new Date(Date.now() + 5 * 60 * 1000) // 5 minutes
    };
    
    // Store assessment result
    await this.storeAssessmentResult(sessionId, result);
    
    // Trigger automated responses if needed
    if (result.requiresAction) {
      await this.triggerAutomatedResponse(sessionId, result);
    }
    
    return result;
  }
  
  private calculateCompositeRisk(factors: RiskFactors): number {
    // Weighted risk calculation
    const weights = {
      identity: 0.25,
      device: 0.20,
      network: 0.15,
      behavior: 0.25,
      threat: 0.15
    };
    
    let compositeScore = 0;
    compositeScore += factors.identity.score * weights.identity;
    compositeScore += factors.device.score * weights.device;
    compositeScore += factors.network.score * weights.network;
    compositeScore += factors.behavior.score * weights.behavior;
    compositeScore += factors.threat.score * weights.threat;
    
    // Apply risk amplification for high-risk combinations
    if (factors.threat.score > 70 && factors.network.score > 60) {
      compositeScore *= 1.3; // High threat + untrusted network
    }
    
    if (factors.identity.score > 60 && factors.device.score > 60) {
      compositeScore *= 1.2; // Compromised identity + untrusted device
    }
    
    if (factors.behavior.score > 80) {
      compositeScore *= 1.4; // Highly anomalous behavior
    }
    
    return Math.min(compositeScore, 100);
  }
  
  private async triggerAutomatedResponse(
    sessionId: string,
    assessment: RiskAssessmentResult
  ): Promise<void> {
    const responses: string[] = [];
    
    if (assessment.riskLevel === 'critical') {
      // Immediate session termination
      await this.terminateSession(sessionId);
      responses.push('session_terminated');
      
      // Lock user account
      const session = await this.getActiveSession(sessionId);
      await this.identityService.lockAccount(session.userId);
      responses.push('account_locked');
      
      // Quarantine device
      await this.deviceService.quarantineDevice(session.deviceId);
      responses.push('device_quarantined');
    } else if (assessment.riskLevel === 'high') {
      // Require step-up authentication
      await this.requireStepUpAuth(sessionId);
      responses.push('step_up_auth_required');
      
      // Enable enhanced monitoring
      await this.enableEnhancedMonitoring(sessionId);
      responses.push('enhanced_monitoring_enabled');
    }
    
    // Log automated response
    await this.auditLogger.log({
      event: 'automated_risk_response',
      sessionId,
      riskLevel: assessment.riskLevel,
      riskScore: assessment.overallScore,
      responses,
      timestamp: new Date()
    });
  }
}
```

### Policy as Code Implementation

```yaml
# policies/zero-trust-policies.yaml
apiVersion: security.ccl.dev/v1
kind: ZeroTrustPolicy
metadata:
  name: high-privilege-access-policy
  namespace: ccl-security
spec:
  description: "Strict controls for high-privilege operations"
  scope:
    services: ["ccl-admin", "ccl-billing"]
    resources: ["admin/*", "billing/*"]
    users: ["role:admin", "role:billing_admin"]
  rules:
    - id: "admin-mfa-required"
      type: "identity"
      conditions:
        - attribute: "user.role"
          operator: "in"
          value: ["admin", "billing_admin"]
          weight: 1.0
      actions:
        - type: "challenge"
          parameters:
            mfa_methods: ["totp", "webauthn"]
            max_age_minutes: 5
          notification_required: true
      risk_score_threshold: 0
      continuous_verification: true
      
    - id: "admin-device-compliance"
      type: "device"
      conditions:
        - attribute: "device.managed"
          operator: "equals"
          value: true
          weight: 0.8
        - attribute: "device.compliance_score"
          operator: "gt"
          value: 90
          weight: 0.9
      actions:
        - type: "deny"
          parameters:
            reason: "Device not compliant for admin access"
          notification_required: true
      risk_score_threshold: 20
      continuous_verification: true
      
    - id: "admin-network-restriction"
      type: "network"
      conditions:
        - attribute: "network.type"
          operator: "in"
          value: ["corporate", "vpn"]
          weight: 1.0
        - attribute: "network.location"
          operator: "in"
          value: ["US", "CA", "EU"]
          weight: 0.7
      actions:
        - type: "deny"
          parameters:
            reason: "Admin access requires corporate network"
          notification_required: true
      risk_score_threshold: 10
      continuous_verification: true
  enforcement: "enforce"
  priority: 100
  
---
apiVersion: security.ccl.dev/v1
kind: ZeroTrustPolicy
metadata:
  name: data-access-policy
  namespace: ccl-security
spec:
  description: "Controls for sensitive data access"
  scope:
    services: ["ccl-analysis-engine", "ccl-query-intelligence"]
    resources: ["data/sensitive/*", "data/pii/*"]
  rules:
    - id: "sensitive-data-encryption"
      type: "data"
      conditions:
        - attribute: "data.classification"
          operator: "in"
          value: ["confidential", "restricted"]
          weight: 1.0
      actions:
        - type: "allow"
          parameters:
            require_encryption: true
            audit_level: "detailed"
          notification_required: false
      risk_score_threshold: 0
      continuous_verification: false
      
    - id: "pii-access-logging"
      type: "data"
      conditions:
        - attribute: "data.contains_pii"
          operator: "equals"
          value: true
          weight: 1.0
      actions:
        - type: "audit"
          parameters:
            log_level: "full"
            retention_days: 2555 # 7 years
          notification_required: true
      risk_score_threshold: 0
      continuous_verification: false
  enforcement: "enforce"
  priority: 200
```

## Validation Loop

### Level 1: Policy Engine Testing
```typescript
// Test Zero Trust policy evaluation
describe('Zero Trust Policy Engine', () => {
  test('high-risk user requires step-up auth', async () => {
    const request = createMockRequest({
      identity: { verified: true, mfaEnabled: false, riskScore: 75 },
      device: { managed: true, compliant: true, riskScore: 20 },
      network: { type: 'public', riskScore: 60 },
      resource: { classification: 'confidential' }
    });
    
    const decision = await policyEngine.evaluateAccess(request);
    
    expect(decision.granted).toBe(true);
    expect(decision.stepUpAuthRequired).toBe(true);
    expect(decision.challengeRequired).toBe(true);
  });
  
  test('critical risk triggers denial', async () => {
    const request = createMockRequest({
      identity: { verified: false, mfaEnabled: false, riskScore: 90 },
      device: { managed: false, compliant: false, riskScore: 80 },
      network: { type: 'tor', riskScore: 95 },
      threat: { active: true, riskScore: 85 }
    });
    
    const decision = await policyEngine.evaluateAccess(request);
    
    expect(decision.granted).toBe(false);
    expect(decision.reason).toContain('Risk score too high');
  });
});
```

### Level 2: Network Security Testing
```typescript
// Test network segmentation
describe('Network Segmentation', () => {
  test('prevents lateral movement between segments', async () => {
    const request = {
      sourceIP: '*********', // services segment
      targetIP: '*********',  // data segment
      service: 'ssh',
      port: 22
    };
    
    const allowed = await networkSegmentation.validateNetworkAccess(request);
    
    expect(allowed).toBe(false);
  });
  
  test('allows authorized service communication', async () => {
    const request = {
      sourceIP: '*********', // services segment
      targetIP: '*********',  // data segment
      service: 'spanner',
      port: 443
    };
    
    const allowed = await networkSegmentation.validateNetworkAccess(request);
    
    expect(allowed).toBe(true);
  });
});
```

### Level 3: Device Trust Testing
```typescript
// Test device compliance evaluation
describe('Device Trust Service', () => {
  test('compliant device gets high trust score', async () => {
    const device = createCompliantDevice();
    const result = await deviceTrustService.evaluateDeviceTrust(device.deviceId);
    
    expect(result.trustScore).toBeGreaterThan(80);
    expect(result.compliant).toBe(true);
    expect(result.anomalies).toHaveLength(0);
  });
  
  test('jailbroken device gets quarantined', async () => {
    const device = createJailbrokenDevice();
    const result = await deviceTrustService.evaluateDeviceTrust(device.deviceId);
    
    expect(result.trustScore).toBeLessThan(50);
    expect(result.recommendedActions).toContain('quarantine');
  });
});
```

## Final Validation Checklist

### Security Controls
- [ ] VPC Service Controls perimeter configured
- [ ] Network segmentation implemented
- [ ] Device trust evaluation working
- [ ] Identity risk assessment functional
- [ ] Continuous risk monitoring active
- [ ] Policy engine enforcing rules
- [ ] Threat intelligence integrated
- [ ] Audit logging comprehensive

### Performance Metrics
- [ ] <100ms access decision latency
- [ ] 99.9% uptime for security services
- [ ] Zero false positive rate for legitimate users
- [ ] <1% performance impact on applications
- [ ] Real-time policy updates within 30 seconds

### Compliance Requirements
- [ ] SOC2 Type II controls implemented
- [ ] HIPAA safeguards in place
- [ ] GDPR privacy protections active
- [ ] Audit trails complete and tamper-proof
- [ ] Incident response procedures tested

## Anti-Patterns to Avoid

1. **DON'T trust internal networks** - Zero trust means never trust
2. **DON'T use static policies** - Policies must be dynamic and contextual
3. **DON'T ignore user experience** - Security shouldn't break workflows
4. **DON'T centralize all decisions** - Distribute for performance and resilience
5. **DON'T log sensitive data** - Audit logs must protect privacy
6. **DON'T ignore false positives** - Tune policies to minimize friction
7. **DON'T skip testing** - Security controls must be thoroughly tested
8. **DON'T forget monitoring** - Continuous visibility is essential
9. **DON'T hardcode policies** - Use configuration and policy-as-code
10. **DON'T assume breach won't happen** - Design for containment and recovery
