name: "Service Implementation PRP Template"
description: |
  Template for implementing microservices in the CCL platform.
  Optimized for AI agents to build production-ready services with comprehensive context.

---

## Goal
Implement the [SERVICE_NAME] service as part of the CCL platform microservices architecture.

## Why
- **Business Value**: [Specific business value this service provides]
- **Architecture Role**: [How this service fits in the overall architecture]
- **User Impact**: [Direct impact on user experience]
- **Technical Necessity**: [Why this service is technically required]

## What
A production-ready microservice that:
- [Primary responsibility/capability]
- [Secondary responsibilities]
- [Integration requirements]
- [Performance requirements]

### Success Criteria
- [ ] Service starts and responds to health checks
- [ ] All API endpoints implemented and tested
- [ ] Integration with other services working
- [ ] Performance meets SLO requirements
- [ ] Security requirements implemented
- [ ] Monitoring and logging configured
- [ ] Documentation complete

## All Needed Context

### Service Specifications
```yaml
Service Details:
  name: [service-name]
  language: [Rust/Python/Go/TypeScript]
  runtime: [Cloud Run/Container]
  port: [port-number]
  
Architecture:
  pattern: [microservice/serverless]
  communication: [REST/gRPC/Events]
  data_store: [Spanner/BigQuery/Firestore]
  
Performance:
  slo_response_time: [<100ms]
  slo_availability: [99.9%]
  scaling: [0-1000 instances]
  memory: [512MB-4GB]
```

### Technology Stack
```yaml
Primary Language: [Language]
Framework: [Framework details]
Database: [Database type and configuration]
Dependencies:
  - [dependency-1]: [version] # [purpose]
  - [dependency-2]: [version] # [purpose]
  
Development Tools:
  - [tool-1]: [purpose]
  - [tool-2]: [purpose]
```

### Current Codebase Context
```bash
# Current project structure
[INSERT TREE OUTPUT]
```

### Desired Service Structure
```bash
services/[service-name]/
├── cmd/                    # Entry points
│   └── main.[ext]         # Service main
├── internal/              # Private implementation
│   ├── handlers/          # HTTP/gRPC handlers
│   ├── services/          # Business logic
│   ├── models/            # Data models
│   └── config/            # Configuration
├── pkg/                   # Public packages
│   └── client/            # Client library
├── api/                   # API definitions
│   ├── openapi.yaml       # REST API spec
│   └── proto/             # gRPC definitions
├── tests/                 # Test files
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   └── fixtures/          # Test data
├── deployments/           # Deployment configs
│   ├── Dockerfile         # Container definition
│   └── cloudbuild.yaml    # Build configuration
└── docs/                  # Service documentation
    ├── README.md          # Service overview
    └── api.md             # API documentation
```

### Integration Requirements
```yaml
Upstream Dependencies:
  - [service-name]: [API endpoints used]
  - [external-api]: [Integration details]
  
Downstream Consumers:
  - [service-name]: [APIs provided]
  - [client-type]: [Usage patterns]
  
Event Subscriptions:
  - [event-type]: [Handler details]
  
Event Publications:
  - [event-type]: [When published]
```

### Known Gotchas & Library Quirks
```yaml
Language-Specific:
  # [Language] specific gotchas
  - [Issue]: [Solution/Workaround]
  
Framework-Specific:
  # [Framework] specific issues
  - [Issue]: [Solution/Workaround]
  
CCL Platform:
  # Platform-specific considerations
  - [Issue]: [Solution/Workaround]
  
Google Cloud:
  # GCP-specific considerations
  - [Issue]: [Solution/Workaround]
```

## Implementation Blueprint

### Phase 1: Service Foundation
1. **Project Structure Setup**
   - Create service directory structure
   - Initialize package management ([language-specific])
   - Set up basic configuration

2. **Core Service Implementation**
   - Implement main service entry point
   - Add health check endpoints
   - Configure logging and metrics

3. **Configuration Management**
   - Environment variable handling
   - Configuration validation
   - Secret management integration

### Phase 2: Business Logic
1. **Data Models**
   - Define core data structures
   - Add validation rules
   - Implement serialization

2. **Business Services**
   - Core business logic implementation
   - Error handling patterns
   - Input validation

3. **Data Access Layer**
   - Database connection setup
   - Repository pattern implementation
   - Query optimization

### Phase 3: API Implementation
1. **REST API Endpoints**
   - Route definitions
   - Request/response handling
   - Input validation and sanitization

2. **Authentication & Authorization**
   - JWT token validation
   - Role-based access control
   - API key management

3. **API Documentation**
   - OpenAPI specification
   - Example requests/responses
   - Error code documentation

### Phase 4: Integration & Testing
1. **Service Integration**
   - Client library implementation
   - Inter-service communication
   - Event handling

2. **Testing Suite**
   - Unit test implementation
   - Integration test setup
   - Mock service creation

3. **Performance Testing**
   - Load testing setup
   - Performance benchmarking
   - Optimization implementation

### Phase 5: Production Readiness
1. **Monitoring & Observability**
   - Metrics collection
   - Distributed tracing
   - Log aggregation

2. **Deployment Configuration**
   - Dockerfile optimization
   - Cloud Run configuration
   - Environment-specific configs

3. **Documentation**
   - Service README
   - API documentation
   - Runbook creation

## Validation Gates

### Development Validation
```bash
# Code Quality
[language-specific linting commands]

# Unit Tests
[language-specific test commands]

# Integration Tests
[integration test commands]

# Security Scan
[security scanning commands]
```

### Deployment Validation
```bash
# Build Validation
[build commands]

# Container Validation
[container test commands]

# Service Health Check
curl -f http://localhost:[port]/health

# API Validation
[API test commands]
```

### Performance Validation
```bash
# Load Testing
[load test commands]

# Performance Benchmarks
[benchmark commands]

# Resource Usage Check
[resource monitoring commands]
```

## Success Metrics

### Technical Metrics
- **Response Time**: [target] (p95)
- **Availability**: [target]%
- **Error Rate**: <[target]%
- **Test Coverage**: >[target]%

### Business Metrics
- **Feature Completeness**: 100%
- **API Compliance**: 100%
- **Documentation Coverage**: 100%
- **Security Compliance**: 100%

## Final Validation Checklist
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Performance tests meet SLO
- [ ] Security scan passes
- [ ] API documentation complete
- [ ] Service documentation updated
- [ ] Monitoring configured
- [ ] Deployment successful
- [ ] Health checks passing
- [ ] Integration with other services verified

---

## Implementation Notes

### Pattern References
- Follow patterns established in `examples/[service-type]/`
- Use existing configuration patterns from other services
- Maintain consistency with CCL architecture principles

### Quality Standards
- Maintain >90% test coverage
- Follow language-specific style guides
- Implement comprehensive error handling
- Add detailed logging for debugging

### Security Requirements
- Implement input validation
- Use secure communication (TLS)
- Follow principle of least privilege
- Add audit logging for sensitive operations
