# Go SDK Implementation

name: "Go SDK Implementation"
description: |
  Comprehensive Go SDK for CCL platform providing idiomatic Go client libraries with context support, structured error handling, and high-performance integrations.
  
  Core Principles:
  - **Idiomatic Go**: Follow Go conventions and best practices
  - **Context Support**: Proper context.Context usage throughout
  - **Performance**: Optimized for high-throughput applications
  - **Type Safety**: Strong typing with generated types
  - **Error Handling**: Comprehensive error types and handling

## Goal

Create an idiomatic Go SDK that provides seamless integration with CCL platform APIs while maintaining performance, type safety, and excellent developer experience for Go developers.

## Why

Go SDK enables:
- High-performance backend services
- Microservice integrations
- CLI tool development
- DevOps automation
- Enterprise integrations

This provides:
- Reduced integration complexity
- Idiomatic Go patterns
- High-performance operations
- Comprehensive error handling
- Production-ready reliability

## What

### User-Visible Behavior
- Simple go get installation
- Idiomatic Go APIs
- Context-aware operations
- Structured error handling
- Configurable client options

### Technical Requirements
- [ ] Go 1.19+ compatibility
- [ ] Full context.Context support
- [ ] Structured error types
- [ ] HTTP/2 and connection pooling
- [ ] Request/response middleware
- [ ] Retry logic with backoff
- [ ] Webhook verification utilities
- [ ] Comprehensive logging integration

### Success Criteria
- [ ] Idiomatic Go code structure
- [ ] Context cancellation support
- [ ] High-performance HTTP client
- [ ] Comprehensive error handling
- [ ] Production-ready reliability

## All Needed Context

### Documentation & References
- url: https://go.dev/doc/effective_go
  why: Go language best practices
- url: https://pkg.go.dev/net/http
  why: HTTP client patterns
- url: https://pkg.go.dev/context
  why: Context usage patterns
- file: docs/guides/sdk-documentation.md
  why: Existing Go examples and patterns

### Package Structure

```yaml
Package Organization:
  client:
    - client.go
    - options.go
    - middleware.go
    
  types:
    - repository.go
    - analysis.go
    - pattern.go
    - query.go
    
  services:
    - repositories.go
    - analysis.go
    - patterns.go
    - queries.go
    
  errors:
    - errors.go
    - codes.go
    
  utils:
    - webhooks.go
    - retry.go
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Always use context.Context for cancellation
- **CRITICAL**: Handle HTTP client lifecycle properly
- **GOTCHA**: JSON unmarshaling with time.Time types
- **GOTCHA**: Error wrapping with fmt.Errorf
- **WARNING**: Memory leaks with unclosed response bodies
- **TIP**: Use structured logging with slog
- **TIP**: Implement proper rate limiting

## Implementation Blueprint

### Core Client Implementation

```go
// client/client.go
package ccl

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/ccl-platform/ccl-go/errors"
	"github.com/ccl-platform/ccl-go/types"
)

// Client represents the main CCL SDK client
type Client struct {
	httpClient *http.Client
	baseURL    *url.URL
	apiKey     string
	userAgent  string
	
	// Service modules
	Repositories *RepositoryService
	Analysis     *AnalysisService
	Patterns     *PatternService
	Queries      *QueryService
	Marketplace  *MarketplaceService
	
	// Middleware
	middleware []Middleware
}

// ClientOption represents a client configuration option
type ClientOption func(*clientConfig)

type clientConfig struct {
	APIKey     string
	BaseURL    string
	HTTPClient *http.Client
	UserAgent  string
	Timeout    time.Duration
	Middleware []Middleware
	Debug      bool
}

// NewClient creates a new CCL client with the given options
func NewClient(opts ...ClientOption) (*Client, error) {
	config := &clientConfig{
		BaseURL:   "https://api.ccl.dev/v1",
		UserAgent: fmt.Sprintf("ccl-go-sdk/%s", Version),
		Timeout:   30 * time.Second,
		Debug:     false,
	}
	
	// Apply options
	for _, opt := range opts {
		opt(config)
	}
	
	// Validate required options
	if config.APIKey == "" {
		return nil, errors.New(errors.ErrCodeInvalidConfig, "API key is required")
	}
	
	// Parse base URL
	baseURL, err := url.Parse(config.BaseURL)
	if err != nil {
		return nil, errors.Wrap(err, errors.ErrCodeInvalidConfig, "invalid base URL")
	}
	
	// Create HTTP client if not provided
	httpClient := config.HTTPClient
	if httpClient == nil {
		httpClient = &http.Client{
			Timeout: config.Timeout,
			Transport: &http.Transport{
				MaxIdleConns:       100,
				IdleConnTimeout:    90 * time.Second,
				DisableCompression: false,
			},
		}
	}
	
	client := &Client{
		httpClient: httpClient,
		baseURL:    baseURL,
		apiKey:     config.APIKey,
		userAgent:  config.UserAgent,
		middleware: config.Middleware,
	}
	
	// Initialize service modules
	client.Repositories = &RepositoryService{client: client}
	client.Analysis = &AnalysisService{client: client}
	client.Patterns = &PatternService{client: client}
	client.Queries = &QueryService{client: client}
	client.Marketplace = &MarketplaceService{client: client}
	
	return client, nil
}

// WithAPIKey sets the API key
func WithAPIKey(apiKey string) ClientOption {
	return func(c *clientConfig) {
		c.APIKey = apiKey
	}
}

// WithBaseURL sets the base URL
func WithBaseURL(baseURL string) ClientOption {
	return func(c *clientConfig) {
		c.BaseURL = baseURL
	}
}

// WithHTTPClient sets a custom HTTP client
func WithHTTPClient(httpClient *http.Client) ClientOption {
	return func(c *clientConfig) {
		c.HTTPClient = httpClient
	}
}

// WithTimeout sets the request timeout
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *clientConfig) {
		c.Timeout = timeout
	}
}

// WithMiddleware adds middleware to the client
func WithMiddleware(middleware ...Middleware) ClientOption {
	return func(c *clientConfig) {
		c.Middleware = append(c.Middleware, middleware...)
	}
}

// WithDebug enables debug logging
func WithDebug(debug bool) ClientOption {
	return func(c *clientConfig) {
		c.Debug = debug
	}
}

// Request makes an HTTP request to the CCL API
func (c *Client) Request(ctx context.Context, method, path string, body interface{}, result interface{}) error {
	// Create request URL
	requestURL := c.baseURL.ResolveReference(&url.URL{Path: path})
	
	// Encode request body
	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		if err != nil {
			return errors.Wrap(err, errors.ErrCodeSerialization, "failed to marshal request body")
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}
	
	// Create HTTP request
	req, err := http.NewRequestWithContext(ctx, method, requestURL.String(), bodyReader)
	if err != nil {
		return errors.Wrap(err, errors.ErrCodeRequest, "failed to create request")
	}
	
	// Set headers
	req.Header.Set("Authorization", "Bearer "+c.apiKey)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", c.userAgent)
	req.Header.Set("X-Request-ID", generateRequestID())
	
	// Apply middleware
	for _, middleware := range c.middleware {
		if err := middleware.ProcessRequest(req); err != nil {
			return errors.Wrap(err, errors.ErrCodeMiddleware, "middleware failed")
		}
	}
	
	// Make request
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return errors.Wrap(err, errors.ErrCodeNetwork, "request failed")
	}
	defer resp.Body.Close()
	
	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return errors.Wrap(err, errors.ErrCodeResponse, "failed to read response body")
	}
	
	// Apply response middleware
	for _, middleware := range c.middleware {
		if err := middleware.ProcessResponse(resp, respBody); err != nil {
			return errors.Wrap(err, errors.ErrCodeMiddleware, "response middleware failed")
		}
	}
	
	// Handle error responses
	if resp.StatusCode >= 400 {
		return c.handleErrorResponse(resp.StatusCode, respBody)
	}
	
	// Unmarshal response
	if result != nil {
		if err := json.Unmarshal(respBody, result); err != nil {
			return errors.Wrap(err, errors.ErrCodeSerialization, "failed to unmarshal response")
		}
	}
	
	return nil
}

// Get makes a GET request
func (c *Client) Get(ctx context.Context, path string, result interface{}) error {
	return c.Request(ctx, http.MethodGet, path, nil, result)
}

// Post makes a POST request
func (c *Client) Post(ctx context.Context, path string, body, result interface{}) error {
	return c.Request(ctx, http.MethodPost, path, body, result)
}

// Put makes a PUT request
func (c *Client) Put(ctx context.Context, path string, body, result interface{}) error {
	return c.Request(ctx, http.MethodPut, path, body, result)
}

// Delete makes a DELETE request
func (c *Client) Delete(ctx context.Context, path string) error {
	return c.Request(ctx, http.MethodDelete, path, nil, nil)
}

// handleErrorResponse handles API error responses
func (c *Client) handleErrorResponse(statusCode int, body []byte) error {
	var apiError types.APIError
	if err := json.Unmarshal(body, &apiError); err != nil {
		// Fallback to generic error
		return errors.New(errors.ErrCodeAPI, fmt.Sprintf("API error %d: %s", statusCode, string(body)))
	}
	
	switch statusCode {
	case 400:
		return errors.NewValidationError(apiError.Message, apiError.Details)
	case 401:
		return errors.NewAuthenticationError(apiError.Message)
	case 403:
		return errors.NewAuthorizationError(apiError.Message)
	case 404:
		return errors.NewNotFoundError(apiError.Message)
	case 429:
		retryAfter := extractRetryAfter(apiError.Details)
		return errors.NewRateLimitError(apiError.Message, retryAfter)
	case 500:
		return errors.NewInternalError(apiError.Message)
	default:
		return errors.New(errors.ErrCodeAPI, apiError.Message)
	}
}

func generateRequestID() string {
	// Generate unique request ID
	return fmt.Sprintf("req_%d_%s", time.Now().UnixNano(), randomString(8))
}

func randomString(length int) string {
	// Implementation for random string generation
	const charset = "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}

func extractRetryAfter(details interface{}) time.Duration {
	if details == nil {
		return 0
	}
	
	detailsMap, ok := details.(map[string]interface{})
	if !ok {
		return 0
	}
	
	retryAfter, ok := detailsMap["retry_after"]
	if !ok {
		return 0
	}
	
	retryAfterFloat, ok := retryAfter.(float64)
	if !ok {
		return 0
	}
	
	return time.Duration(retryAfterFloat) * time.Second
}
```

### Type Definitions

```go
// types/repository.go
package types

import (
	"time"
)

// RepositoryProvider represents the repository provider
type RepositoryProvider string

const (
	RepositoryProviderGitHub    RepositoryProvider = "github"
	RepositoryProviderGitLab    RepositoryProvider = "gitlab"
	RepositoryProviderBitbucket RepositoryProvider = "bitbucket"
	RepositoryProviderLocal     RepositoryProvider = "local"
)

// Repository represents a code repository
type Repository struct {
	ID             string              `json:"id"`
	Name           string              `json:"name"`
	URL            string              `json:"url"`
	Provider       RepositoryProvider  `json:"provider"`
	OwnerID        string              `json:"owner_id"`
	OrganizationID *string             `json:"organization_id,omitempty"`
	Description    *string             `json:"description,omitempty"`
	Language       string              `json:"language"`
	IsPrivate      bool                `json:"is_private"`
	LastAnalyzedAt *time.Time          `json:"last_analyzed_at,omitempty"`
	CreatedAt      time.Time           `json:"created_at"`
	UpdatedAt      time.Time           `json:"updated_at"`
}

// CreateRepositoryRequest represents a request to create a repository
type CreateRepositoryRequest struct {
	Name           string             `json:"name"`
	URL            string             `json:"url"`
	Provider       RepositoryProvider `json:"provider"`
	OrganizationID *string            `json:"organization_id,omitempty"`
	AutoAnalyze    bool               `json:"auto_analyze"`
}

// UpdateRepositoryRequest represents a request to update a repository
type UpdateRepositoryRequest struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
}

// RepositoryFilter represents repository filtering options
type RepositoryFilter struct {
	Provider       *RepositoryProvider `json:"provider,omitempty"`
	Language       *string             `json:"language,omitempty"`
	HasAnalysis    *bool               `json:"has_analysis,omitempty"`
	OrganizationID *string             `json:"organization_id,omitempty"`
	Search         *string             `json:"search,omitempty"`
}

// types/analysis.go
package types

import (
	"time"
)

// AnalysisStatus represents the status of an analysis
type AnalysisStatus string

const (
	AnalysisStatusPending   AnalysisStatus = "pending"
	AnalysisStatusRunning   AnalysisStatus = "running"
	AnalysisStatusCompleted AnalysisStatus = "completed"
	AnalysisStatusFailed    AnalysisStatus = "failed"
	AnalysisStatusCancelled AnalysisStatus = "cancelled"
)

// Analysis represents a code analysis
type Analysis struct {
	ID           string             `json:"id"`
	RepositoryID string             `json:"repository_id"`
	Status       AnalysisStatus     `json:"status"`
	StartedAt    time.Time          `json:"started_at"`
	CompletedAt  *time.Time         `json:"completed_at,omitempty"`
	Duration     *int               `json:"duration,omitempty"` // seconds
	Commit       *string            `json:"commit,omitempty"`
	Branch       string             `json:"branch"`
	Statistics   *AnalysisStatistics `json:"statistics,omitempty"`
	Error        *AnalysisError     `json:"error,omitempty"`
	CreatedAt    time.Time          `json:"created_at"`
	UpdatedAt    time.Time          `json:"updated_at"`
}

// AnalysisStatistics represents analysis statistics
type AnalysisStatistics struct {
	TotalFiles           int                `json:"total_files"`
	TotalLines           int                `json:"total_lines"`
	LanguageBreakdown    map[string]int     `json:"language_breakdown"`
	Complexity           float64            `json:"complexity"`
	MaintainabilityIndex float64            `json:"maintainability_index"`
	TechnicalDebt        float64            `json:"technical_debt"`
}

// AnalysisError represents an analysis error
type AnalysisError struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
}

// AnalysisOptions represents analysis options
type AnalysisOptions struct {
	Incremental                  bool     `json:"incremental"`
	DeepPatternAnalysis          bool     `json:"deep_pattern_analysis"`
	GenerateEmbeddings           bool     `json:"generate_embeddings"`
	IncludePaths                 []string `json:"include_paths,omitempty"`
	ExcludePaths                 []string `json:"exclude_paths,omitempty"`
	PatternConfidenceThreshold   float64  `json:"pattern_confidence_threshold"`
}

// StartAnalysisRequest represents a request to start analysis
type StartAnalysisRequest struct {
	RepositoryID string           `json:"repository_id"`
	Branch       string           `json:"branch"`
	Commit       *string          `json:"commit,omitempty"`
	Options      *AnalysisOptions `json:"options,omitempty"`
}

// types/pattern.go
package types

import (
	"time"
)

// PatternType represents the type of pattern
type PatternType string

const (
	PatternTypeArchitectural PatternType = "architectural"
	PatternTypeDesign        PatternType = "design"
	PatternTypeIdiom         PatternType = "idiom"
	PatternTypeAntiPattern   PatternType = "anti-pattern"
	PatternTypeSecurity      PatternType = "security"
	PatternTypePerformance   PatternType = "performance"
)

// Pattern represents a detected code pattern
type Pattern struct {
	ID           string            `json:"id"`
	RepositoryID string            `json:"repository_id"`
	AnalysisID   string            `json:"analysis_id"`
	Type         PatternType       `json:"type"`
	Name         string            `json:"name"`
	Description  string            `json:"description"`
	Confidence   float64           `json:"confidence"`
	Occurrences  int               `json:"occurrences"`
	Locations    []PatternLocation `json:"locations"`
	Template     PatternTemplate   `json:"template"`
	QualityScore float64           `json:"quality_score"`
	Tags         []string          `json:"tags"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
}

// PatternLocation represents where a pattern was found
type PatternLocation struct {
	File      string `json:"file"`
	StartLine int    `json:"start_line"`
	EndLine   int    `json:"end_line"`
	Snippet   string `json:"snippet"`
}

// PatternTemplate represents a pattern template
type PatternTemplate struct {
	Code      string   `json:"code"`
	Variables []string `json:"variables"`
	Language  string   `json:"language"`
}

// PatternFilter represents pattern filtering options
type PatternFilter struct {
	Type          *PatternType `json:"type,omitempty"`
	MinConfidence *float64     `json:"min_confidence,omitempty"`
	Language      *string      `json:"language,omitempty"`
	RepositoryID  *string      `json:"repository_id,omitempty"`
	AnalysisID    *string      `json:"analysis_id,omitempty"`
	Search        *string      `json:"search,omitempty"`
}

// ValidateCodeRequest represents a request to validate code against patterns
type ValidateCodeRequest struct {
	RepositoryID string `json:"repository_id"`
	Code         string `json:"code"`
	FilePath     string `json:"file_path"`
	Language     string `json:"language"`
}

// ValidationResult represents the result of pattern validation
type ValidationResult struct {
	Valid      bool                 `json:"valid"`
	Violations []PatternViolation   `json:"violations"`
	Suggestions []PatternSuggestion `json:"suggestions"`
}

// PatternViolation represents a pattern violation
type PatternViolation struct {
	PatternID   string `json:"pattern_id"`
	PatternName string `json:"pattern_name"`
	Message     string `json:"message"`
	Line        int    `json:"line"`
	Column      int    `json:"column"`
	Severity    string `json:"severity"`
}

// PatternSuggestion represents a pattern suggestion
type PatternSuggestion struct {
	Message     string `json:"message"`
	Replacement string `json:"replacement,omitempty"`
	Line        int    `json:"line"`
	Column      int    `json:"column"`
}

// types/common.go
package types

// PaginationOptions represents pagination options
type PaginationOptions struct {
	Limit  int     `json:"limit,omitempty"`
	Offset int     `json:"offset,omitempty"`
	Cursor *string `json:"cursor,omitempty"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	Total      int     `json:"total"`
	Limit      int     `json:"limit"`
	Offset     int     `json:"offset"`
	HasMore    bool    `json:"has_more"`
	NextCursor *string `json:"next_cursor,omitempty"`
	PrevCursor *string `json:"prev_cursor,omitempty"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse[T any] struct {
	Data       []T            `json:"data"`
	Pagination PaginationInfo `json:"pagination"`
}

// APIError represents an API error response
type APIError struct {
	Code      string      `json:"code"`
	Message   string      `json:"message"`
	Details   interface{} `json:"details,omitempty"`
	RequestID string      `json:"request_id,omitempty"`
	Timestamp string      `json:"timestamp,omitempty"`
}
```

### Service Implementations

```go
// services/repositories.go
package ccl

import (
	"context"
	"fmt"
	"net/url"
	"strconv"

	"github.com/ccl-platform/ccl-go/types"
)

// RepositoryService handles repository operations
type RepositoryService struct {
	client *Client
}

// List retrieves a list of repositories
func (s *RepositoryService) List(ctx context.Context, filter *types.RepositoryFilter, pagination *types.PaginationOptions) (*types.PaginatedResponse[types.Repository], error) {
	path := "/repositories"
	
	// Build query parameters
	params := url.Values{}
	if filter != nil {
		if filter.Provider != nil {
			params.Set("provider", string(*filter.Provider))
		}
		if filter.Language != nil {
			params.Set("language", *filter.Language)
		}
		if filter.HasAnalysis != nil {
			params.Set("has_analysis", strconv.FormatBool(*filter.HasAnalysis))
		}
		if filter.OrganizationID != nil {
			params.Set("organization_id", *filter.OrganizationID)
		}
		if filter.Search != nil {
			params.Set("search", *filter.Search)
		}
	}
	
	if pagination != nil {
		if pagination.Limit > 0 {
			params.Set("limit", strconv.Itoa(pagination.Limit))
		}
		if pagination.Offset > 0 {
			params.Set("offset", strconv.Itoa(pagination.Offset))
		}
		if pagination.Cursor != nil {
			params.Set("cursor", *pagination.Cursor)
		}
	}
	
	if len(params) > 0 {
		path += "?" + params.Encode()
	}
	
	var result types.PaginatedResponse[types.Repository]
	if err := s.client.Get(ctx, path, &result); err != nil {
		return nil, err
	}
	
	return &result, nil
}

// Get retrieves a specific repository by ID
func (s *RepositoryService) Get(ctx context.Context, repositoryID string) (*types.Repository, error) {
	path := fmt.Sprintf("/repositories/%s", repositoryID)
	
	var repository types.Repository
	if err := s.client.Get(ctx, path, &repository); err != nil {
		return nil, err
	}
	
	return &repository, nil
}

// Create creates a new repository
func (s *RepositoryService) Create(ctx context.Context, request *types.CreateRepositoryRequest) (*types.Repository, error) {
	path := "/repositories"
	
	var repository types.Repository
	if err := s.client.Post(ctx, path, request, &repository); err != nil {
		return nil, err
	}
	
	return &repository, nil
}

// Update updates an existing repository
func (s *RepositoryService) Update(ctx context.Context, repositoryID string, request *types.UpdateRepositoryRequest) (*types.Repository, error) {
	path := fmt.Sprintf("/repositories/%s", repositoryID)
	
	var repository types.Repository
	if err := s.client.Put(ctx, path, request, &repository); err != nil {
		return nil, err
	}
	
	return &repository, nil
}

// Delete deletes a repository
func (s *RepositoryService) Delete(ctx context.Context, repositoryID string) error {
	path := fmt.Sprintf("/repositories/%s", repositoryID)
	return s.client.Delete(ctx, path)
}

// GetAnalyses retrieves analyses for a repository
func (s *RepositoryService) GetAnalyses(ctx context.Context, repositoryID string, pagination *types.PaginationOptions) (*types.PaginatedResponse[types.Analysis], error) {
	path := fmt.Sprintf("/repositories/%s/analyses", repositoryID)
	
	if pagination != nil {
		params := url.Values{}
		if pagination.Limit > 0 {
			params.Set("limit", strconv.Itoa(pagination.Limit))
		}
		if pagination.Offset > 0 {
			params.Set("offset", strconv.Itoa(pagination.Offset))
		}
		if pagination.Cursor != nil {
			params.Set("cursor", *pagination.Cursor)
		}
		if len(params) > 0 {
			path += "?" + params.Encode()
		}
	}
	
	var result types.PaginatedResponse[types.Analysis]
	if err := s.client.Get(ctx, path, &result); err != nil {
		return nil, err
	}
	
	return &result, nil
}

// GetPatterns retrieves patterns for a repository
func (s *RepositoryService) GetPatterns(ctx context.Context, repositoryID string, filter *types.PatternFilter, pagination *types.PaginationOptions) (*types.PaginatedResponse[types.Pattern], error) {
	path := fmt.Sprintf("/repositories/%s/patterns", repositoryID)
	
	// Build query parameters
	params := url.Values{}
	if filter != nil {
		if filter.Type != nil {
			params.Set("type", string(*filter.Type))
		}
		if filter.MinConfidence != nil {
			params.Set("min_confidence", strconv.FormatFloat(*filter.MinConfidence, 'f', -1, 64))
		}
		if filter.Language != nil {
			params.Set("language", *filter.Language)
		}
		if filter.Search != nil {
			params.Set("search", *filter.Search)
		}
	}
	
	if pagination != nil {
		if pagination.Limit > 0 {
			params.Set("limit", strconv.Itoa(pagination.Limit))
		}
		if pagination.Offset > 0 {
			params.Set("offset", strconv.Itoa(pagination.Offset))
		}
		if pagination.Cursor != nil {
			params.Set("cursor", *pagination.Cursor)
		}
	}
	
	if len(params) > 0 {
		path += "?" + params.Encode()
	}
	
	var result types.PaginatedResponse[types.Pattern]
	if err := s.client.Get(ctx, path, &result); err != nil {
		return nil, err
	}
	
	return &result, nil
}

// services/analysis.go
package ccl

import (
	"context"
	"fmt"
	"time"

	"github.com/ccl-platform/ccl-go/types"
)

// AnalysisService handles analysis operations
type AnalysisService struct {
	client *Client
}

// Start starts a new analysis
func (s *AnalysisService) Start(ctx context.Context, request *types.StartAnalysisRequest) (*types.Analysis, error) {
	path := "/analysis"
	
	var analysis types.Analysis
	if err := s.client.Post(ctx, path, request, &analysis); err != nil {
		return nil, err
	}
	
	return &analysis, nil
}

// Get retrieves an analysis by ID
func (s *AnalysisService) Get(ctx context.Context, analysisID string) (*types.Analysis, error) {
	path := fmt.Sprintf("/analysis/%s", analysisID)
	
	var analysis types.Analysis
	if err := s.client.Get(ctx, path, &analysis); err != nil {
		return nil, err
	}
	
	return &analysis, nil
}

// Cancel cancels a running analysis
func (s *AnalysisService) Cancel(ctx context.Context, analysisID string) error {
	path := fmt.Sprintf("/analysis/%s/cancel", analysisID)
	return s.client.Post(ctx, path, nil, nil)
}

// WaitForCompletion waits for an analysis to complete
func (s *AnalysisService) WaitForCompletion(ctx context.Context, analysisID string, pollInterval time.Duration) (*types.Analysis, error) {
	if pollInterval == 0 {
		pollInterval = 2 * time.Second
	}
	
	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ticker.C:
			analysis, err := s.Get(ctx, analysisID)
			if err != nil {
				return nil, err
			}
			
			switch analysis.Status {
			case types.AnalysisStatusCompleted, types.AnalysisStatusFailed, types.AnalysisStatusCancelled:
				return analysis, nil
			}
		}
	}
}

// StreamProgress streams analysis progress updates
func (s *AnalysisService) StreamProgress(ctx context.Context, analysisID string, pollInterval time.Duration) (<-chan *types.Analysis, <-chan error) {
	if pollInterval == 0 {
		pollInterval = 1 * time.Second
	}
	
	analysisChan := make(chan *types.Analysis)
	errorChan := make(chan error, 1)
	
	go func() {
		defer close(analysisChan)
		defer close(errorChan)
		
		ticker := time.NewTicker(pollInterval)
		defer ticker.Stop()
		
		var lastStatus types.AnalysisStatus
		
		for {
			select {
			case <-ctx.Done():
				errorChan <- ctx.Err()
				return
			case <-ticker.C:
				analysis, err := s.Get(ctx, analysisID)
				if err != nil {
					errorChan <- err
					return
				}
				
				// Send update if status changed or if still running
				if analysis.Status != lastStatus || analysis.Status == types.AnalysisStatusRunning {
					select {
					case analysisChan <- analysis:
						lastStatus = analysis.Status
					case <-ctx.Done():
						errorChan <- ctx.Err()
						return
					}
				}
				
				// Stop if completed
				if analysis.Status == types.AnalysisStatusCompleted ||
					analysis.Status == types.AnalysisStatusFailed ||
					analysis.Status == types.AnalysisStatusCancelled {
					return
				}
			}
		}
	}()
	
	return analysisChan, errorChan
}

// services/patterns.go
package ccl

import (
	"context"
	"fmt"

	"github.com/ccl-platform/ccl-go/types"
)

// PatternService handles pattern operations
type PatternService struct {
	client *Client
}

// ValidateCode validates code against patterns
func (s *PatternService) ValidateCode(ctx context.Context, request *types.ValidateCodeRequest) (*types.ValidationResult, error) {
	path := "/patterns/validate"
	
	var result types.ValidationResult
	if err := s.client.Post(ctx, path, request, &result); err != nil {
		return nil, err
	}
	
	return &result, nil
}

// Get retrieves a pattern by ID
func (s *PatternService) Get(ctx context.Context, patternID string) (*types.Pattern, error) {
	path := fmt.Sprintf("/patterns/%s", patternID)
	
	var pattern types.Pattern
	if err := s.client.Get(ctx, path, &pattern); err != nil {
		return nil, err
	}
	
	return &pattern, nil
}

// List retrieves a list of patterns
func (s *PatternService) List(ctx context.Context, filter *types.PatternFilter, pagination *types.PaginationOptions) (*types.PaginatedResponse[types.Pattern], error) {
	path := "/patterns"
	
	// Build query parameters (similar to repositories.go)
	// ... query parameter building logic
	
	var result types.PaginatedResponse[types.Pattern]
	if err := s.client.Get(ctx, path, &result); err != nil {
		return nil, err
	}
	
	return &result, nil
}
```

### Error Handling

```go
// errors/errors.go
package errors

import (
	"fmt"
	"time"
)

// Error codes
const (
	ErrCodeUnknown        = "UNKNOWN_ERROR"
	ErrCodeInvalidConfig  = "INVALID_CONFIG"
	ErrCodeRequest        = "REQUEST_ERROR"
	ErrCodeResponse       = "RESPONSE_ERROR"
	ErrCodeNetwork        = "NETWORK_ERROR"
	ErrCodeSerialization  = "SERIALIZATION_ERROR"
	ErrCodeAPI            = "API_ERROR"
	ErrCodeAuthentication = "AUTHENTICATION_ERROR"
	ErrCodeAuthorization  = "AUTHORIZATION_ERROR"
	ErrCodeNotFound       = "NOT_FOUND_ERROR"
	ErrCodeValidation     = "VALIDATION_ERROR"
	ErrCodeRateLimit      = "RATE_LIMIT_ERROR"
	ErrCodeInternal       = "INTERNAL_ERROR"
	ErrCodeMiddleware     = "MIDDLEWARE_ERROR"
)

// CCLError represents a CCL SDK error
type CCLError struct {
	Code       string
	Message    string
	Details    interface{}
	Cause      error
	StatusCode int
}

func (e *CCLError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func (e *CCLError) Unwrap() error {
	return e.Cause
}

// New creates a new CCL error
func New(code, message string) *CCLError {
	return &CCLError{
		Code:    code,
		Message: message,
	}
}

// Wrap wraps an existing error with CCL error information
func Wrap(err error, code, message string) *CCLError {
	return &CCLError{
		Code:    code,
		Message: message,
		Cause:   err,
	}
}

// Specific error types
type AuthenticationError struct {
	*CCLError
}

func NewAuthenticationError(message string) *AuthenticationError {
	return &AuthenticationError{
		CCLError: &CCLError{
			Code:       ErrCodeAuthentication,
			Message:    message,
			StatusCode: 401,
		},
	}
}

type AuthorizationError struct {
	*CCLError
}

func NewAuthorizationError(message string) *AuthorizationError {
	return &AuthorizationError{
		CCLError: &CCLError{
			Code:       ErrCodeAuthorization,
			Message:    message,
			StatusCode: 403,
		},
	}
}

type NotFoundError struct {
	*CCLError
}

func NewNotFoundError(message string) *NotFoundError {
	return &NotFoundError{
		CCLError: &CCLError{
			Code:       ErrCodeNotFound,
			Message:    message,
			StatusCode: 404,
		},
	}
}

type ValidationError struct {
	*CCLError
	FieldErrors []FieldError
}

type FieldError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

func NewValidationError(message string, details interface{}) *ValidationError {
	return &ValidationError{
		CCLError: &CCLError{
			Code:       ErrCodeValidation,
			Message:    message,
			Details:    details,
			StatusCode: 400,
		},
	}
}

type RateLimitError struct {
	*CCLError
	RetryAfter time.Duration
}

func NewRateLimitError(message string, retryAfter time.Duration) *RateLimitError {
	return &RateLimitError{
		CCLError: &CCLError{
			Code:       ErrCodeRateLimit,
			Message:    message,
			StatusCode: 429,
		},
		RetryAfter: retryAfter,
	}
}

type InternalError struct {
	*CCLError
}

func NewInternalError(message string) *InternalError {
	return &InternalError{
		CCLError: &CCLError{
			Code:       ErrCodeInternal,
			Message:    message,
			StatusCode: 500,
		},
	}
}
```

### Middleware and Utilities

```go
// middleware.go
package ccl

import (
	"bytes"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"
)

// Middleware interface for request/response processing
type Middleware interface {
	ProcessRequest(req *http.Request) error
	ProcessResponse(resp *http.Response, body []byte) error
}

// LoggingMiddleware logs requests and responses
type LoggingMiddleware struct {
	Logger *slog.Logger
}

func NewLoggingMiddleware(logger *slog.Logger) *LoggingMiddleware {
	if logger == nil {
		logger = slog.Default()
	}
	return &LoggingMiddleware{Logger: logger}
}

func (m *LoggingMiddleware) ProcessRequest(req *http.Request) error {
	m.Logger.Info("HTTP Request",
		"method", req.Method,
		"url", req.URL.String(),
		"headers", req.Header,
	)
	return nil
}

func (m *LoggingMiddleware) ProcessResponse(resp *http.Response, body []byte) error {
	m.Logger.Info("HTTP Response",
		"status", resp.Status,
		"status_code", resp.StatusCode,
		"headers", resp.Header,
		"body_size", len(body),
	)
	return nil
}

// RetryMiddleware handles automatic retries
type RetryMiddleware struct {
	MaxRetries int
	BackoffFunc func(attempt int) time.Duration
}

func NewRetryMiddleware(maxRetries int) *RetryMiddleware {
	return &RetryMiddleware{
		MaxRetries: maxRetries,
		BackoffFunc: func(attempt int) time.Duration {
			return time.Duration(attempt) * time.Second
		},
	}
}

func (m *RetryMiddleware) ProcessRequest(req *http.Request) error {
	// Store request body for potential retries
	if req.Body != nil {
		body, err := io.ReadAll(req.Body)
		if err != nil {
			return err
		}
		req.Body = io.NopCloser(bytes.NewReader(body))
		// Store original body in context for retries
		// Implementation would need context value storage
	}
	return nil
}

func (m *RetryMiddleware) ProcessResponse(resp *http.Response, body []byte) error {
	// This would be called by a retry-aware client
	return nil
}

// webhooks/verifier.go
package webhooks

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// Verifier handles webhook signature verification
type Verifier struct {
	secret []byte
}

// NewVerifier creates a new webhook verifier
func NewVerifier(secret string) *Verifier {
	return &Verifier{
		secret: []byte(secret),
	}
}

// Verify verifies a webhook signature
func (v *Verifier) Verify(payload []byte, signature, timestamp string) bool {
	// Parse timestamp
	ts, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		return false
	}
	
	// Check timestamp is within tolerance (5 minutes)
	now := time.Now().Unix()
	if abs(now-ts) > 300 {
		return false
	}
	
	// Create signature payload
	signaturePayload := fmt.Sprintf("%d.%s", ts, string(payload))
	
	// Compute expected signature
	h := hmac.New(sha256.New, v.secret)
	h.Write([]byte(signaturePayload))
	expectedSignature := "sha256=" + hex.EncodeToString(h.Sum(nil))
	
	// Compare signatures
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

// VerifyHTTPRequest verifies a webhook from HTTP headers
func (v *Verifier) VerifyHTTPRequest(payload []byte, headers map[string][]string) bool {
	signature := getHeader(headers, "X-CCL-Signature-256")
	timestamp := getHeader(headers, "X-CCL-Timestamp")
	
	if signature == "" || timestamp == "" {
		return false
	}
	
	return v.Verify(payload, signature, timestamp)
}

func getHeader(headers map[string][]string, key string) string {
	values := headers[key]
	if len(values) == 0 {
		return ""
	}
	return values[0]
}
```

### Usage Examples

```go
// examples/main.go
package main

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"time"

	ccl "github.com/ccl-platform/ccl-go"
	"github.com/ccl-platform/ccl-go/types"
)

func main() {
	ctx := context.Background()
	
	// Create client with options
	client, err := ccl.NewClient(
		ccl.WithAPIKey("your-api-key"),
		ccl.WithTimeout(30*time.Second),
		ccl.WithMiddleware(
			ccl.NewLoggingMiddleware(slog.Default()),
			ccl.NewRetryMiddleware(3),
		),
		ccl.WithDebug(true),
	)
	if err != nil {
		log.Fatal(err)
	}
	
	// Example: Repository operations
	if err := repositoryExample(ctx, client); err != nil {
		log.Printf("Repository example failed: %v", err)
	}
	
	// Example: Analysis operations
	if err := analysisExample(ctx, client); err != nil {
		log.Printf("Analysis example failed: %v", err)
	}
	
	// Example: Pattern validation
	if err := patternExample(ctx, client); err != nil {
		log.Printf("Pattern example failed: %v", err)
	}
}

func repositoryExample(ctx context.Context, client *ccl.Client) error {
	// Create repository
	createReq := &types.CreateRepositoryRequest{
		Name:        "example-repo",
		URL:         "https://github.com/example/repo",
		Provider:    types.RepositoryProviderGitHub,
		AutoAnalyze: true,
	}
	
	repo, err := client.Repositories.Create(ctx, createReq)
	if err != nil {
		return fmt.Errorf("failed to create repository: %w", err)
	}
	
	fmt.Printf("Created repository: %s (ID: %s)\n", repo.Name, repo.ID)
	
	// List repositories
	filter := &types.RepositoryFilter{
		Provider: &types.RepositoryProviderGitHub,
	}
	pagination := &types.PaginationOptions{
		Limit: 10,
	}
	
	repos, err := client.Repositories.List(ctx, filter, pagination)
	if err != nil {
		return fmt.Errorf("failed to list repositories: %w", err)
	}
	
	fmt.Printf("Found %d repositories\n", len(repos.Data))
	
	return nil
}

func analysisExample(ctx context.Context, client *ccl.Client) error {
	// Start analysis
	analysisReq := &types.StartAnalysisRequest{
		RepositoryID: "repo-123",
		Branch:       "main",
		Options: &types.AnalysisOptions{
			Incremental:         false,
			DeepPatternAnalysis: true,
			GenerateEmbeddings:  true,
		},
	}
	
	analysis, err := client.Analysis.Start(ctx, analysisReq)
	if err != nil {
		return fmt.Errorf("failed to start analysis: %w", err)
	}
	
	fmt.Printf("Started analysis: %s\n", analysis.ID)
	
	// Stream progress updates
	progressChan, errorChan := client.Analysis.StreamProgress(ctx, analysis.ID, 2*time.Second)
	
	for {
		select {
		case update := <-progressChan:
			if update == nil {
				fmt.Println("Analysis stream closed")
				return nil
			}
			fmt.Printf("Analysis status: %s\n", update.Status)
			
			if update.Status == types.AnalysisStatusCompleted {
				fmt.Printf("Analysis completed in %d seconds\n", *update.Duration)
				if update.Statistics != nil {
					fmt.Printf("Files analyzed: %d\n", update.Statistics.TotalFiles)
				}
				return nil
			} else if update.Status == types.AnalysisStatusFailed {
				if update.Error != nil {
					fmt.Printf("Analysis failed: %s\n", update.Error.Message)
				}
				return nil
			}
			
		case err := <-errorChan:
			if err != nil {
				return fmt.Errorf("analysis progress error: %w", err)
			}
			
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func patternExample(ctx context.Context, client *ccl.Client) error {
	// Validate code against patterns
	validateReq := &types.ValidateCodeRequest{
		RepositoryID: "repo-123",
		Code: `
func getUserData(id string) (*User, error) {
    db := openDB()
    defer db.Close()
    return db.Query("SELECT * FROM users WHERE id = " + id)
}`,
		FilePath: "user.go",
		Language: "go",
	}
	
	result, err := client.Patterns.ValidateCode(ctx, validateReq)
	if err != nil {
		return fmt.Errorf("failed to validate code: %w", err)
	}
	
	if !result.Valid {
		fmt.Printf("Code validation failed with %d violations:\n", len(result.Violations))
		for _, violation := range result.Violations {
			fmt.Printf("- %s: %s (line %d)\n", violation.PatternName, violation.Message, violation.Line)
		}
	} else {
		fmt.Println("Code validation passed!")
	}
	
	return nil
}

// examples/pattern_library_manager.go
type PatternManager struct {
    client *ccl.Client
}

func NewPatternManager(apiKey string) *PatternManager {
    client, _ := ccl.NewClient(ccl.WithAPIKey(apiKey))
    return &PatternManager{client: client}
}

// DiscoverPatterns finds patterns in a repository
func (pm *PatternManager) DiscoverPatterns(repoID string) error {
    // Trigger analysis
    analysis, err := pm.client.Analysis.Start(context.Background(), &types.StartAnalysisRequest{
        RepositoryID: repoID,
        Options: &types.AnalysisOptions{
            DeepPatternAnalysis: true,
        },
    })
    if err != nil {
        return fmt.Errorf("analysis failed: %w", err)
    }
    
    // Wait for completion
    if _, err := pm.client.Analysis.WaitForCompletion(context.Background(), analysis.ID, 5*time.Second); err != nil {
        return fmt.Errorf("analysis wait failed: %w", err)
    }
    
    // Get discovered patterns
    patterns, err := pm.client.Patterns.List(context.Background(), &types.PatternFilter{
        RepositoryID: &repoID,
        MinConfidence: float64(0.8),
    }, nil)
    if err != nil {
        return fmt.Errorf("pattern list failed: %w", err)
    }
    
    fmt.Printf("Discovered %d patterns:\n", len(patterns.Data))
    for _, pattern := range patterns.Data {
        fmt.Printf("- %s (confidence: %.2f)\n", 
            pattern.Name, 
            pattern.Confidence,
        )
    }
    
    return nil
}
```

## Validation Loop

### Level 1: Basic Functionality Testing
```go
// client_test.go
package ccl_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	ccl "github.com/ccl-platform/ccl-go"
	"github.com/ccl-platform/ccl-go/types"
)

func TestClientCreation(t *testing.T) {
	client, err := ccl.NewClient(
		ccl.WithAPIKey("test-key"),
		ccl.WithBaseURL("https://api.test.ccl.dev"),
	)
	
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	
	if client == nil {
		t.Fatal("Client is nil")
	}
}

func TestRepositoryOperations(t *testing.T) {
	// Create test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/repositories":
			if r.Method == http.MethodPost {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusCreated)
				w.Write([]byte(`{
					"id": "repo-123",
					"name": "test-repo",
					"url": "https://github.com/test/repo",
					"provider": "github",
					"owner_id": "user-123",
					"language": "go",
					"is_private": false,
					"created_at": "2025-01-01T00:00:00Z",
					"updated_at": "2025-01-01T00:00:00Z"
				}`))
			}
		}
	}))
	defer server.Close()
	
	client, err := ccl.NewClient(
		ccl.WithAPIKey("test-key"),
		ccl.WithBaseURL(server.URL),
	)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	
	// Test create repository
	createReq := &types.CreateRepositoryRequest{
		Name:     "test-repo",
		URL:      "https://github.com/test/repo",
		Provider: types.RepositoryProviderGitHub,
	}
	
	repo, err := client.Repositories.Create(context.Background(), createReq)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}
	
	if repo.ID != "repo-123" {
		t.Errorf("Expected repository ID 'repo-123', got '%s'", repo.ID)
	}
}

func TestErrorHandling(t *testing.T) {
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		w.Write([]byte(`{
			"code": "AUTHENTICATION_ERROR",
			"message": "Invalid API key"
		}`))
	}))
	defer server.Close()
	
	client, err := ccl.NewClient(
		ccl.WithAPIKey("invalid-key"),
		ccl.WithBaseURL(server.URL),
	)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	
	_, err = client.Repositories.List(context.Background(), nil, nil)
	if err == nil {
		t.Fatal("Expected error, got nil")
	}
	
	// Check if it's an authentication error
	var authErr *ccl.AuthenticationError
	if !errors.As(err, &authErr) {
		t.Errorf("Expected AuthenticationError, got %T", err)
	}
}
```

### Level 2: Integration Testing
```go
// integration_test.go
//go:build integration

package ccl_test

import (
	"context"
	"os"
	"testing"
	"time"

	ccl "github.com/ccl-platform/ccl-go"
	"github.com/ccl-platform/ccl-go/types"
)

func TestFullWorkflow(t *testing.T) {
	apiKey := os.Getenv("CCL_TEST_API_KEY")
	if apiKey == "" {
		t.Skip("CCL_TEST_API_KEY not set")
	}
	
	client, err := ccl.NewClient(ccl.WithAPIKey(apiKey))
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	
	ctx := context.Background()
	
	// Create test repository
	createReq := &types.CreateRepositoryRequest{
		Name:     "integration-test-repo",
		URL:      "https://github.com/octocat/Hello-World",
		Provider: types.RepositoryProviderGitHub,
	}
	
	repo, err := client.Repositories.Create(ctx, createReq)
	if err != nil {
		t.Fatalf("Failed to create repository: %v", err)
	}
	
	defer func() {
		// Clean up
		if err := client.Repositories.Delete(ctx, repo.ID); err != nil {
			t.Errorf("Failed to delete repository: %v", err)
		}
	}()
	
	// Start analysis
	analysisReq := &types.StartAnalysisRequest{
		RepositoryID: repo.ID,
		Branch:       "main",
	}
	
	analysis, err := client.Analysis.Start(ctx, analysisReq)
	if err != nil {
		t.Fatalf("Failed to start analysis: %v", err)
	}
	
	// Wait for completion with timeout
	ctx, cancel := context.WithTimeout(ctx, 5*time.Minute)
	defer cancel()
	
	completed, err := client.Analysis.WaitForCompletion(ctx, analysis.ID, 2*time.Second)
	if err != nil {
		t.Fatalf("Failed to wait for analysis: %v", err)
	}
	
	if completed.Status != types.AnalysisStatusCompleted {
		t.Errorf("Expected completed status, got %s", completed.Status)
	}
	
	// Validate code
	validateReq := &types.ValidateCodeRequest{
		RepositoryID: repo.ID,
		Code:         "package main\n\nfunc main() {\n\tprintln(\"Hello, World!\")\n}",
		FilePath:     "main.go",
		Language:     "go",
	}
	
	result, err := client.Patterns.ValidateCode(ctx, validateReq)
	if err != nil {
		t.Fatalf("Failed to validate code: %v", err)
	}
	
	if result == nil {
		t.Fatal("Validation result is nil")
	}
}
```

### Level 3: Performance and Benchmark Testing
```go
// benchmark_test.go
package ccl_test

import (
	"context"
	"sync"
	"testing"

	ccl "github.com/ccl-platform/ccl-go"
)

func BenchmarkConcurrentRequests(b *testing.B) {
	client, err := ccl.NewClient(ccl.WithAPIKey("test-key"))
	if err != nil {
		b.Fatalf("Failed to create client: %v", err)
	}
	
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := client.Repositories.List(context.Background(), nil, nil)
			if err != nil {
				b.Errorf("Request failed: %v", err)
			}
		}
	})
}

func TestConcurrentSafety(t *testing.T) {
	client, err := ccl.NewClient(ccl.WithAPIKey("test-key"))
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	
	const numGoroutines = 100
	var wg sync.WaitGroup
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			
			_, err := client.Repositories.List(context.Background(), nil, nil)
			if err != nil {
				t.Errorf("Concurrent request failed: %v", err)
			}
		}()
	}
	
	wg.Wait()
}
```

## Final Validation Checklist

- [ ] Idiomatic Go code structure
- [ ] Full context.Context support implemented
- [ ] Structured error types with wrapping
- [ ] HTTP/2 and connection pooling configured
- [ ] Middleware system for extensibility
- [ ] Webhook verification utilities
- [ ] Comprehensive test coverage
- [ ] Integration tests passing
- [ ] Performance benchmarks
- [ ] Documentation and examples

## Anti-Patterns to Avoid

1. **DON'T ignore context.Context** - Essential for cancellation and timeouts
2. **DON'T panic in library code** - Return errors instead
3. **DON'T block indefinitely** - Always respect context cancellation
4. **DON'T leak goroutines** - Clean up properly
5. **DON'T ignore HTTP response body closure** - Always close bodies
6. **DON'T use global state** - Make everything configurable
7. **DON'T skip error wrapping** - Provide context with errors
8. **DON'T ignore HTTP/2** - Use modern transport features
9. **DON'T hardcode timeouts** - Make them configurable
10. **DON'T skip interface segregation** - Keep interfaces focused
