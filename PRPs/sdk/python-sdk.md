# Python SDK Implementation

name: "Python SDK Implementation"
description: |
  Comprehensive Python SDK for CCL platform providing type-safe client libraries with async/await support, dataclasses for type safety, and integration with popular Python frameworks.
  
  Core Principles:
  - **Type Safety**: Full type hints and mypy compatibility
  - **Async/Await**: Modern Python async support
  - **Pythonic API**: Follows Python conventions and idioms
  - **Framework Integration**: Support for Django, FastAPI, Flask
  - **Developer Experience**: Clear documentation and error messages

## Goal

Create a Pythonic SDK that provides seamless integration with CCL platform APIs while maintaining type safety, async support, and excellent developer experience for Python developers.

## Why

Python SDK enables:
- Backend API integrations
- Data science and ML workflows
- CI/CD pipeline integrations
- Django/FastAPI application development
- Automation scripts and tools

This provides:
- Reduced integration time
- Type safety with modern Python
- Async performance benefits
- Framework-specific helpers
- Comprehensive error handling

## What

### User-Visible Behavior
- Simple pip installation
- Intuitive async/sync APIs
- Type-safe interactions
- Framework-specific integrations
- Automatic token management

### Technical Requirements
- [ ] Python 3.8+ compatibility
- [ ] Full type hints and mypy support
- [ ] Async/await and sync API support
- [ ] Django and FastAPI integrations
- [ ] Automatic retries and error handling
- [ ] Streaming support for real-time features
- [ ] Webhook verification utilities
- [ ] Request/response logging

### Success Criteria
- [ ] Full type safety with mypy
- [ ] Async/sync API parity
- [ ] Framework integrations working
- [ ] Comprehensive documentation
- [ ] Zero breaking changes in minor versions

## All Needed Context

### Documentation & References
- url: https://docs.python.org/3/library/typing.html
  why: Python type hints best practices
- url: https://docs.aiohttp.org/en/stable/
  why: Async HTTP client patterns
- url: https://pydantic-docs.helpmanual.io/
  why: Data validation and serialization
- file: docs/guides/sdk-documentation.md
  why: Existing Python examples and patterns

### Package Structure

```yaml
Package Organization:
  core:
    - client
    - auth
    - types
    - errors
    - exceptions
    
  services:
    - repositories
    - analysis
    - patterns
    - queries
    - marketplace
    
  integrations:
    - django
    - fastapi
    - flask
    
  utils:
    - webhooks
    - retry
    - logging
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Handle asyncio event loop properly
- **CRITICAL**: Provide both sync and async APIs
- **GOTCHA**: Session cleanup in async contexts
- **GOTCHA**: SSL certificate verification issues
- **WARNING**: Memory usage with large file uploads
- **TIP**: Use pydantic for data validation
- **TIP**: Implement proper logging configuration

## Implementation Blueprint

### Core SDK Client

```python
# ccl_sdk/client.py
import asyncio
import json
import logging
from typing import Dict, List, Optional, Union, Any
from dataclasses import dataclass, field
from pathlib import Path
import aiohttp
import httpx
from pydantic import BaseModel, validator

from .auth import AuthManager
from .errors import CCLError, AuthenticationError, RateLimitError
from .retry import RetryManager
from .types import *

@dataclass
class CCLConfig:
    """Configuration for CCL client."""
    api_key: Optional[str] = None
    api_url: str = "https://api.ccl.dev/v1"
    timeout: int = 30
    max_retries: int = 3
    environment: str = "production"
    debug: bool = False
    
    def __post_init__(self):
        if not self.api_key:
            import os
            self.api_key = os.getenv('CCL_API_KEY')
        
        if not self.api_key:
            raise ValueError("API key is required. Set CCL_API_KEY environment variable or pass api_key parameter.")

class CCLClient:
    """Main CCL SDK client with async and sync support."""
    
    def __init__(self, config: Optional[CCLConfig] = None):
        self.config = config or CCLConfig()
        self._session: Optional[aiohttp.ClientSession] = None
        self._sync_session: Optional[httpx.Client] = None
        
        # Initialize managers
        self.auth = AuthManager(self.config)
        self.retry_manager = RetryManager(max_retries=self.config.max_retries)
        
        # Initialize service modules
        self.repositories = RepositoryService(self)
        self.analysis = AnalysisService(self)
        self.patterns = PatternService(self)
        self.queries = QueryService(self)
        self.marketplace = MarketplaceService(self)
        
        # Setup logging
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        self.logger = logging.getLogger('ccl_sdk')
        if self.config.debug:
            self.logger.setLevel(logging.DEBUG)
            if not self.logger.handlers:
                handler = logging.StreamHandler()
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                handler.setFormatter(formatter)
                self.logger.addHandler(handler)
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._get_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self._session:
            await self._session.close()
    
    def __enter__(self):
        """Sync context manager entry."""
        self._get_sync_session()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Sync context manager exit."""
        if self._sync_session:
            self._sync_session.close()
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create async HTTP session."""
        if self._session is None:
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            headers = {
                'Authorization': f'Bearer {self.config.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': f'ccl-python-sdk/{self._get_version()}'
            }
            self._session = aiohttp.ClientSession(
                base_url=self.config.api_url,
                timeout=timeout,
                headers=headers,
                connector=aiohttp.TCPConnector(limit=100),
                raise_for_status=False
            )
        return self._session
    
    def _get_sync_session(self) -> httpx.Client:
        """Get or create sync HTTP session."""
        if self._sync_session is None:
            headers = {
                'Authorization': f'Bearer {self.config.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': f'ccl-python-sdk/{self._get_version()}'
            }
            self._sync_session = httpx.Client(
                base_url=self.config.api_url,
                timeout=self.config.timeout,
                headers=headers,
                limits=httpx.Limits(max_connections=100)
            )
        return self._sync_session
    
    async def request(
        self,
        method: str,
        path: str,
        *,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
        timeout: Optional[int] = None
    ) -> Dict[str, Any]:
        """Make async HTTP request."""
        session = await self._get_session()
        
        # Merge headers
        request_headers = session.headers.copy()
        if headers:
            request_headers.update(headers)
        
        # Add request ID for tracing
        request_headers['X-Request-ID'] = self._generate_request_id()
        
        # Setup retry wrapper
        async def _make_request():
            async with session.request(
                method=method,
                url=path,
                json=data,
                params=params,
                headers=request_headers,
                timeout=aiohttp.ClientTimeout(total=timeout or self.config.timeout)
            ) as response:
                response_data = await response.json() if response.content_type == 'application/json' else await response.text()
                
                if response.status >= 400:
                    self._handle_error_response(response.status, response_data)
                
                self.logger.debug(f"{method} {path} -> {response.status}")
                return response_data
        
        return await self.retry_manager.retry_async(_make_request)
    
    def request_sync(
        self,
        method: str,
        path: str,
        *,
        data: Optional[Dict] = None,
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
        timeout: Optional[int] = None
    ) -> Dict[str, Any]:
        """Make sync HTTP request."""
        session = self._get_sync_session()
        
        # Merge headers
        request_headers = session.headers.copy()
        if headers:
            request_headers.update(headers)
        
        # Add request ID for tracing
        request_headers['X-Request-ID'] = self._generate_request_id()
        
        # Setup retry wrapper
        def _make_request():
            response = session.request(
                method=method,
                url=path,
                json=data,
                params=params,
                headers=request_headers,
                timeout=timeout or self.config.timeout
            )
            
            response_data = response.json() if 'application/json' in response.headers.get('content-type', '') else response.text
            
            if response.status_code >= 400:
                self._handle_error_response(response.status_code, response_data)
            
            self.logger.debug(f"{method} {path} -> {response.status_code}")
            return response_data
        
        return self.retry_manager.retry_sync(_make_request)
    
    async def get(self, path: str, **kwargs) -> Dict[str, Any]:
        """Make async GET request."""
        return await self.request('GET', path, **kwargs)
    
    async def post(self, path: str, **kwargs) -> Dict[str, Any]:
        """Make async POST request."""
        return await self.request('POST', path, **kwargs)
    
    async def put(self, path: str, **kwargs) -> Dict[str, Any]:
        """Make async PUT request."""
        return await self.request('PUT', path, **kwargs)
    
    async def delete(self, path: str, **kwargs) -> Dict[str, Any]:
        """Make async DELETE request."""
        return await self.request('DELETE', path, **kwargs)
    
    def get_sync(self, path: str, **kwargs) -> Dict[str, Any]:
        """Make sync GET request."""
        return self.request_sync('GET', path, **kwargs)
    
    def post_sync(self, path: str, **kwargs) -> Dict[str, Any]:
        """Make sync POST request."""
        return self.request_sync('POST', path, **kwargs)
    
    def put_sync(self, path: str, **kwargs) -> Dict[str, Any]:
        """Make sync PUT request."""
        return self.request_sync('PUT', path, **kwargs)
    
    def delete_sync(self, path: str, **kwargs) -> Dict[str, Any]:
        """Make sync DELETE request."""
        return self.request_sync('DELETE', path, **kwargs)
    
    def _handle_error_response(self, status_code: int, response_data: Any):
        """Handle HTTP error responses."""
        if isinstance(response_data, dict):
            error_code = response_data.get('error', {}).get('code', 'UNKNOWN_ERROR')
            error_message = response_data.get('error', {}).get('message', 'Unknown error')
            error_details = response_data.get('error', {}).get('details')
        else:
            error_code = 'UNKNOWN_ERROR'
            error_message = str(response_data)
            error_details = None
        
        if status_code == 401:
            raise AuthenticationError(error_message)
        elif status_code == 403:
            raise AuthorizationError(error_message)
        elif status_code == 429:
            retry_after = None
            if isinstance(response_data, dict):
                retry_after = response_data.get('error', {}).get('details', {}).get('retry_after')
            raise RateLimitError(error_message, retry_after=retry_after)
        else:
            raise CCLError(error_message, code=error_code, status_code=status_code, details=error_details)
    
    def _generate_request_id(self) -> str:
        """Generate unique request ID."""
        import uuid
        return f"req_{uuid.uuid4().hex[:16]}"
    
    def _get_version(self) -> str:
        """Get SDK version."""
        try:
            import pkg_resources
            return pkg_resources.get_distribution('ccl-sdk').version
        except:
            return '1.0.0'

# Convenience function
def create_client(
    api_key: Optional[str] = None,
    api_url: str = "https://api.ccl.dev/v1",
    **kwargs
) -> CCLClient:
    """Create CCL client with configuration."""
    config = CCLConfig(api_key=api_key, api_url=api_url, **kwargs)
    return CCLClient(config)
```

### Type Definitions and Models

```python
# ccl_sdk/types.py
from datetime import datetime
from typing import List, Optional, Dict, Any, Union, Literal
from pydantic import BaseModel, Field, validator
from enum import Enum

class RepositoryProvider(str, Enum):
    """Repository provider types."""
    GITHUB = "github"
    GITLAB = "gitlab" 
    BITBUCKET = "bitbucket"
    LOCAL = "local"

class AnalysisStatus(str, Enum):
    """Analysis status types."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class PatternType(str, Enum):
    """Pattern types."""
    ARCHITECTURAL = "architectural"
    DESIGN = "design"
    IDIOM = "idiom"
    ANTI_PATTERN = "anti-pattern"
    SECURITY = "security"
    PERFORMANCE = "performance"

class MessageRole(str, Enum):
    """Message roles in conversations."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

# Base models
class BaseModel(BaseModel):
    """Base model with common configuration."""
    
    class Config:
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"

class TimestampedModel(CCLBaseModel):
    """Base model with timestamps."""
    created_at: datetime
    updated_at: datetime

# Repository models
class Repository(TimestampedModel):
    """Repository model."""
    id: str
    name: str
    url: str
    provider: RepositoryProvider
    owner_id: str
    organization_id: Optional[str] = None
    description: Optional[str] = None
    language: str
    is_private: bool = False
    last_analyzed_at: Optional[datetime] = None

class CreateRepositoryRequest(CCLBaseModel):
    """Request to create a repository."""
    name: str = Field(..., min_length=1, max_length=100)
    url: str = Field(..., regex=r'^https?://.+')
    provider: RepositoryProvider
    organization_id: Optional[str] = None
    auto_analyze: bool = True
    
    @validator('url')
    def validate_url(cls, v):
        # Additional URL validation can go here
        return v

# Analysis models
class AnalysisOptions(CCLBaseModel):
    """Analysis configuration options."""
    incremental: bool = False
    deep_pattern_analysis: bool = True
    generate_embeddings: bool = True
    include_paths: List[str] = Field(default_factory=list)
    exclude_paths: List[str] = Field(default_factory=list)
    pattern_confidence_threshold: float = Field(default=0.8, ge=0.0, le=1.0)

class AnalysisStatistics(CCLBaseModel):
    """Analysis statistics."""
    total_files: int
    total_lines: int
    language_breakdown: Dict[str, int]
    complexity: float
    maintainability_index: float
    technical_debt: float

class AnalysisError(CCLBaseModel):
    """Analysis error details."""
    code: str
    message: str
    details: Optional[Dict[str, Any]] = None

class Analysis(TimestampedModel):
    """Analysis model."""
    id: str
    repository_id: str
    status: AnalysisStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    duration: Optional[int] = None  # seconds
    commit: Optional[str] = None
    branch: str = "main"
    statistics: Optional[AnalysisStatistics] = None
    error: Optional[AnalysisError] = None

class StartAnalysisRequest(CCLBaseModel):
    """Request to start analysis."""
    repository_id: str
    branch: str = "main"
    commit: Optional[str] = None
    options: Optional[AnalysisOptions] = None

# Pattern models
class PatternLocation(CCLBaseModel):
    """Pattern location in code."""
    file: str
    start_line: int = Field(..., ge=1)
    end_line: int = Field(..., ge=1)
    snippet: str
    
    @validator('end_line')
    def validate_end_line(cls, v, values):
        if 'start_line' in values and v < values['start_line']:
            raise ValueError('end_line must be >= start_line')
        return v

class PatternTemplate(CCLBaseModel):
    """Pattern template definition."""
    code: str
    variables: List[str] = Field(default_factory=list)
    language: str

class Pattern(TimestampedModel):
    """Pattern model."""
    id: str
    repository_id: str
    analysis_id: str
    type: PatternType
    name: str
    description: str
    confidence: float = Field(..., ge=0.0, le=1.0)
    occurrences: int = Field(..., ge=0)
    locations: List[PatternLocation]
    template: PatternTemplate
    quality_score: float = Field(..., ge=0.0, le=1.0)
    tags: List[str] = Field(default_factory=list)

# Query models
class QueryContext(CCLBaseModel):
    """Query context configuration."""
    focus_area: Optional[str] = None
    include_code: bool = True
    max_examples: int = Field(default=3, ge=0, le=10)

class QueryRequest(CCLBaseModel):
    """Query request."""
    query: str = Field(..., min_length=1, max_length=1000)
    repository_id: Optional[str] = None
    conversation_id: Optional[str] = None
    context: Optional[QueryContext] = None
    stream: bool = False
    model: Literal["fast", "advanced"] = "advanced"

class QuerySource(CCLBaseModel):
    """Query response source."""
    type: Literal["file", "pattern", "documentation"]
    title: str
    content: str
    url: Optional[str] = None
    relevance_score: float = Field(..., ge=0.0, le=1.0)

class CodeExample(CCLBaseModel):
    """Code example in query response."""
    language: str
    code: str
    file: str
    start_line: int = Field(..., ge=1)
    end_line: int = Field(..., ge=1)
    explanation: str

class PatternReference(CCLBaseModel):
    """Pattern reference in query response."""
    pattern_id: str
    name: str
    relevance_score: float = Field(..., ge=0.0, le=1.0)

class QueryResponse(CCLBaseModel):
    """Query response."""
    answer: str
    confidence: float = Field(..., ge=0.0, le=1.0)
    conversation_id: str
    sources: List[QuerySource] = Field(default_factory=list)
    code_examples: List[CodeExample] = Field(default_factory=list)
    patterns_referenced: List[PatternReference] = Field(default_factory=list)
    suggested_questions: List[str] = Field(default_factory=list)

# Conversation models
class Message(TimestampedModel):
    """Conversation message."""
    id: str
    conversation_id: str
    role: MessageRole
    content: str
    confidence: Optional[float] = None
    sources: List[QuerySource] = Field(default_factory=list)

class Conversation(TimestampedModel):
    """Conversation model."""
    id: str
    repository_id: Optional[str] = None
    user_id: str
    title: Optional[str] = None
    message_count: int = 0
    last_message_at: datetime

# Pagination models
class PaginationInfo(CCLBaseModel):
    """Pagination information."""
    total: int = Field(..., ge=0)
    limit: int = Field(..., ge=1, le=100)
    offset: int = Field(..., ge=0)
    has_more: bool
    next_cursor: Optional[str] = None
    prev_cursor: Optional[str] = None

class PaginatedResponse(CCLBaseModel, Generic[T]):
    """Generic paginated response."""
    data: List[T]
    pagination: PaginationInfo

# Filter models
class RepositoryFilter(CCLBaseModel):
    """Repository filter options."""
    provider: Optional[RepositoryProvider] = None
    language: Optional[str] = None
    has_analysis: Optional[bool] = None
    organization_id: Optional[str] = None
    search: Optional[str] = None

class PatternFilter(CCLBaseModel):
    """Pattern filter options."""
    type: Optional[PatternType] = None
    min_confidence: Optional[float] = Field(None, ge=0.0, le=1.0)
    language: Optional[str] = None
    repository_id: Optional[str] = None
    analysis_id: Optional[str] = None
    search: Optional[str] = None

class PaginationOptions(CCLBaseModel):
    """Pagination options."""
    limit: int = Field(default=20, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    cursor: Optional[str] = None

# Type aliases for convenience
RepositoryList = PaginatedResponse[Repository]
AnalysisList = PaginatedResponse[Analysis]
PatternList = PaginatedResponse[Pattern]
ConversationList = PaginatedResponse[Conversation]
MessageList = PaginatedResponse[Message]
```

### Service Modules

```python
# ccl_sdk/services/repositories.py
from typing import Optional, List
from ..types import *
from ..client import CCLClient

class RepositoryService:
    """Repository service for managing repositories."""
    
    def __init__(self, client: CCLClient):
        self.client = client
    
    async def list(
        self,
        filter_options: Optional[RepositoryFilter] = None,
        pagination: Optional[PaginationOptions] = None
    ) -> RepositoryList:
        """List repositories with optional filtering and pagination."""
        params = {}
        
        if filter_options:
            params.update(filter_options.dict(exclude_none=True))
        
        if pagination:
            params.update(pagination.dict(exclude_none=True))
        
        response = await self.client.get('/repositories', params=params)
        return RepositoryList(**response)
    
    def list_sync(
        self,
        filter_options: Optional[RepositoryFilter] = None,
        pagination: Optional[PaginationOptions] = None
    ) -> RepositoryList:
        """Sync version of list repositories."""
        params = {}
        
        if filter_options:
            params.update(filter_options.dict(exclude_none=True))
        
        if pagination:
            params.update(pagination.dict(exclude_none=True))
        
        response = self.client.get_sync('/repositories', params=params)
        return RepositoryList(**response)
    
    async def get(self, repository_id: str) -> Repository:
        """Get repository by ID."""
        response = await self.client.get(f'/repositories/{repository_id}')
        return Repository(**response)
    
    def get_sync(self, repository_id: str) -> Repository:
        """Sync version of get repository."""
        response = self.client.get_sync(f'/repositories/{repository_id}')
        return Repository(**response)
    
    async def create(self, request: CreateRepositoryRequest) -> Repository:
        """Create a new repository."""
        response = await self.client.post('/repositories', data=request.dict())
        return Repository(**response)
    
    def create_sync(self, request: CreateRepositoryRequest) -> Repository:
        """Sync version of create repository."""
        response = self.client.post_sync('/repositories', data=request.dict())
        return Repository(**response)
    
    async def update(self, repository_id: str, updates: Dict[str, Any]) -> Repository:
        """Update repository."""
        response = await self.client.put(f'/repositories/{repository_id}', data=updates)
        return Repository(**response)
    
    def update_sync(self, repository_id: str, updates: Dict[str, Any]) -> Repository:
        """Sync version of update repository."""
        response = self.client.put_sync(f'/repositories/{repository_id}', data=updates)
        return Repository(**response)
    
    async def delete(self, repository_id: str) -> None:
        """Delete repository."""
        await self.client.delete(f'/repositories/{repository_id}')
    
    def delete_sync(self, repository_id: str) -> None:
        """Sync version of delete repository."""
        self.client.delete_sync(f'/repositories/{repository_id}')
    
    async def get_analyses(
        self,
        repository_id: str,
        pagination: Optional[PaginationOptions] = None
    ) -> AnalysisList:
        """Get analyses for repository."""
        params = pagination.dict(exclude_none=True) if pagination else {}
        response = await self.client.get(f'/repositories/{repository_id}/analyses', params=params)
        return AnalysisList(**response)
    
    async def get_patterns(
        self,
        repository_id: str,
        filter_options: Optional[PatternFilter] = None,
        pagination: Optional[PaginationOptions] = None
    ) -> PatternList:
        """Get patterns for repository."""
        params = {}
        
        if filter_options:
            params.update(filter_options.dict(exclude_none=True))
        
        if pagination:
            params.update(pagination.dict(exclude_none=True))
        
        response = await self.client.get(f'/repositories/{repository_id}/patterns', params=params)
        return PatternList(**response)

# ccl_sdk/services/analysis.py
from typing import Optional, AsyncGenerator
import asyncio
from ..types import *
from ..client import CCLClient

class AnalysisService:
    """Analysis service for managing code analysis."""
    
    def __init__(self, client: CCLClient):
        self.client = client
    
    async def start(self, request: StartAnalysisRequest) -> Analysis:
        """Start a new analysis."""
        response = await self.client.post('/analysis', data=request.dict())
        return Analysis(**response)
    
    def start_sync(self, request: StartAnalysisRequest) -> Analysis:
        """Sync version of start analysis."""
        response = self.client.post_sync('/analysis', data=request.dict())
        return Analysis(**response)
    
    async def get(self, analysis_id: str) -> Analysis:
        """Get analysis by ID."""
        response = await self.client.get(f'/analysis/{analysis_id}')
        return Analysis(**response)
    
    def get_sync(self, analysis_id: str) -> Analysis:
        """Sync version of get analysis."""
        response = self.client.get_sync(f'/analysis/{analysis_id}')
        return Analysis(**response)
    
    async def list(
        self,
        filter_options: Optional[Dict[str, Any]] = None,
        pagination: Optional[PaginationOptions] = None
    ) -> AnalysisList:
        """List analyses with optional filtering."""
        params = {}
        
        if filter_options:
            params.update(filter_options)
        
        if pagination:
            params.update(pagination.dict(exclude_none=True))
        
        response = await self.client.get('/analysis', params=params)
        return AnalysisList(**response)
    
    async def cancel(self, analysis_id: str) -> None:
        """Cancel running analysis."""
        await self.client.post(f'/analysis/{analysis_id}/cancel')
    
    async def wait_for_completion(
        self,
        analysis_id: str,
        poll_interval: float = 2.0,
        timeout: Optional[float] = None
    ) -> Analysis:
        """Wait for analysis to complete."""
        start_time = asyncio.get_event_loop().time()
        
        while True:
            analysis = await self.get(analysis_id)
            
            if analysis.status in [AnalysisStatus.COMPLETED, AnalysisStatus.FAILED, AnalysisStatus.CANCELLED]:
                return analysis
            
            if timeout and (asyncio.get_event_loop().time() - start_time) > timeout:
                raise TimeoutError(f"Analysis {analysis_id} did not complete within {timeout} seconds")
            
            await asyncio.sleep(poll_interval)
    
    async def stream_progress(
        self,
        analysis_id: str,
        poll_interval: float = 1.0
    ) -> AsyncGenerator[Analysis, None]:
        """Stream analysis progress updates."""
        last_status = None
        
        while True:
            try:
                analysis = await self.get(analysis_id)
                
                # Yield if status changed or if still running
                if analysis.status != last_status or analysis.status == AnalysisStatus.RUNNING:
                    yield analysis
                    last_status = analysis.status
                
                # Stop if completed
                if analysis.status in [AnalysisStatus.COMPLETED, AnalysisStatus.FAILED, AnalysisStatus.CANCELLED]:
                    break
                
                await asyncio.sleep(poll_interval)
                
            except Exception as e:
                self.client.logger.error(f"Error streaming analysis progress: {e}")
                break

# ccl_sdk/services/queries.py
from typing import AsyncGenerator, Optional
from ..types import *
from ..client import CCLClient

class QueryService:
    """Query service for natural language querying."""
    
    def __init__(self, client: CCLClient):
        self.client = client
    
    async def execute(self, request: QueryRequest) -> QueryResponse:
        """Execute a query."""
        response = await self.client.post('/query', data=request.dict())
        return QueryResponse(**response)
    
    def execute_sync(self, request: QueryRequest) -> QueryResponse:
        """Sync version of execute query."""
        response = self.client.post_sync('/query', data=request.dict())
        return QueryResponse(**response)
    
    async def execute_stream(
        self,
        request: QueryRequest,
        chunk_callback: Optional[callable] = None
    ) -> AsyncGenerator[str, QueryResponse]:
        """Execute streaming query."""
        # This would require SSE support - simplified implementation
        stream_request = request.copy()
        stream_request.stream = True
        
        # For now, simulate streaming by yielding chunks
        response = await self.execute(stream_request)
        
        # Simulate streaming by breaking answer into chunks
        words = response.answer.split()
        chunk_size = max(1, len(words) // 10)
        
        for i in range(0, len(words), chunk_size):
            chunk = ' '.join(words[i:i + chunk_size])
            if chunk_callback:
                chunk_callback(chunk)
            yield chunk
        
        return response
    
    async def get_conversations(
        self,
        filter_options: Optional[Dict[str, Any]] = None,
        pagination: Optional[PaginationOptions] = None
    ) -> ConversationList:
        """Get conversations."""
        params = {}
        
        if filter_options:
            params.update(filter_options)
        
        if pagination:
            params.update(pagination.dict(exclude_none=True))
        
        response = await self.client.get('/conversations', params=params)
        return ConversationList(**response)
    
    async def get_conversation(self, conversation_id: str) -> Conversation:
        """Get conversation by ID."""
        response = await self.client.get(f'/conversations/{conversation_id}')
        return Conversation(**response)
    
    async def get_messages(
        self,
        conversation_id: str,
        pagination: Optional[PaginationOptions] = None
    ) -> MessageList:
        """Get messages for conversation."""
        params = pagination.dict(exclude_none=True) if pagination else {}
        response = await self.client.get(f'/conversations/{conversation_id}/messages', params=params)
        return MessageList(**response)

# Convenience conversation class
class ConversationSession:
    """High-level conversation session."""
    
    def __init__(self, client: CCLClient, conversation_id: str):
        self.client = client
        self.conversation_id = conversation_id
        self.queries = QueryService(client)
    
    async def ask(self, question: str, **kwargs) -> QueryResponse:
        """Ask a question in this conversation."""
        request = QueryRequest(
            query=question,
            conversation_id=self.conversation_id,
            **kwargs
        )
        return await self.queries.execute(request)
    
    def ask_sync(self, question: str, **kwargs) -> QueryResponse:
        """Sync version of ask."""
        request = QueryRequest(
            query=question,
            conversation_id=self.conversation_id,
            **kwargs
        )
        return self.queries.execute_sync(request)
    
    async def get_history(self) -> List[Message]:
        """Get conversation history."""
        messages = await self.queries.get_messages(self.conversation_id)
        return messages.data
```

### Framework Integrations

```python
# ccl_sdk/integrations/django.py
from django.conf import settings
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
import json
import logging

from ..client import CCLClient, CCLConfig
from ..webhooks import WebhookVerifier

logger = logging.getLogger(__name__)

class CCLMixin:
    """Django mixin for CCL integration."""
    
    def get_ccl_client(self) -> CCLClient:
        """Get CCL client from Django settings."""
        config = CCLConfig(
            api_key=getattr(settings, 'CCL_API_KEY', None),
            api_url=getattr(settings, 'CCL_API_URL', 'https://api.ccl.dev/v1'),
            debug=getattr(settings, 'DEBUG', False)
        )
        return CCLClient(config)

@method_decorator(csrf_exempt, name='dispatch')
class CCLWebhookView(View):
    """Django view for handling CCL webhooks."""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.webhook_secret = getattr(settings, 'CCL_WEBHOOK_SECRET', None)
        if not self.webhook_secret:
            logger.warning("CCL_WEBHOOK_SECRET not configured")
    
    def post(self, request):
        """Handle webhook POST request."""
        try:
            # Verify webhook signature
            if self.webhook_secret:
                verifier = WebhookVerifier(self.webhook_secret)
                signature = request.META.get('HTTP_X_CCL_SIGNATURE_256')
                timestamp = request.META.get('HTTP_X_CCL_TIMESTAMP')
                
                if not verifier.verify(request.body, signature, timestamp):
                    return HttpResponse('Invalid signature', status=401)
            
            # Parse webhook payload
            payload = json.loads(request.body)
            event_type = payload.get('type')
            
            # Handle different event types
            if event_type == 'analysis.completed':
                return self.handle_analysis_completed(payload['data'])
            elif event_type == 'pattern.detected':
                return self.handle_pattern_detected(payload['data'])
            else:
                logger.info(f"Unhandled webhook event type: {event_type}")
            
            return JsonResponse({'status': 'success'})
            
        except Exception as e:
            logger.error(f"Webhook processing error: {e}")
            return HttpResponse('Processing error', status=500)
    
    def handle_analysis_completed(self, data):
        """Handle analysis completed event."""
        # Override in subclasses
        logger.info(f"Analysis completed: {data['analysis']['id']}")
        return JsonResponse({'status': 'handled'})
    
    def handle_pattern_detected(self, data):
        """Handle pattern detected event."""
        # Override in subclasses
        logger.info(f"Pattern detected: {data['pattern']['name']}")
        return JsonResponse({'status': 'handled'})

# ccl_sdk/integrations/fastapi.py
from fastapi import FastAPI, HTTPException, Depends, Header, Request
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from pydantic import BaseSettings
import asyncio

from ..client import CCLClient, CCLConfig
from ..webhooks import WebhookVerifier

class CCLSettings(BaseSettings):
    """CCL settings for FastAPI."""
    ccl_api_key: str
    ccl_api_url: str = "https://api.ccl.dev/v1"
    ccl_webhook_secret: str = None
    
    class Config:
        env_file = ".env"

# Global client instance
_ccl_client: CCLClient = None

def get_ccl_client() -> CCLClient:
    """FastAPI dependency for CCL client."""
    global _ccl_client
    if _ccl_client is None:
        settings = CCLSettings()
        config = CCLConfig(
            api_key=settings.ccl_api_key,
            api_url=settings.ccl_api_url
        )
        _ccl_client = CCLClient(config)
    return _ccl_client

async def verify_webhook_signature(
    request: Request,
    x_ccl_signature_256: str = Header(None),
    x_ccl_timestamp: str = Header(None)
):
    """FastAPI dependency for webhook verification."""
    settings = CCLSettings()
    
    if not settings.ccl_webhook_secret:
        return True  # Skip verification if not configured
    
    if not x_ccl_signature_256 or not x_ccl_timestamp:
        raise HTTPException(status_code=401, detail="Missing webhook headers")
    
    body = await request.body()
    verifier = WebhookVerifier(settings.ccl_webhook_secret)
    
    if not verifier.verify(body, x_ccl_signature_256, x_ccl_timestamp):
        raise HTTPException(status_code=401, detail="Invalid webhook signature")
    
    return True

# Example FastAPI integration
def create_ccl_app() -> FastAPI:
    """Create FastAPI app with CCL integration."""
    app = FastAPI(title="CCL Integration API")
    
    @app.post("/webhooks/ccl")
    async def handle_ccl_webhook(
        request: Request,
        verified: bool = Depends(verify_webhook_signature)
    ):
        """Handle CCL webhook events."""
        payload = await request.json()
        event_type = payload.get('type')
        
        if event_type == 'analysis.completed':
            # Handle analysis completion
            analysis_data = payload['data']['analysis']
            print(f"Analysis {analysis_data['id']} completed")
        
        return {"status": "success"}
    
    @app.get("/repositories")
    async def list_repositories(
        ccl: CCLClient = Depends(get_ccl_client)
    ):
        """List repositories via CCL API."""
        async with ccl:
            repositories = await ccl.repositories.list()
            return repositories.dict()
    
    @app.post("/analyze")
    async def start_analysis(
        repository_id: str,
        ccl: CCLClient = Depends(get_ccl_client)
    ):
        """Start repository analysis."""
        async with ccl:
            from ..types import StartAnalysisRequest
            request = StartAnalysisRequest(repository_id=repository_id)
            analysis = await ccl.analysis.start(request)
            return analysis.dict()
    
    return app
```

### Usage Examples and Documentation

```python
# examples/basic_usage.py
"""
Basic CCL SDK usage examples.
"""

import asyncio
from ccl_sdk import create_client, StartAnalysisRequest, CreateRepositoryRequest

async def main():
    # Create client
    async with create_client(api_key="your-api-key") as ccl:
        
        # Create repository
        repo_request = CreateRepositoryRequest(
            name="My Project",
            url="https://github.com/user/repo",
            provider="github"
        )
        repository = await ccl.repositories.create(repo_request)
        print(f"Created repository: {repository.id}")
        
        # Start analysis
        analysis_request = StartAnalysisRequest(
            repository_id=repository.id,
            branch="main"
        )
        analysis = await ccl.analysis.start(analysis_request)
        print(f"Started analysis: {analysis.id}")
        
        # Wait for completion
        completed_analysis = await ccl.analysis.wait_for_completion(analysis.id)
        print(f"Analysis status: {completed_analysis.status}")
        
        # Query the codebase
        from ccl_sdk.types import QueryRequest
        query_request = QueryRequest(
            query="How does authentication work?",
            repository_id=repository.id
        )
        response = await ccl.queries.execute(query_request)
        print(f"Answer: {response.answer}")

# Sync usage
def sync_example():
    """Synchronous usage example."""
    with create_client(api_key="your-api-key") as ccl:
        # List repositories
        repositories = ccl.repositories.list_sync()
        print(f"Found {len(repositories.data)} repositories")
        
        # Get specific repository
        if repositories.data:
            repo = ccl.repositories.get_sync(repositories.data[0].id)
            print(f"Repository: {repo.name}")

if __name__ == "__main__":
    asyncio.run(main())
    sync_example()

# examples/pattern_enforcement.py
from ccl import CCL
import sys
import json

class PatternEnforcer:
    def __init__(self, api_key: str, repo_id: str):
        self.ccl = CCL(api_key=api_key)
        self.repo_id = repo_id
    
    def check_patterns(self, changed_files: list) -> bool:
        """Check if changed files follow established patterns"""
        
        violations = []
        
        for file_path in changed_files:
            with open(file_path, 'r') as f:
                code = f.read()
            
            # Validate against patterns
            result = self.ccl.patterns.validate_code(
                repository_id=self.repo_id,
                code=code,
                file_path=file_path
            )
            
            if result['violations']:
                violations.append({
                    'file': file_path,
                    'violations': result['violations']
                })
        
        if violations:
            print("❌ Pattern violations found:")
            for v in violations:
                print(f"\n{v['file']}:")
                for violation in v['violations']:
                    print(f"  - {violation['message']}")
                    print(f"    Suggestion: {violation['suggestion']}")
            return False
        
        print("✅ All files follow established patterns!")
        return True
    
    def suggest_improvements(self, file_path: str) -> list:
        """Get AI-powered improvement suggestions"""
        
        with open(file_path, 'r') as f:
            code = f.read()
        
        response = self.ccl.query(
            query=f"Suggest improvements for this code based on the team's patterns",
            repository_id=self.repo_id,
            context={'code': code, 'file': file_path}
        )
        
        return response['suggestions']

# CI/CD Integration
if __name__ == "__main__":
    enforcer = PatternEnforcer(
        api_key=os.environ['CCL_API_KEY'],
        repo_id=os.environ['CCL_REPO_ID']
    )
    
    # Get changed files from git
    changed_files = sys.argv[1:]
    
    if not enforcer.check_patterns(changed_files):
        sys.exit(1)
```

## Validation Loop

### Level 1: Basic Functionality Testing
```python
# tests/test_client.py
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch

from ccl_sdk import create_client, CCLConfig
from ccl_sdk.types import Repository, CreateRepositoryRequest
from ccl_sdk.errors import AuthenticationError, RateLimitError

@pytest.mark.asyncio
async def test_create_client():
    """Test client creation."""
    client = create_client(api_key="test-key")
    assert client.config.api_key == "test-key"
    assert client.config.api_url == "https://api.ccl.dev/v1"

@pytest.mark.asyncio 
async def test_repository_operations():
    """Test repository CRUD operations."""
    with patch('aiohttp.ClientSession') as mock_session:
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            "id": "repo-123",
            "name": "test-repo",
            "url": "https://github.com/test/repo",
            "provider": "github",
            "owner_id": "user-123",
            "language": "python",
            "is_private": False,
            "created_at": "2025-01-01T00:00:00Z",
            "updated_at": "2025-01-01T00:00:00Z"
        }
        mock_session.return_value.__aenter__.return_value.request.return_value.__aenter__.return_value = mock_response
        
        async with create_client(api_key="test-key") as ccl:
            # Test create repository
            request = CreateRepositoryRequest(
                name="test-repo",
                url="https://github.com/test/repo",
                provider="github"
            )
            repo = await ccl.repositories.create(request)
            
            assert repo.id == "repo-123"
            assert repo.name == "test-repo"
            assert repo.provider == "github"

def test_sync_operations():
    """Test synchronous operations."""
    with patch('httpx.Client') as mock_client:
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "data": [],
            "pagination": {
                "total": 0,
                "limit": 20,
                "offset": 0,
                "has_more": False
            }
        }
        mock_client.return_value.request.return_value = mock_response
        
        with create_client(api_key="test-key") as ccl:
            repos = ccl.repositories.list_sync()
            assert len(repos.data) == 0
            assert repos.pagination.total == 0

@pytest.mark.asyncio
async def test_error_handling():
    """Test error handling."""
    with patch('aiohttp.ClientSession') as mock_session:
        mock_response = AsyncMock()
        mock_response.status = 401
        mock_response.json.return_value = {
            "error": {
                "code": "AUTHENTICATION_ERROR",
                "message": "Invalid API key"
            }
        }
        mock_session.return_value.__aenter__.return_value.request.return_value.__aenter__.return_value = mock_response
        
        async with create_client(api_key="invalid-key") as ccl:
            with pytest.raises(AuthenticationError):
                await ccl.repositories.list()
```

### Level 2: Integration Testing
```python
# tests/test_integration.py
import pytest
import os
from ccl_sdk import create_client
from ccl_sdk.types import CreateRepositoryRequest, StartAnalysisRequest

@pytest.mark.integration
@pytest.mark.asyncio
async def test_full_workflow():
    """Test complete workflow integration."""
    api_key = os.getenv('CCL_TEST_API_KEY')
    if not api_key:
        pytest.skip("CCL_TEST_API_KEY not set")
    
    async with create_client(api_key=api_key) as ccl:
        # Create test repository
        repo_request = CreateRepositoryRequest(
            name="integration-test-repo",
            url="https://github.com/octocat/Hello-World",
            provider="github"
        )
        
        repository = await ccl.repositories.create(repo_request)
        assert repository.id is not None
        
        try:
            # Start analysis
            analysis_request = StartAnalysisRequest(
                repository_id=repository.id
            )
            analysis = await ccl.analysis.start(analysis_request)
            assert analysis.status == "pending"
            
            # Wait for completion (with timeout)
            completed = await ccl.analysis.wait_for_completion(
                analysis.id,
                timeout=300  # 5 minutes
            )
            assert completed.status in ["completed", "failed"]
            
            if completed.status == "completed":
                # Query the analyzed codebase
                from ccl_sdk.types import QueryRequest
                query = QueryRequest(
                    query="What programming language is used?",
                    repository_id=repository.id
                )
                response = await ccl.queries.execute(query)
                assert response.answer is not None
                assert response.confidence > 0
                
        finally:
            # Clean up
            await ccl.repositories.delete(repository.id)
```

### Level 3: Performance Testing
```python
# tests/test_performance.py
import pytest
import asyncio
import time
from ccl_sdk import create_client

@pytest.mark.performance
@pytest.mark.asyncio
async def test_concurrent_requests():
    """Test concurrent request handling."""
    async with create_client(api_key="test-key") as ccl:
        # Mock responses
        with patch('aiohttp.ClientSession') as mock_session:
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json.return_value = {"data": [], "pagination": {"total": 0, "limit": 20, "offset": 0, "has_more": False}}
            mock_session.return_value.__aenter__.return_value.request.return_value.__aenter__.return_value = mock_response
            
            # Make 100 concurrent requests
            start_time = time.time()
            tasks = [ccl.repositories.list() for _ in range(100)]
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            assert len(results) == 100
            assert end_time - start_time < 5.0  # Should complete within 5 seconds

@pytest.mark.performance
def test_memory_usage():
    """Test memory usage with large responses."""
    # This would test memory efficiency with large datasets
    pass
```

## Final Validation Checklist

- [ ] Full type hints and mypy compatibility
- [ ] Async/sync API parity implemented
- [ ] Pydantic models for all data types
- [ ] Django and FastAPI integrations working
- [ ] Comprehensive error handling
- [ ] Webhook verification utilities
- [ ] Request/response logging
- [ ] Performance optimizations
- [ ] Integration tests passing
- [ ] Documentation complete

## Anti-Patterns to Avoid

1. **DON'T mix async and sync improperly** - Provide clear separation
2. **DON'T ignore type hints** - Full typing is essential for Python
3. **DON'T block the event loop** - Use proper async patterns
4. **DON'T hardcode API endpoints** - Make them configurable
5. **DON'T skip session cleanup** - Always close HTTP sessions
6. **DON'T ignore SSL verification** - Security is critical
7. **DON'T skip input validation** - Use pydantic for all models
8. **DON'T forget about memory usage** - Handle large responses efficiently
9. **DON'T make breaking changes in patches** - Follow semantic versioning
10. **DON'T skip comprehensive error handling** - Python developers expect detailed errors
