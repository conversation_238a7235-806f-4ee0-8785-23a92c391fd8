# JavaScript SDK Implementation

name: "JavaScript SDK Implementation"
description: |
  Comprehensive JavaScript/TypeScript SDK for CCL platform providing type-safe client libraries for Node.js and browser environments with React hooks and authentication.
  
  Core Principles:
  - **Type Safety**: Full TypeScript support with generated types
  - **Modern APIs**: Promise-based with async/await support
  - **React Integration**: Custom hooks for React applications
  - **Environment Agnostic**: Works in Node.js and browsers
  - **Error Handling**: Comprehensive error types and retry logic

## Goal

Create a developer-friendly JavaScript SDK that provides seamless integration with CCL platform APIs while maintaining type safety and excellent developer experience.

## Why

JavaScript SDK enables:
- Rapid frontend application development
- Server-side integrations with Node.js
- Type-safe API interactions
- Simplified authentication handling
- React application development

This provides:
- Reduced integration time
- Better developer experience
- Type safety and IntelliSense
- Consistent error handling
- Built-in best practices

## What

### User-Visible Behavior
- Simple SDK installation via npm
- Intuitive API methods
- Type-safe interactions
- React hooks for common operations
- Automatic token management

### Technical Requirements
- [ ] TypeScript-first development
- [ ] Node.js and browser compatibility
- [ ] React hooks package
- [ ] Automatic token refresh
- [ ] Request/response interceptors
- [ ] Error handling and retries
- [ ] Streaming support for real-time features
- [ ] GraphQL client integration

### Success Criteria
- [ ] <100KB bundle size for browser
- [ ] Full TypeScript coverage
- [ ] React hooks for all major operations
- [ ] Comprehensive documentation
- [ ] Zero breaking changes in minor versions

## All Needed Context

### Documentation & References
- url: https://www.typescriptlang.org/docs/
  why: TypeScript best practices
- url: https://react.dev/reference/react
  why: React hooks patterns
- url: https://axios-http.com/docs/intro
  why: HTTP client patterns
- file: docs/api/README.md
  why: API specifications

### Package Structure

```yaml
Package Organization:
  core:
    - client
    - authentication
    - types
    - errors
    
  modules:
    - repositories
    - analysis
    - patterns
    - queries
    - marketplace
    
  integrations:
    - react-hooks
    - graphql-client
    - websocket-client
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Handle token refresh race conditions
- **CRITICAL**: Proper error boundary setup for React
- **GOTCHA**: CORS issues in browser environments
- **GOTCHA**: File upload size limits in browsers
- **WARNING**: Bundle size impacts with full SDK
- **TIP**: Use tree-shaking for smaller bundles
- **TIP**: Implement retry logic with exponential backoff

## Implementation Blueprint

### Core SDK Client

```typescript
// src/client/CCLClient.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { AuthManager } from './auth/AuthManager';
import { ErrorHandler } from './errors/ErrorHandler';
import { RetryManager } from './retry/RetryManager';

export interface CCLClientConfig {
  apiUrl?: string;
  apiKey?: string;
  accessToken?: string;
  timeout?: number;
  retries?: number;
  environment?: 'production' | 'staging' | 'development';
}

export interface RequestOptions {
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
  signal?: AbortSignal;
}

export class CCLClient {
  private http: AxiosInstance;
  private auth: AuthManager;
  private errorHandler: ErrorHandler;
  private retryManager: RetryManager;
  
  // Service modules
  public repositories: RepositoryService;
  public analysis: AnalysisService;
  public patterns: PatternService;
  public queries: QueryService;
  public marketplace: MarketplaceService;
  public auth: AuthService;
  
  constructor(config: CCLClientConfig = {}) {
    const baseURL = config.apiUrl || this.getDefaultApiUrl(config.environment);
    
    // Create HTTP client
    this.http = axios.create({
      baseURL,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': `ccl-js-sdk/${this.getVersion()}`
      }
    });
    
    // Initialize managers
    this.auth = new AuthManager(config, this.http);
    this.errorHandler = new ErrorHandler();
    this.retryManager = new RetryManager(config.retries || 3);
    
    // Setup interceptors
    this.setupRequestInterceptors();
    this.setupResponseInterceptors();
    
    // Initialize service modules
    this.repositories = new RepositoryService(this);
    this.analysis = new AnalysisService(this);
    this.patterns = new PatternService(this);
    this.queries = new QueryService(this);
    this.marketplace = new MarketplaceService(this);
    this.auth = new AuthService(this);
  }
  
  private setupRequestInterceptors(): void {
    this.http.interceptors.request.use(
      async (config) => {
        // Add authentication
        const token = await this.auth.getValidToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        // Add request ID for tracing
        config.headers['X-Request-ID'] = this.generateRequestId();
        
        return config;
      },
      (error) => Promise.reject(error)
    );
  }
  
  private setupResponseInterceptors(): void {
    this.http.interceptors.response.use(
      (response) => response,
      async (error) => {
        // Handle token refresh
        if (error.response?.status === 401) {
          const refreshed = await this.auth.refreshToken();
          if (refreshed) {
            // Retry the original request
            return this.http.request(error.config);
          }
        }
        
        // Apply retry logic
        if (this.retryManager.shouldRetry(error)) {
          return this.retryManager.retry(() => this.http.request(error.config));
        }
        
        // Transform to SDK error
        throw this.errorHandler.transformError(error);
      }
    );
  }
  
  async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH',
    url: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<T> {
    const config: AxiosRequestConfig = {
      method,
      url,
      data,
      timeout: options.timeout,
      headers: options.headers,
      signal: options.signal
    };
    
    const response = await this.http.request<T>(config);
    return response.data;
  }
  
  async get<T>(url: string, options?: RequestOptions): Promise<T> {
    return this.request<T>('GET', url, undefined, options);
  }
  
  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<T> {
    return this.request<T>('POST', url, data, options);
  }
  
  async put<T>(url: string, data?: any, options?: RequestOptions): Promise<T> {
    return this.request<T>('PUT', url, data, options);
  }
  
  async delete<T>(url: string, options?: RequestOptions): Promise<T> {
    return this.request<T>('DELETE', url, undefined, options);
  }
  
  // File upload with progress
  async uploadFile(
    url: string,
    file: File | Buffer,
    options?: {
      onProgress?: (progress: number) => void;
      timeout?: number;
    }
  ): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);
    
    const config: AxiosRequestConfig = {
      headers: { 'Content-Type': 'multipart/form-data' },
      timeout: options?.timeout || 300000, // 5 minutes
      onUploadProgress: options?.onProgress ? (progressEvent) => {
        const progress = progressEvent.total 
          ? (progressEvent.loaded / progressEvent.total) * 100
          : 0;
        options.onProgress!(Math.round(progress));
      } : undefined
    };
    
    const response = await this.http.post(url, formData, config);
    return response.data;
  }
  
  // Streaming support
  createEventSource(url: string, options?: { token?: string }): EventSource {
    const token = options?.token || this.auth.getCurrentToken();
    const fullUrl = new URL(url, this.http.defaults.baseURL);
    
    if (token) {
      fullUrl.searchParams.set('token', token);
    }
    
    return new EventSource(fullUrl.toString());
  }
  
  private getDefaultApiUrl(environment?: string): string {
    switch (environment) {
      case 'production':
        return 'https://api.ccl.dev/v1';
      case 'staging':
        return 'https://staging-api.ccl.dev/v1';
      default:
        return 'https://api.ccl.dev/v1';
    }
  }
  
  private getVersion(): string {
    return process.env.SDK_VERSION || '1.0.0';
  }
  
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Export convenience function
export function createClient(config?: CCLClientConfig): CCLClient {
  return new CCLClient(config);
}
```

### TypeScript Type Definitions

```typescript
// src/types/index.ts

// Base types
export interface BaseEntity {
  id: string;
  createdAt: string;
  updatedAt: string;
}

// Repository types
export interface Repository extends BaseEntity {
  name: string;
  url: string;
  provider: 'github' | 'gitlab' | 'bitbucket';
  ownerId: string;
  organizationId?: string;
  description?: string;
  language: string;
  isPrivate: boolean;
  lastAnalyzedAt?: string;
}

export interface CreateRepositoryRequest {
  name: string;
  url: string;
  provider: 'github' | 'gitlab' | 'bitbucket';
  organizationId?: string;
  autoAnalyze?: boolean;
}

// Analysis types
export interface Analysis extends BaseEntity {
  repositoryId: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  duration?: number;
  commit?: string;
  branch: string;
  statistics?: AnalysisStatistics;
  error?: AnalysisError;
}

export interface AnalysisStatistics {
  totalFiles: number;
  totalLines: number;
  languageBreakdown: Record<string, number>;
  complexity: number;
  maintainabilityIndex: number;
  technicalDebt: number;
}

export interface AnalysisError {
  code: string;
  message: string;
  details?: any;
}

export interface StartAnalysisRequest {
  repositoryId: string;
  branch?: string;
  commit?: string;
  options?: AnalysisOptions;
}

export interface AnalysisOptions {
  incremental?: boolean;
  deepPatternAnalysis?: boolean;
  generateEmbeddings?: boolean;
  includePaths?: string[];
  excludePaths?: string[];
  patternConfidenceThreshold?: number;
}

// Pattern types
export interface Pattern extends BaseEntity {
  repositoryId: string;
  analysisId: string;
  type: 'architectural' | 'design' | 'idiom' | 'anti-pattern';
  name: string;
  description: string;
  confidence: number;
  occurrences: number;
  locations: PatternLocation[];
  template: PatternTemplate;
  qualityScore: number;
  tags: string[];
}

export interface PatternLocation {
  file: string;
  startLine: number;
  endLine: number;
  snippet: string;
}

export interface PatternTemplate {
  code: string;
  variables: string[];
  language: string;
}

// Query types
export interface QueryRequest {
  query: string;
  repositoryId?: string;
  conversationId?: string;
  context?: QueryContext;
  stream?: boolean;
  model?: 'fast' | 'advanced';
}

export interface QueryContext {
  focusArea?: string;
  includeCode?: boolean;
  maxExamples?: number;
}

export interface QueryResponse {
  answer: string;
  confidence: number;
  conversationId: string;
  sources: QuerySource[];
  codeExamples: CodeExample[];
  patternsReferenced: PatternReference[];
  suggestedQuestions: string[];
}

export interface QuerySource {
  type: 'file' | 'pattern' | 'documentation';
  title: string;
  content: string;
  url?: string;
  relevanceScore: number;
}

export interface CodeExample {
  language: string;
  code: string;
  file: string;
  startLine: number;
  endLine: number;
  explanation: string;
}

// Error types
export class CCLError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'CCLError';
  }
}

export class AuthenticationError extends CCLError {
  constructor(message = 'Authentication failed') {
    super(message, 'AUTHENTICATION_ERROR', 401);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends CCLError {
  constructor(message = 'Access denied') {
    super(message, 'AUTHORIZATION_ERROR', 403);
    this.name = 'AuthorizationError';
  }
}

export class ValidationError extends CCLError {
  constructor(message: string, public errors: ValidationFieldError[]) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
  }
}

export interface ValidationFieldError {
  field: string;
  message: string;
  code: string;
}

export class RateLimitError extends CCLError {
  constructor(
    message = 'Rate limit exceeded',
    public retryAfter?: number
  ) {
    super(message, 'RATE_LIMIT_ERROR', 429);
    this.name = 'RateLimitError';
  }
}

// Pagination types
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
    nextCursor?: string;
    prevCursor?: string;
  };
}

export interface PaginationOptions {
  limit?: number;
  offset?: number;
  cursor?: string;
}

// Filter types
export interface RepositoryFilter {
  provider?: 'github' | 'gitlab' | 'bitbucket';
  language?: string;
  hasAnalysis?: boolean;
  organizationId?: string;
  search?: string;
}

export interface PatternFilter {
  type?: 'architectural' | 'design' | 'idiom' | 'anti-pattern';
  minConfidence?: number;
  language?: string;
  repositoryId?: string;
  analysisId?: string;
  search?: string;
}
```

### Service Modules

```typescript
// src/services/RepositoryService.ts
export class RepositoryService {
  constructor(private client: CCLClient) {}
  
  async list(
    options?: {
      filter?: RepositoryFilter;
      pagination?: PaginationOptions;
    }
  ): Promise<PaginatedResponse<Repository>> {
    const params = new URLSearchParams();
    
    if (options?.filter) {
      Object.entries(options.filter).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    if (options?.pagination) {
      Object.entries(options.pagination).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    return this.client.get<PaginatedResponse<Repository>>(
      `/repositories?${params.toString()}`
    );
  }
  
  async get(id: string): Promise<Repository> {
    return this.client.get<Repository>(`/repositories/${id}`);
  }
  
  async create(data: CreateRepositoryRequest): Promise<Repository> {
    return this.client.post<Repository>('/repositories', data);
  }
  
  async update(id: string, data: Partial<Repository>): Promise<Repository> {
    return this.client.put<Repository>(`/repositories/${id}`, data);
  }
  
  async delete(id: string): Promise<void> {
    await this.client.delete(`/repositories/${id}`);
  }
  
  async getAnalyses(
    id: string,
    options?: {
      pagination?: PaginationOptions;
    }
  ): Promise<PaginatedResponse<Analysis>> {
    const params = new URLSearchParams();
    
    if (options?.pagination) {
      Object.entries(options.pagination).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    return this.client.get<PaginatedResponse<Analysis>>(
      `/repositories/${id}/analyses?${params.toString()}`
    );
  }
  
  async getPatterns(
    id: string,
    options?: {
      filter?: PatternFilter;
      pagination?: PaginationOptions;
    }
  ): Promise<PaginatedResponse<Pattern>> {
    const params = new URLSearchParams();
    
    if (options?.filter) {
      Object.entries(options.filter).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    if (options?.pagination) {
      Object.entries(options.pagination).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    return this.client.get<PaginatedResponse<Pattern>>(
      `/repositories/${id}/patterns?${params.toString()}`
    );
  }
}

// src/services/AnalysisService.ts
export class AnalysisService {
  constructor(private client: CCLClient) {}
  
  async start(request: StartAnalysisRequest): Promise<Analysis> {
    return this.client.post<Analysis>('/analysis', request);
  }
  
  async get(id: string): Promise<Analysis> {
    return this.client.get<Analysis>(`/analysis/${id}`);
  }
  
  async list(
    options?: {
      filter?: {
        repositoryId?: string;
        status?: Analysis['status'];
        startedAfter?: string;
        startedBefore?: string;
      };
      pagination?: PaginationOptions;
    }
  ): Promise<PaginatedResponse<Analysis>> {
    const params = new URLSearchParams();
    
    if (options?.filter) {
      Object.entries(options.filter).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    if (options?.pagination) {
      Object.entries(options.pagination).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    return this.client.get<PaginatedResponse<Analysis>>(
      `/analysis?${params.toString()}`
    );
  }
  
  async cancel(id: string): Promise<void> {
    await this.client.post(`/analysis/${id}/cancel`);
  }
  
  // Real-time analysis updates
  subscribeToUpdates(
    analysisId: string,
    callback: (analysis: Analysis) => void
  ): () => void {
    const eventSource = this.client.createEventSource(
      `/analysis/${analysisId}/stream`
    );
    
    eventSource.onmessage = (event) => {
      try {
        const analysis = JSON.parse(event.data) as Analysis;
        callback(analysis);
      } catch (error) {
        console.error('Failed to parse analysis update:', error);
      }
    };
    
    // Return cleanup function
    return () => {
      eventSource.close();
    };
  }
}

// src/services/QueryService.ts
export class QueryService {
  constructor(private client: CCLClient) {}
  
  async execute(request: QueryRequest): Promise<QueryResponse> {
    return this.client.post<QueryResponse>('/query', request);
  }
  
  // Streaming query execution
  async executeStream(
    request: QueryRequest,
    callback: (chunk: string) => void
  ): Promise<QueryResponse> {
    const streamRequest = { ...request, stream: true };
    
    const eventSource = this.client.createEventSource('/query/stream');
    
    return new Promise((resolve, reject) => {
      let finalResponse: QueryResponse;
      
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'chunk') {
            callback(data.content);
          } else if (data.type === 'complete') {
            finalResponse = data.response;
            eventSource.close();
            resolve(finalResponse);
          }
        } catch (error) {
          reject(error);
        }
      };
      
      eventSource.onerror = (error) => {
        eventSource.close();
        reject(error);
      };
      
      // Send the query request
      this.client.post('/query/stream', streamRequest).catch(reject);
    });
  }
  
  async getConversations(
    options?: {
      filter?: {
        repositoryId?: string;
      };
      pagination?: PaginationOptions;
    }
  ): Promise<PaginatedResponse<Conversation>> {
    const params = new URLSearchParams();
    
    if (options?.filter) {
      Object.entries(options.filter).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    if (options?.pagination) {
      Object.entries(options.pagination).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    return this.client.get<PaginatedResponse<Conversation>>(
      `/conversations?${params.toString()}`
    );
  }
  
  async getConversation(id: string): Promise<Conversation> {
    return this.client.get<Conversation>(`/conversations/${id}`);
  }
  
  async getMessages(
    conversationId: string,
    options?: {
      pagination?: PaginationOptions;
    }
  ): Promise<PaginatedResponse<Message>> {
    const params = new URLSearchParams();
    
    if (options?.pagination) {
      Object.entries(options.pagination).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
    }
    
    return this.client.get<PaginatedResponse<Message>>(
      `/conversations/${conversationId}/messages?${params.toString()}`
    );
  }
}
```

### React Hooks Package

```typescript
// packages/react-hooks/src/index.ts
import { useState, useEffect, useCallback, useRef } from 'react';
import { CCLClient, Repository, Analysis, Pattern, QueryResponse } from '@ccl/sdk';

// Context for CCL client
import React, { createContext, useContext, ReactNode } from 'react';

interface CCLContextValue {
  client: CCLClient;
}

const CCLContext = createContext<CCLContextValue | undefined>(undefined);

export interface CCLProviderProps {
  client: CCLClient;
  children: ReactNode;
}

export function CCLProvider({ client, children }: CCLProviderProps) {
  return (
    <CCLContext.Provider value={{ client }}>
      {children}
    </CCLContext.Provider>
  );
}

export function useClient(): CCLClient {
  const context = useContext(CCLContext);
  if (!context) {
    throw new Error('useClient must be used within CCLProvider');
  }
  return context.client;
}

// Repository hooks
export function useRepositories(filter?: RepositoryFilter) {
  const client = useClient();
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchRepositories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await client.repositories.list({ filter });
      setRepositories(response.data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [client, filter]);
  
  useEffect(() => {
    fetchRepositories();
  }, [fetchRepositories]);
  
  return {
    repositories,
    loading,
    error,
    refetch: fetchRepositories
  };
}

export function useRepository(id: string) {
  const client = useClient();
  const [repository, setRepository] = useState<Repository | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    if (!id) return;
    
    const fetchRepository = async () => {
      try {
        setLoading(true);
        setError(null);
        const repo = await client.repositories.get(id);
        setRepository(repo);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchRepository();
  }, [client, id]);
  
  const updateRepository = useCallback(async (data: Partial<Repository>) => {
    if (!id) return;
    
    try {
      const updated = await client.repositories.update(id, data);
      setRepository(updated);
      return updated;
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  }, [client, id]);
  
  const deleteRepository = useCallback(async () => {
    if (!id) return;
    
    try {
      await client.repositories.delete(id);
      setRepository(null);
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  }, [client, id]);
  
  return {
    repository,
    loading,
    error,
    update: updateRepository,
    delete: deleteRepository
  };
}

// Analysis hooks
export function useAnalysis(id: string) {
  const client = useClient();
  const [analysis, setAnalysis] = useState<Analysis | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    if (!id) return;
    
    const fetchAnalysis = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await client.analysis.get(id);
        setAnalysis(result);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchAnalysis();
  }, [client, id]);
  
  // Subscribe to real-time updates
  useEffect(() => {
    if (!id || !analysis) return;
    
    const unsubscribe = client.analysis.subscribeToUpdates(id, (updated) => {
      setAnalysis(updated);
    });
    
    return unsubscribe;
  }, [client, id, analysis]);
  
  return {
    analysis,
    loading,
    error
  };
}

export function useStartAnalysis() {
  const client = useClient();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const startAnalysis = useCallback(async (request: StartAnalysisRequest) => {
    try {
      setLoading(true);
      setError(null);
      const analysis = await client.analysis.start(request);
      return analysis;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [client]);
  
  return {
    startAnalysis,
    loading,
    error
  };
}

// Query hooks
export function useQuery() {
  const client = useClient();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const executeQuery = useCallback(async (request: QueryRequest) => {
    try {
      setLoading(true);
      setError(null);
      const response = await client.queries.execute(request);
      return response;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [client]);
  
  return {
    executeQuery,
    loading,
    error
  };
}

export function useStreamingQuery() {
  const client = useClient();
  const [response, setResponse] = useState<QueryResponse | null>(null);
  const [chunks, setChunks] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const executeStreamingQuery = useCallback(async (request: QueryRequest) => {
    try {
      setLoading(true);
      setError(null);
      setChunks([]);
      setResponse(null);
      
      const finalResponse = await client.queries.executeStream(request, (chunk) => {
        setChunks(prev => [...prev, chunk]);
      });
      
      setResponse(finalResponse);
      return finalResponse;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [client]);
  
  return {
    executeStreamingQuery,
    response,
    chunks,
    loading,
    error
  };
}

// File upload hook
export function useFileUpload() {
  const client = useClient();
  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const uploadFile = useCallback(async (
    url: string,
    file: File,
    options?: { timeout?: number }
  ) => {
    try {
      setLoading(true);
      setError(null);
      setProgress(0);
      
      const result = await client.uploadFile(url, file, {
        onProgress: setProgress,
        timeout: options?.timeout
      });
      
      return result;
    } catch (err) {
      setError(err as Error);
      throw err;
    } finally {
      setLoading(false);
      setProgress(0);
    }
  }, [client]);
  
  return {
    uploadFile,
    progress,
    loading,
    error
  };
}

// Example: Code Review Assistant
class CodeReviewAssistant {
  private ccl: CCLClient;
  private repoId: string;

  constructor(apiKey: string, repoId: string) {
    this.ccl = new CCLClient({ apiKey });
    this.repoId = repoId;
  }

  async reviewPullRequest(prNumber: number) {
    // Get PR changes
    const changes = await this.getPRChanges(prNumber);
    
    // Analyze changes against patterns
    const violations = [];
    
    for (const file of changes.files) {
      const analysis = await this.ccl.patterns.validateCode({
        repositoryId: this.repoId,
        code: file.content,
        filePath: file.path
      });
      
      if (analysis.violations.length > 0) {
        violations.push({
          file: file.path,
          violations: analysis.violations
        });
      }
    }
    
    // Generate review comment
    if (violations.length > 0) {
      const comment = await this.generateReviewComment(violations);
      await this.postPRComment(prNumber, comment);
    }
    
    return violations;
  }
  
  private async generateReviewComment(violations: any[]) {
    const response = await this.ccl.query({
      query: `Generate a constructive code review comment for these pattern violations: ${JSON.stringify(violations)}`,
      repositoryId: this.repoId
    });
    
    return response.answer;
  }
}
```

### Package Configuration

```json
// package.json
{
  "name": "@ccl/sdk",
  "version": "1.0.0",
  "description": "Official JavaScript SDK for CCL (Codebase Context Layer)",
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts",
  "files": [
    "dist"
  ],
  "scripts": {
    "build": "rollup -c",
    "build:types": "tsc --emitDeclarationOnly",
    "dev": "rollup -c --watch",
    "test": "jest",
    "test:watch": "jest --watch",
    "lint": "eslint src --ext .ts,.tsx",
    "type-check": "tsc --noEmit"
  },
  "keywords": [
    "ccl",
    "codebase",
    "analysis",
    "sdk",
    "typescript",
    "api-client"
  ],
  "author": "CCL Team",
  "license": "MIT",
  "dependencies": {
    "axios": "^1.6.0"
  },
  "devDependencies": {
    "@rollup/plugin-commonjs": "^25.0.0",
    "@rollup/plugin-node-resolve": "^15.2.0",
    "@rollup/plugin-typescript": "^11.1.0",
    "@types/jest": "^29.5.0",
    "@types/node": "^20.10.0",
    "@typescript-eslint/eslint-plugin": "^6.15.0",
    "@typescript-eslint/parser": "^6.15.0",
    "eslint": "^8.56.0",
    "jest": "^29.7.0",
    "rollup": "^4.9.0",
    "rollup-plugin-dts": "^6.1.0",
    "ts-jest": "^29.1.0",
    "typescript": "^5.3.0"
  },
  "peerDependencies": {
    "react": ">=16.8.0"
  },
  "peerDependenciesMeta": {
    "react": {
      "optional": true
    }
  }
}

// packages/react-hooks/package.json
{
  "name": "@ccl/react-hooks",
  "version": "1.0.0",
  "description": "React hooks for CCL SDK",
  "main": "dist/index.js",
  "module": "dist/index.esm.js",
  "types": "dist/index.d.ts",
  "files": [
    "dist"
  ],
  "scripts": {
    "build": "rollup -c",
    "build:types": "tsc --emitDeclarationOnly",
    "dev": "rollup -c --watch",
    "test": "jest",
    "lint": "eslint src --ext .ts,.tsx"
  },
  "keywords": [
    "ccl",
    "react",
    "hooks",
    "sdk"
  ],
  "author": "CCL Team",
  "license": "MIT",
  "dependencies": {
    "@ccl/sdk": "^1.0.0"
  },
  "peerDependencies": {
    "react": ">=16.8.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "react": "^18.2.0"
  }
}
```

## Validation Loop

### Level 1: Basic Functionality Testing
```typescript
// Test core SDK functionality
describe('CCL SDK', () => {
  let client: CCLClient;
  
  beforeEach(() => {
    client = createClient({
      apiUrl: 'https://api.test.ccl.dev',
      apiKey: 'test-key'
    });
  });
  
  test('creates client with default config', () => {
    const defaultClient = createClient();
    expect(defaultClient).toBeInstanceOf(CCLClient);
  });
  
  test('repositories service works', async () => {
    const mockRepository: Repository = {
      id: 'repo-123',
      name: 'test-repo',
      url: 'https://github.com/test/repo',
      provider: 'github',
      ownerId: 'user-123',
      language: 'typescript',
      isPrivate: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    // Mock the HTTP request
    jest.spyOn(client, 'get').mockResolvedValue({
      data: [mockRepository],
      pagination: {
        total: 1,
        limit: 20,
        offset: 0,
        hasMore: false
      }
    });
    
    const result = await client.repositories.list();
    expect(result.data).toHaveLength(1);
    expect(result.data[0]).toEqual(mockRepository);
  });
  
  test('handles authentication errors', async () => {
    jest.spyOn(client, 'get').mockRejectedValue(
      new AuthenticationError('Invalid token')
    );
    
    await expect(client.repositories.list()).rejects.toThrow(AuthenticationError);
  });
});
```

### Level 2: React Hooks Testing
```typescript
// Test React hooks
import { renderHook, act } from '@testing-library/react';
import { CCLProvider, useRepositories, useRepository } from '@ccl/react-hooks';

describe('React Hooks', () => {
  const mockClient = createClient({ apiKey: 'test' });
  
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <CCLProvider client={mockClient}>
      {children}
    </CCLProvider>
  );
  
  test('useRepositories fetches repositories', async () => {
    const mockRepos = [
      { id: '1', name: 'repo1' },
      { id: '2', name: 'repo2' }
    ];
    
    jest.spyOn(mockClient.repositories, 'list').mockResolvedValue({
      data: mockRepos,
      pagination: { total: 2, limit: 20, offset: 0, hasMore: false }
    });
    
    const { result } = renderHook(() => useRepositories(), { wrapper });
    
    expect(result.current.loading).toBe(true);
    
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });
    
    expect(result.current.loading).toBe(false);
    expect(result.current.repositories).toHaveLength(2);
  });
  
  test('useRepository updates repository', async () => {
    const mockRepo = { id: 'repo-123', name: 'test-repo' };
    const updatedRepo = { ...mockRepo, name: 'updated-repo' };
    
    jest.spyOn(mockClient.repositories, 'get').mockResolvedValue(mockRepo);
    jest.spyOn(mockClient.repositories, 'update').mockResolvedValue(updatedRepo);
    
    const { result } = renderHook(() => useRepository('repo-123'), { wrapper });
    
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });
    
    expect(result.current.repository).toEqual(mockRepo);
    
    await act(async () => {
      await result.current.update({ name: 'updated-repo' });
    });
    
    expect(result.current.repository).toEqual(updatedRepo);
  });
});
```

### Level 3: Integration Testing
```typescript
// Integration tests with real API
describe('SDK Integration', () => {
  let client: CCLClient;
  
  beforeAll(() => {
    client = createClient({
      apiUrl: process.env.TEST_API_URL,
      apiKey: process.env.TEST_API_KEY
    });
  });
  
  test('complete repository workflow', async () => {
    // Create repository
    const repo = await client.repositories.create({
      name: 'test-integration-repo',
      url: 'https://github.com/test/integration',
      provider: 'github'
    });
    
    expect(repo.id).toBeDefined();
    expect(repo.name).toBe('test-integration-repo');
    
    // Start analysis
    const analysis = await client.analysis.start({
      repositoryId: repo.id
    });
    
    expect(analysis.status).toBe('pending');
    
    // Clean up
    await client.repositories.delete(repo.id);
  });
  
  test('streaming query execution', async () => {
    const chunks: string[] = [];
    
    const response = await client.queries.executeStream(
      {
        query: 'Explain authentication patterns',
        repositoryId: 'test-repo-id'
      },
      (chunk) => {
        chunks.push(chunk);
      }
    );
    
    expect(chunks.length).toBeGreaterThan(0);
    expect(response.answer).toBeDefined();
    expect(response.confidence).toBeGreaterThan(0);
  });
});
```

## Final Validation Checklist

- [ ] TypeScript types generated and exported
- [ ] Node.js and browser compatibility verified
- [ ] React hooks package functional
- [ ] Authentication and token refresh working
- [ ] Error handling comprehensive
- [ ] File upload with progress tracking
- [ ] Streaming support implemented
- [ ] Bundle size under 100KB
- [ ] Documentation complete
- [ ] Integration tests passing

## Anti-Patterns to Avoid

1. **DON'T expose internal implementation details** - Keep API clean
2. **DON'T ignore error handling** - Provide meaningful error messages
3. **DON'T block the main thread** - Use async/await properly
4. **DON'T hardcode API URLs** - Make them configurable
5. **DON'T ignore bundle size** - Tree-shaking and optimization matter
6. **DON'T skip TypeScript types** - Type safety is crucial
7. **DON'T forget about React's rules of hooks** - Follow hooks guidelines
8. **DON'T ignore memory leaks** - Clean up subscriptions and event listeners
9. **DON'T make breaking changes in patches** - Follow semantic versioning
10. **DON'T skip comprehensive testing** - Unit, integration, and E2E tests needed
