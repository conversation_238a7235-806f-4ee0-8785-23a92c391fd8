# OAuth 2.0 Implementation

name: "OAuth 2.0 Implementation"
description: |
  Complete OAuth 2.0 authorization server implementation for secure third-party integrations and API access control.
  
  Core Principles:
  - **Security First**: Follow OAuth 2.0 security best practices
  - **Standard Compliance**: RFC 6749 and related specifications
  - **Flexible Scopes**: Fine-grained permission system
  - **Secure Storage**: Encrypted tokens and secure key management
  - **Audit Trail**: Complete authorization event logging

## Goal

Implement a secure OAuth 2.0 authorization server that enables third-party applications to safely access CCL APIs with user consent and proper scope limitations.

## Why

OAuth 2.0 enables:
- Secure third-party integrations
- User-controlled access delegation
- Enterprise SSO integration
- Mobile and web application authentication
- API access without password sharing

This provides:
- Industry-standard security
- Scalable authentication
- User privacy protection
- Enterprise compliance
- Developer-friendly integration

## What

### User-Visible Behavior
- Consent screens for third-party apps
- Secure login flow
- Token management interface
- Application authorization management
- SSO integration

### Technical Requirements
- [ ] OAuth 2.0 authorization server
- [ ] Authorization code flow
- [ ] Client credentials flow
- [ ] PKCE support for mobile apps
- [ ] JWT token implementation
- [ ] Refresh token rotation
- [ ] Scope-based access control
- [ ] Client application management

### Success Criteria
- [ ] RFC 6749 compliance
- [ ] <200ms token generation
- [ ] Secure token storage
- [ ] Comprehensive audit logging
- [ ] Enterprise SSO integration

## All Needed Context

### Documentation & References
- url: https://tools.ietf.org/html/rfc6749
  why: OAuth 2.0 specification
- url: https://tools.ietf.org/html/rfc7636
  why: PKCE specification for secure mobile flows
- url: https://tools.ietf.org/html/rfc7519
  why: JWT specification
- file: docs/api/README.md
  why: Current API authentication requirements

### OAuth Flow Types

```yaml
Supported Flows:
  authorization_code:
    use_case: Web applications, mobile apps
    security: High (with PKCE)
    
  client_credentials:
    use_case: Server-to-server APIs
    security: High (client secret required)
    
  refresh_token:
    use_case: Token renewal
    security: High (rotation enabled)
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Always validate redirect URIs
- **CRITICAL**: Use PKCE for public clients
- **GOTCHA**: State parameter prevents CSRF attacks
- **GOTCHA**: Token expiration must be enforced
- **WARNING**: Client secrets must be hashed
- **TIP**: Use short-lived access tokens
- **TIP**: Rotate refresh tokens on use

## Implementation Blueprint

### OAuth Server Configuration

```typescript
// config/oauth.ts
export interface OAuthConfig {
  issuer: string;
  authorizationEndpoint: string;
  tokenEndpoint: string;
  jwksEndpoint: string;
  
  // Token settings
  accessTokenLifetime: number; // seconds
  refreshTokenLifetime: number; // seconds
  authorizationCodeLifetime: number; // seconds
  
  // Security settings
  requirePKCE: boolean;
  rotateRefreshTokens: boolean;
  revokeRefreshTokensOnAccess: boolean;
  
  // Supported flows
  supportedGrantTypes: string[];
  supportedResponseTypes: string[];
  supportedScopes: string[];
}

export const oauthConfig: OAuthConfig = {
  issuer: 'https://auth.ccl.dev',
  authorizationEndpoint: 'https://auth.ccl.dev/oauth/authorize',
  tokenEndpoint: 'https://auth.ccl.dev/oauth/token',
  jwksEndpoint: 'https://auth.ccl.dev/.well-known/jwks.json',
  
  accessTokenLifetime: 3600, // 1 hour
  refreshTokenLifetime: 2592000, // 30 days
  authorizationCodeLifetime: 600, // 10 minutes
  
  requirePKCE: true,
  rotateRefreshTokens: true,
  revokeRefreshTokensOnAccess: false,
  
  supportedGrantTypes: [
    'authorization_code',
    'client_credentials',
    'refresh_token'
  ],
  supportedResponseTypes: ['code'],
  supportedScopes: [
    'read:analysis',
    'write:analysis',
    'read:patterns',
    'write:patterns',
    'read:queries',
    'write:queries',
    'read:repositories',
    'write:repositories',
    'read:profile',
    'write:profile',
    'admin'
  ]
};
```

### Client Application Management

```typescript
// models/oauthClient.ts
export interface OAuthClient {
  clientId: string;
  clientSecret?: string; // Hashed, only for confidential clients
  name: string;
  description: string;
  
  // Client type
  clientType: 'public' | 'confidential';
  
  // Redirect URIs
  redirectUris: string[];
  
  // Allowed grants and scopes
  allowedGrantTypes: string[];
  allowedScopes: string[];
  
  // Client metadata
  logoUri?: string;
  homepageUri?: string;
  privacyPolicyUri?: string;
  termsOfServiceUri?: string;
  
  // Settings
  requirePKCE: boolean;
  requireConsent: boolean;
  
  // Status
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Owner information
  ownerId: string; // User who registered the client
  organizationId?: string;
}

export class OAuthClientService {
  async createClient(data: CreateClientRequest): Promise<OAuthClient> {
    // Generate client ID
    const clientId = this.generateClientId();
    
    // Generate client secret for confidential clients
    const clientSecret = data.clientType === 'confidential' 
      ? this.generateClientSecret()
      : undefined;
    
    const client: OAuthClient = {
      clientId,
      clientSecret: clientSecret ? await this.hashSecret(clientSecret) : undefined,
      name: data.name,
      description: data.description,
      clientType: data.clientType,
      redirectUris: data.redirectUris,
      allowedGrantTypes: data.allowedGrantTypes,
      allowedScopes: data.allowedScopes,
      requirePKCE: data.clientType === 'public',
      requireConsent: true,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      ownerId: data.ownerId,
      organizationId: data.organizationId
    };
    
    await this.repository.save(client);
    
    // Return client with plain text secret (only time it's shown)
    return {
      ...client,
      clientSecret: clientSecret
    };
  }
  
  async validateClient(clientId: string, clientSecret?: string): Promise<OAuthClient | null> {
    const client = await this.repository.findByClientId(clientId);
    
    if (!client || !client.active) {
      return null;
    }
    
    // Validate client secret for confidential clients
    if (client.clientType === 'confidential') {
      if (!clientSecret || !client.clientSecret) {
        return null;
      }
      
      const isValid = await this.verifySecret(clientSecret, client.clientSecret);
      if (!isValid) {
        return null;
      }
    }
    
    return client;
  }
  
  private generateClientId(): string {
    return `ccl_${crypto.randomBytes(16).toString('hex')}`;
  }
  
  private generateClientSecret(): string {
    return crypto.randomBytes(32).toString('base64url');
  }
  
  private async hashSecret(secret: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(secret, saltRounds);
  }
  
  private async verifySecret(secret: string, hash: string): Promise<boolean> {
    return bcrypt.compare(secret, hash);
  }
}
```

### Authorization Code Flow

```typescript
// routes/oauth/authorize.ts
export class AuthorizationEndpoint {
  async handleAuthorizeRequest(req: Request, res: Response) {
    try {
      // Parse and validate request
      const authRequest = await this.parseAuthorizationRequest(req);
      
      // Validate client
      const client = await this.clientService.validateClient(authRequest.clientId);
      if (!client) {
        return this.sendError(res, 'invalid_client', 'Invalid client');
      }
      
      // Validate redirect URI
      if (!this.isValidRedirectUri(authRequest.redirectUri, client.redirectUris)) {
        return this.sendError(res, 'invalid_request', 'Invalid redirect URI');
      }
      
      // Validate scope
      const requestedScopes = authRequest.scope.split(' ');
      if (!this.areValidScopes(requestedScopes, client.allowedScopes)) {
        return this.redirectError(res, authRequest.redirectUri, 'invalid_scope', authRequest.state);
      }
      
      // Check if user is authenticated
      if (!req.user) {
        // Redirect to login with return URL
        const loginUrl = `/login?return_to=${encodeURIComponent(req.originalUrl)}`;
        return res.redirect(loginUrl);
      }
      
      // Check for existing authorization
      const existingAuth = await this.authorizationService.findExisting(
        req.user.id,
        authRequest.clientId,
        requestedScopes
      );
      
      if (existingAuth && !client.requireConsent) {
        // Auto-approve if client is trusted and user previously consented
        return this.generateAuthorizationCode(req, res, authRequest, req.user, requestedScopes);
      }
      
      // Show consent screen
      return this.showConsentScreen(res, {
        client,
        user: req.user,
        requestedScopes,
        authRequest
      });
      
    } catch (error) {
      logger.error('Authorization request error:', error);
      return this.sendError(res, 'server_error', 'Internal server error');
    }
  }
  
  async handleConsentResponse(req: Request, res: Response) {
    const { approve, ...authRequest } = req.body;
    
    if (!approve) {
      return this.redirectError(
        res,
        authRequest.redirectUri,
        'access_denied',
        authRequest.state
      );
    }
    
    const requestedScopes = authRequest.scope.split(' ');
    return this.generateAuthorizationCode(req, res, authRequest, req.user, requestedScopes);
  }
  
  private async generateAuthorizationCode(
    req: Request,
    res: Response,
    authRequest: AuthorizationRequest,
    user: User,
    scopes: string[]
  ) {
    // Generate authorization code
    const code = crypto.randomBytes(32).toString('base64url');
    
    // Store authorization code
    await this.authorizationCodeService.store({
      code,
      clientId: authRequest.clientId,
      userId: user.id,
      scopes,
      redirectUri: authRequest.redirectUri,
      codeChallenge: authRequest.codeChallenge,
      codeChallengeMethod: authRequest.codeChallengeMethod,
      expiresAt: new Date(Date.now() + oauthConfig.authorizationCodeLifetime * 1000)
    });
    
    // Store user authorization
    await this.authorizationService.store({
      userId: user.id,
      clientId: authRequest.clientId,
      scopes,
      createdAt: new Date()
    });
    
    // Redirect with authorization code
    const redirectUrl = new URL(authRequest.redirectUri);
    redirectUrl.searchParams.set('code', code);
    if (authRequest.state) {
      redirectUrl.searchParams.set('state', authRequest.state);
    }
    
    return res.redirect(redirectUrl.toString());
  }
  
  private async parseAuthorizationRequest(req: Request): Promise<AuthorizationRequest> {
    const {
      response_type,
      client_id,
      redirect_uri,
      scope = '',
      state,
      code_challenge,
      code_challenge_method
    } = req.query;
    
    // Validate required parameters
    if (!response_type || !client_id || !redirect_uri) {
      throw new Error('Missing required parameters');
    }
    
    if (response_type !== 'code') {
      throw new Error('Unsupported response type');
    }
    
    return {
      responseType: response_type as string,
      clientId: client_id as string,
      redirectUri: redirect_uri as string,
      scope: scope as string,
      state: state as string,
      codeChallenge: code_challenge as string,
      codeChallengeMethod: code_challenge_method as string || 'S256'
    };
  }
}
```

### Token Endpoint

```typescript
// routes/oauth/token.ts
export class TokenEndpoint {
  async handleTokenRequest(req: Request, res: Response) {
    try {
      const grantType = req.body.grant_type;
      
      switch (grantType) {
        case 'authorization_code':
          return this.handleAuthorizationCodeGrant(req, res);
        case 'client_credentials':
          return this.handleClientCredentialsGrant(req, res);
        case 'refresh_token':
          return this.handleRefreshTokenGrant(req, res);
        default:
          return this.sendTokenError(res, 'unsupported_grant_type');
      }
    } catch (error) {
      logger.error('Token request error:', error);
      return this.sendTokenError(res, 'server_error');
    }
  }
  
  private async handleAuthorizationCodeGrant(req: Request, res: Response) {
    const {
      code,
      redirect_uri,
      client_id,
      client_secret,
      code_verifier
    } = req.body;
    
    // Validate client
    const client = await this.clientService.validateClient(client_id, client_secret);
    if (!client) {
      return this.sendTokenError(res, 'invalid_client');
    }
    
    // Get and validate authorization code
    const authCode = await this.authorizationCodeService.consume(code);
    if (!authCode || authCode.clientId !== client_id) {
      return this.sendTokenError(res, 'invalid_grant');
    }
    
    // Validate redirect URI
    if (authCode.redirectUri !== redirect_uri) {
      return this.sendTokenError(res, 'invalid_grant');
    }
    
    // Validate PKCE if required
    if (authCode.codeChallenge) {
      if (!code_verifier) {
        return this.sendTokenError(res, 'invalid_request', 'Code verifier required');
      }
      
      const isValidPKCE = await this.validatePKCE(
        code_verifier,
        authCode.codeChallenge,
        authCode.codeChallengeMethod
      );
      
      if (!isValidPKCE) {
        return this.sendTokenError(res, 'invalid_grant', 'Invalid code verifier');
      }
    }
    
    // Generate tokens
    const tokens = await this.generateTokens({
      userId: authCode.userId,
      clientId: client_id,
      scopes: authCode.scopes
    });
    
    return this.sendTokenResponse(res, tokens);
  }
  
  private async handleClientCredentialsGrant(req: Request, res: Response) {
    const { client_id, client_secret, scope } = req.body;
    
    // Validate client
    const client = await this.clientService.validateClient(client_id, client_secret);
    if (!client || client.clientType !== 'confidential') {
      return this.sendTokenError(res, 'invalid_client');
    }
    
    // Validate grant type is allowed
    if (!client.allowedGrantTypes.includes('client_credentials')) {
      return this.sendTokenError(res, 'unauthorized_client');
    }
    
    // Validate scopes
    const requestedScopes = scope ? scope.split(' ') : [];
    if (!this.areValidScopes(requestedScopes, client.allowedScopes)) {
      return this.sendTokenError(res, 'invalid_scope');
    }
    
    // Generate access token (no refresh token for client credentials)
    const accessToken = await this.generateAccessToken({
      clientId: client_id,
      scopes: requestedScopes,
      tokenType: 'client_credentials'
    });
    
    return this.sendTokenResponse(res, {
      access_token: accessToken,
      token_type: 'Bearer',
      expires_in: oauthConfig.accessTokenLifetime,
      scope: requestedScopes.join(' ')
    });
  }
  
  private async handleRefreshTokenGrant(req: Request, res: Response) {
    const { refresh_token, client_id, client_secret, scope } = req.body;
    
    // Validate client
    const client = await this.clientService.validateClient(client_id, client_secret);
    if (!client) {
      return this.sendTokenError(res, 'invalid_client');
    }
    
    // Validate refresh token
    const tokenData = await this.refreshTokenService.consume(refresh_token);
    if (!tokenData || tokenData.clientId !== client_id) {
      return this.sendTokenError(res, 'invalid_grant');
    }
    
    // Validate scope (can only reduce, not expand)
    const requestedScopes = scope ? scope.split(' ') : tokenData.scopes;
    if (!this.areSubsetScopes(requestedScopes, tokenData.scopes)) {
      return this.sendTokenError(res, 'invalid_scope');
    }
    
    // Generate new tokens
    const tokens = await this.generateTokens({
      userId: tokenData.userId,
      clientId: client_id,
      scopes: requestedScopes
    });
    
    return this.sendTokenResponse(res, tokens);
  }
  
  private async generateTokens(params: {
    userId?: string;
    clientId: string;
    scopes: string[];
  }): Promise<TokenResponse> {
    const { userId, clientId, scopes } = params;
    
    // Generate access token
    const accessToken = await this.generateAccessToken({
      userId,
      clientId,
      scopes,
      tokenType: userId ? 'authorization_code' : 'client_credentials'
    });
    
    // Generate refresh token (only for authorization code grants)
    let refreshToken: string | undefined;
    if (userId && oauthConfig.rotateRefreshTokens) {
      refreshToken = await this.generateRefreshToken({
        userId,
        clientId,
        scopes
      });
    }
    
    return {
      access_token: accessToken,
      token_type: 'Bearer',
      expires_in: oauthConfig.accessTokenLifetime,
      refresh_token: refreshToken,
      scope: scopes.join(' ')
    };
  }
  
  private async validatePKCE(
    codeVerifier: string,
    codeChallenge: string,
    method: string
  ): Promise<boolean> {
    if (method === 'S256') {
      const hash = crypto.createHash('sha256')
        .update(codeVerifier)
        .digest('base64url');
      return hash === codeChallenge;
    } else if (method === 'plain') {
      return codeVerifier === codeChallenge;
    }
    
    return false;
  }
}
```

### JWT Token Implementation

```typescript
// services/jwtService.ts
import jwt from 'jsonwebtoken';
import { JWK, JWS } from 'node-jose';

export interface JWTPayload {
  iss: string; // Issuer
  sub: string; // Subject (user ID or client ID)
  aud: string; // Audience
  exp: number; // Expiration time
  iat: number; // Issued at
  jti: string; // JWT ID
  scope: string; // OAuth scopes
  client_id: string; // Client ID
  token_type: 'access_token' | 'refresh_token';
}

export class JWTService {
  private keyStore: JWK.KeyStore;
  private currentKey: JWK.Key;
  
  constructor() {
    this.initializeKeys();
  }
  
  private async initializeKeys() {
    // Load or generate RSA key pair
    this.keyStore = JWK.createKeyStore();
    
    if (process.env.JWT_PRIVATE_KEY) {
      // Load existing key
      this.currentKey = await JWK.asKey(process.env.JWT_PRIVATE_KEY, 'pem');
    } else {
      // Generate new key
      this.currentKey = await JWK.createKey('RSA', 2048, { alg: 'RS256', use: 'sig' });
    }
    
    await this.keyStore.add(this.currentKey);
  }
  
  async generateAccessToken(params: {
    userId?: string;
    clientId: string;
    scopes: string[];
    tokenType: string;
  }): Promise<string> {
    const { userId, clientId, scopes, tokenType } = params;
    
    const payload: JWTPayload = {
      iss: oauthConfig.issuer,
      sub: userId || clientId,
      aud: 'https://api.ccl.dev',
      exp: Math.floor(Date.now() / 1000) + oauthConfig.accessTokenLifetime,
      iat: Math.floor(Date.now() / 1000),
      jti: crypto.randomUUID(),
      scope: scopes.join(' '),
      client_id: clientId,
      token_type: 'access_token'
    };
    
    // Add user-specific claims
    if (userId) {
      const user = await this.userService.findById(userId);
      payload.email = user.email;
      payload.name = user.name;
      payload.picture = user.avatarUrl;
    }
    
    return this.signToken(payload);
  }
  
  async generateRefreshToken(params: {
    userId: string;
    clientId: string;
    scopes: string[];
  }): Promise<string> {
    const { userId, clientId, scopes } = params;
    
    const refreshToken = crypto.randomBytes(32).toString('base64url');
    
    // Store refresh token
    await this.refreshTokenService.store({
      token: refreshToken,
      userId,
      clientId,
      scopes,
      expiresAt: new Date(Date.now() + oauthConfig.refreshTokenLifetime * 1000)
    });
    
    return refreshToken;
  }
  
  private async signToken(payload: JWTPayload): Promise<string> {
    const token = await JWS.createSign(
      { format: 'compact', fields: { alg: 'RS256', typ: 'JWT' } },
      this.currentKey
    )
    .update(JSON.stringify(payload))
    .final();
    
    return token.toString();
  }
  
  async verifyToken(token: string): Promise<JWTPayload | null> {
    try {
      const result = await JWS.createVerify(this.keyStore).verify(token);
      const payload = JSON.parse(result.payload.toString());
      
      // Verify expiration
      if (payload.exp < Math.floor(Date.now() / 1000)) {
        return null;
      }
      
      return payload;
    } catch (error) {
      logger.warn('Token verification failed:', error.message);
      return null;
    }
  }
  
  async getJWKS(): Promise<any> {
    return this.keyStore.toJSON();
  }
}
```

### Scope-Based Access Control

```typescript
// middleware/scopeValidation.ts
export function requireScope(requiredScopes: string | string[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const token = extractTokenFromHeader(req);
    if (!token) {
      return res.status(401).json({ error: 'access_denied', error_description: 'No token provided' });
    }
    
    const payload = await jwtService.verifyToken(token);
    if (!payload) {
      return res.status(401).json({ error: 'invalid_token', error_description: 'Invalid or expired token' });
    }
    
    const tokenScopes = payload.scope.split(' ');
    const required = Array.isArray(requiredScopes) ? requiredScopes : [requiredScopes];
    
    // Check if token has required scopes
    const hasRequiredScope = required.some(scope => 
      tokenScopes.includes(scope) || tokenScopes.includes('admin')
    );
    
    if (!hasRequiredScope) {
      return res.status(403).json({ 
        error: 'insufficient_scope', 
        error_description: `Required scope: ${required.join(' or ')}` 
      });
    }
    
    // Add token info to request
    req.oauth = {
      token: payload,
      scopes: tokenScopes,
      userId: payload.sub,
      clientId: payload.client_id
    };
    
    next();
  };
}

// Usage in routes
app.get('/api/patterns', requireScope('read:patterns'), async (req, res) => {
  // Handler implementation
});

app.post('/api/patterns', requireScope(['write:patterns', 'admin']), async (req, res) => {
  // Handler implementation
});
```

### Consent Screen Implementation

```html
<!-- views/consent.ejs -->
<!DOCTYPE html>
<html>
<head>
    <title>Authorize Application - CCL</title>
    <link rel="stylesheet" href="/styles/oauth.css">
</head>
<body>
    <div class="consent-container">
        <div class="consent-header">
            <img src="<%= client.logoUri || '/images/default-app.png' %>" alt="<%= client.name %>" class="app-logo">
            <h1>Authorize <%= client.name %></h1>
            <p class="app-description"><%= client.description %></p>
        </div>
        
        <div class="user-info">
            <img src="<%= user.avatarUrl %>" alt="<%= user.name %>" class="user-avatar">
            <p>Signed in as <strong><%= user.name %></strong> (<%= user.email %>)</p>
        </div>
        
        <div class="permissions">
            <h2><%= client.name %> would like to:</h2>
            <ul class="scope-list">
                <% requestedScopes.forEach(scope => { %>
                    <li class="scope-item">
                        <i class="scope-icon <%= getScopeIcon(scope) %>"></i>
                        <span class="scope-description"><%= getScopeDescription(scope) %></span>
                    </li>
                <% }); %>
            </ul>
        </div>
        
        <div class="app-info">
            <p class="app-links">
                <% if (client.privacyPolicyUri) { %>
                    <a href="<%= client.privacyPolicyUri %>" target="_blank">Privacy Policy</a>
                <% } %>
                <% if (client.termsOfServiceUri) { %>
                    <a href="<%= client.termsOfServiceUri %>" target="_blank">Terms of Service</a>
                <% } %>
            </p>
        </div>
        
        <form method="post" action="/oauth/consent" class="consent-form">
            <input type="hidden" name="client_id" value="<%= authRequest.clientId %>">
            <input type="hidden" name="redirect_uri" value="<%= authRequest.redirectUri %>">
            <input type="hidden" name="scope" value="<%= authRequest.scope %>">
            <input type="hidden" name="state" value="<%= authRequest.state %>">
            <input type="hidden" name="response_type" value="<%= authRequest.responseType %>">
            <input type="hidden" name="code_challenge" value="<%= authRequest.codeChallenge %>">
            <input type="hidden" name="code_challenge_method" value="<%= authRequest.codeChallengeMethod %>">
            
            <div class="button-group">
                <button type="submit" name="approve" value="false" class="btn btn-cancel">Cancel</button>
                <button type="submit" name="approve" value="true" class="btn btn-primary">Authorize</button>
            </div>
        </form>
        
        <div class="security-notice">
            <p>By authorizing, you allow this app to access your CCL data according to the permissions above.</p>
            <p>You can revoke access at any time in your <a href="/settings/applications">account settings</a>.</p>
        </div>
    </div>
</body>
</html>
```

### Token Introspection

```typescript
// routes/oauth/introspect.ts
export class IntrospectionEndpoint {
  async handleIntrospectionRequest(req: Request, res: Response) {
    const { token, token_type_hint } = req.body;
    
    if (!token) {
      return res.status(400).json({ error: 'invalid_request' });
    }
    
    // Authenticate client
    const client = await this.authenticateClient(req);
    if (!client) {
      return res.status(401).json({ error: 'invalid_client' });
    }
    
    // Introspect token
    const tokenInfo = await this.introspectToken(token, token_type_hint);
    
    if (!tokenInfo || !tokenInfo.active) {
      return res.json({ active: false });
    }
    
    // Return token information
    return res.json({
      active: true,
      scope: tokenInfo.scope,
      client_id: tokenInfo.client_id,
      username: tokenInfo.username,
      exp: tokenInfo.exp,
      iat: tokenInfo.iat,
      sub: tokenInfo.sub,
      aud: tokenInfo.aud,
      iss: tokenInfo.iss,
      jti: tokenInfo.jti
    });
  }
  
  private async introspectToken(token: string, hint?: string): Promise<any> {
    // Try as access token first
    const payload = await this.jwtService.verifyToken(token);
    if (payload) {
      return {
        active: true,
        ...payload
      };
    }
    
    // Try as refresh token
    if (hint === 'refresh_token' || !hint) {
      const refreshToken = await this.refreshTokenService.findByToken(token);
      if (refreshToken && refreshToken.expiresAt > new Date()) {
        return {
          active: true,
          scope: refreshToken.scopes.join(' '),
          client_id: refreshToken.clientId,
          sub: refreshToken.userId,
          exp: Math.floor(refreshToken.expiresAt.getTime() / 1000),
          token_type: 'refresh_token'
        };
      }
    }
    
    return { active: false };
  }
}
```

## Validation Loop

### Level 1: OAuth Flow Testing
```typescript
// Test OAuth authorization code flow
describe('OAuth Authorization Code Flow', () => {
  test('complete authorization flow', async () => {
    // 1. Start authorization
    const authResponse = await request(app)
      .get('/oauth/authorize')
      .query({
        response_type: 'code',
        client_id: 'test_client',
        redirect_uri: 'https://example.com/callback',
        scope: 'read:analysis',
        state: 'random_state'
      })
      .expect(302);
    
    // 2. Get authorization code
    const callbackUrl = new URL(authResponse.headers.location);
    const code = callbackUrl.searchParams.get('code');
    expect(code).toBeDefined();
    
    // 3. Exchange code for token
    const tokenResponse = await request(app)
      .post('/oauth/token')
      .send({
        grant_type: 'authorization_code',
        code,
        redirect_uri: 'https://example.com/callback',
        client_id: 'test_client',
        client_secret: 'test_secret'
      })
      .expect(200);
    
    expect(tokenResponse.body.access_token).toBeDefined();
    expect(tokenResponse.body.token_type).toBe('Bearer');
  });
});
```

### Level 2: Security Testing
```typescript
// Test security measures
describe('OAuth Security', () => {
  test('PKCE validation', async () => {
    const codeVerifier = generateCodeVerifier();
    const codeChallenge = generateCodeChallenge(codeVerifier);
    
    // Authorization with PKCE
    const authResponse = await request(app)
      .get('/oauth/authorize')
      .query({
        response_type: 'code',
        client_id: 'public_client',
        redirect_uri: 'https://example.com/callback',
        code_challenge: codeChallenge,
        code_challenge_method: 'S256'
      });
    
    const code = extractCodeFromRedirect(authResponse.headers.location);
    
    // Token exchange with wrong verifier should fail
    await request(app)
      .post('/oauth/token')
      .send({
        grant_type: 'authorization_code',
        code,
        client_id: 'public_client',
        code_verifier: 'wrong_verifier'
      })
      .expect(400);
  });
  
  test('scope validation', async () => {
    const token = await getValidToken(['read:analysis']);
    
    // Should allow access with correct scope
    await request(app)
      .get('/api/analysis')
      .set('Authorization', `Bearer ${token}`)
      .expect(200);
    
    // Should deny access to endpoint requiring different scope
    await request(app)
      .post('/api/patterns')
      .set('Authorization', `Bearer ${token}`)
      .expect(403);
  });
});
```

### Level 3: Load Testing
```javascript
// k6 OAuth load test
import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { duration: '2m', target: 50 },
    { duration: '5m', target: 50 },
    { duration: '2m', target: 0 },
  ],
};

export default function() {
  // Test token endpoint
  const response = http.post('https://auth.ccl.dev/oauth/token', {
    grant_type: 'client_credentials',
    client_id: 'load_test_client',
    client_secret: 'load_test_secret',
    scope: 'read:analysis'
  });
  
  check(response, {
    'token generated': (r) => r.status === 200,
    'has access token': (r) => r.json('access_token') !== undefined,
    'response time < 200ms': (r) => r.timings.duration < 200,
  });
}
```

## Final Validation Checklist

- [ ] OAuth 2.0 flows implemented correctly
- [ ] PKCE support for public clients
- [ ] Secure token generation and storage
- [ ] Proper scope validation
- [ ] Client authentication working
- [ ] Consent screen functional
- [ ] Token introspection available
- [ ] Refresh token rotation enabled
- [ ] Audit logging implemented
- [ ] Security testing passed

## Anti-Patterns to Avoid

1. **DON'T store tokens in plain text** - Always encrypt or hash
2. **DON'T skip PKCE for public clients** - Security vulnerability
3. **DON'T use long-lived access tokens** - Minimize exposure window
4. **DON'T expose client secrets** - Keep confidential
5. **DON'T skip redirect URI validation** - Prevents token theft
6. **DON'T allow broad scopes by default** - Principle of least privilege
7. **DON'T reuse authorization codes** - Single use only
8. **DON'T skip state parameter** - CSRF protection
9. **DON'T ignore token expiration** - Always validate
10. **DON'T log sensitive data** - Tokens and secrets are sensitive