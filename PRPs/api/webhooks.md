# Webhooks Implementation

name: "Webhooks Implementation"
description: |
  Reliable webhook system for real-time event notifications to external applications and integrations.
  
  Core Principles:
  - **Reliable Delivery**: Retry failed deliveries with exponential backoff
  - **Security**: HMAC signature verification for authenticity
  - **Scalable**: Async processing with queue-based delivery
  - **Flexible**: Support for multiple event types and filters
  - **Debuggable**: Comprehensive logging and delivery status tracking

## Goal

Implement a robust webhook system that delivers real-time notifications about CCL events to external applications with guaranteed delivery and strong security.

## Why

Webhooks enable:
- Real-time integrations with external systems
- Event-driven automation workflows
- Third-party application synchronization
- Custom business logic triggers
- Monitoring and alerting systems

This provides:
- Immediate event notifications
- Reduced polling overhead
- Better user experience
- System integration capabilities
- Automated workflow triggers

## What

### User-Visible Behavior
- Webhook endpoint management UI
- Real-time delivery status
- Event filtering options
- Retry attempts tracking
- Delivery failure alerts

### Technical Requirements
- [ ] Event-driven webhook triggers
- [ ] HMAC signature verification
- [ ] Exponential backoff retry logic
- [ ] Delivery attempt logging
- [ ] Event filtering and subscription
- [ ] Rate limiting protection
- [ ] Dead letter queue handling
- [ ] Webhook endpoint validation

### Success Criteria
- [ ] >99% delivery success rate
- [ ] <5s delivery latency
- [ ] Secure signature verification
- [ ] Comprehensive event coverage
- [ ] Reliable retry mechanism

## All Needed Context

### Documentation & References
- url: https://docs.github.com/en/developers/webhooks-and-events/webhooks
  why: Industry standard webhook patterns
- url: https://stripe.com/docs/webhooks
  why: Webhook security best practices
- url: https://cloud.google.com/pubsub/docs/overview
  why: Pub/Sub for reliable message delivery
- file: docs/api/README.md
  why: Current webhook specifications

### Webhook Event Types

```yaml
Event Categories:
  analysis:
    - analysis.started
    - analysis.completed
    - analysis.failed
    
  patterns:
    - pattern.detected
    - pattern.validated
    - pattern.published
    
  repositories:
    - repository.added
    - repository.updated
    - repository.removed
    
  marketplace:
    - pattern.purchased
    - pattern.downloaded
    
  conversations:
    - query.completed
    - conversation.started
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Always verify HMAC signatures
- **CRITICAL**: Implement idempotency for duplicate events
- **GOTCHA**: Webhook URLs must be publicly accessible
- **GOTCHA**: Some firewalls block webhook deliveries
- **WARNING**: Large payloads may be rejected by endpoints
- **TIP**: Use exponential backoff for retries
- **TIP**: Include event timestamps for ordering

## Implementation Blueprint

### Webhook Configuration Management

```typescript
// models/webhook.ts
export interface WebhookEndpoint {
  id: string;
  userId: string;
  organizationId?: string;
  
  // Endpoint configuration
  url: string;
  description: string;
  secret: string; // For HMAC signature
  
  // Event filtering
  events: string[]; // Subscribed events
  filters: WebhookFilter[];
  
  // Settings
  active: boolean;
  retryEnabled: boolean;
  maxRetries: number;
  timeoutSeconds: number;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  lastTriggeredAt?: Date;
  
  // Statistics
  totalDeliveries: number;
  successfulDeliveries: number;
  failedDeliveries: number;
}

export interface WebhookFilter {
  field: string; // e.g., 'repository.id', 'pattern.type'
  operator: 'equals' | 'contains' | 'starts_with' | 'in';
  value: string | string[];
}

export interface WebhookDelivery {
  id: string;
  webhookId: string;
  eventId: string;
  
  // Request details
  url: string;
  method: string;
  headers: Record<string, string>;
  payload: any;
  
  // Response details
  statusCode?: number;
  responseHeaders?: Record<string, string>;
  responseBody?: string;
  
  // Timing
  attemptNumber: number;
  deliveredAt?: Date;
  duration?: number; // milliseconds
  
  // Status
  status: 'pending' | 'success' | 'failed' | 'abandoned';
  error?: string;
  
  // Metadata
  createdAt: Date;
}

export class WebhookService {
  constructor(
    private webhookRepository: WebhookRepository,
    private deliveryRepository: WebhookDeliveryRepository,
    private queueService: QueueService,
    private cryptoService: CryptoService
  ) {}
  
  async createWebhook(data: CreateWebhookRequest): Promise<WebhookEndpoint> {
    // Validate webhook URL
    await this.validateWebhookUrl(data.url);
    
    // Generate secret for HMAC
    const secret = this.cryptoService.generateSecret();
    
    const webhook: WebhookEndpoint = {
      id: this.generateWebhookId(),
      userId: data.userId,
      organizationId: data.organizationId,
      url: data.url,
      description: data.description,
      secret,
      events: data.events,
      filters: data.filters || [],
      active: true,
      retryEnabled: true,
      maxRetries: 5,
      timeoutSeconds: 30,
      createdAt: new Date(),
      updatedAt: new Date(),
      totalDeliveries: 0,
      successfulDeliveries: 0,
      failedDeliveries: 0
    };
    
    await this.webhookRepository.save(webhook);
    
    // Send test delivery
    await this.sendTestDelivery(webhook);
    
    return webhook;
  }
  
  async triggerWebhooks(event: WebhookEvent): Promise<void> {
    // Find matching webhooks
    const webhooks = await this.findMatchingWebhooks(event);
    
    // Queue deliveries
    const deliveryPromises = webhooks.map(webhook => 
      this.queueDelivery(webhook, event)
    );
    
    await Promise.all(deliveryPromises);
  }
  
  private async findMatchingWebhooks(event: WebhookEvent): Promise<WebhookEndpoint[]> {
    // Get all active webhooks for the event type
    const candidateWebhooks = await this.webhookRepository.findByEvent(event.type);
    
    // Filter by organization/user access
    const accessibleWebhooks = candidateWebhooks.filter(webhook => 
      this.hasAccess(webhook, event)
    );
    
    // Apply custom filters
    return accessibleWebhooks.filter(webhook => 
      this.matchesFilters(webhook.filters, event.data)
    );
  }
  
  private async queueDelivery(webhook: WebhookEndpoint, event: WebhookEvent): Promise<void> {
    const delivery: WebhookDelivery = {
      id: this.generateDeliveryId(),
      webhookId: webhook.id,
      eventId: event.id,
      url: webhook.url,
      method: 'POST',
      headers: this.buildHeaders(webhook, event),
      payload: this.buildPayload(event),
      attemptNumber: 1,
      status: 'pending',
      createdAt: new Date()
    };
    
    await this.deliveryRepository.save(delivery);
    
    // Queue for delivery
    await this.queueService.enqueue('webhook-delivery', {
      deliveryId: delivery.id,
      delayMs: 0
    });
  }
  
  private buildHeaders(webhook: WebhookEndpoint, event: WebhookEvent): Record<string, string> {
    const timestamp = Math.floor(Date.now() / 1000);
    const signature = this.generateSignature(webhook.secret, event, timestamp);
    
    return {
      'Content-Type': 'application/json',
      'User-Agent': 'CCL-Webhooks/1.0',
      'X-CCL-Event': event.type,
      'X-CCL-Delivery': this.generateDeliveryId(),
      'X-CCL-Signature-256': signature,
      'X-CCL-Timestamp': timestamp.toString()
    };
  }
  
  private generateSignature(secret: string, event: WebhookEvent, timestamp: number): string {
    const payload = JSON.stringify(event.data);
    const signaturePayload = `${timestamp}.${payload}`;
    
    const hmac = crypto.createHmac('sha256', secret);
    hmac.update(signaturePayload);
    return `sha256=${hmac.digest('hex')}`;
  }
}
```

### Webhook Delivery Engine

```typescript
// services/webhookDelivery.ts
export class WebhookDeliveryService {
  constructor(
    private deliveryRepository: WebhookDeliveryRepository,
    private webhookRepository: WebhookRepository,
    private httpClient: HTTPClient,
    private queueService: QueueService
  ) {}
  
  async processDelivery(deliveryId: string): Promise<void> {
    const delivery = await this.deliveryRepository.findById(deliveryId);
    if (!delivery) {
      throw new Error(`Delivery ${deliveryId} not found`);
    }
    
    const webhook = await this.webhookRepository.findById(delivery.webhookId);
    if (!webhook || !webhook.active) {
      await this.markDeliveryAbandoned(delivery, 'Webhook inactive');
      return;
    }
    
    try {
      await this.attemptDelivery(delivery, webhook);
    } catch (error) {
      await this.handleDeliveryFailure(delivery, webhook, error);
    }
  }
  
  private async attemptDelivery(delivery: WebhookDelivery, webhook: WebhookEndpoint): Promise<void> {
    const startTime = Date.now();
    
    try {
      const response = await this.httpClient.post(delivery.url, {
        headers: delivery.headers,
        data: delivery.payload,
        timeout: webhook.timeoutSeconds * 1000,
        validateStatus: (status) => status >= 200 && status < 300
      });
      
      const duration = Date.now() - startTime;
      
      // Update delivery as successful
      await this.deliveryRepository.update(delivery.id, {
        status: 'success',
        statusCode: response.status,
        responseHeaders: response.headers,
        responseBody: this.truncateResponse(response.data),
        deliveredAt: new Date(),
        duration
      });
      
      // Update webhook statistics
      await this.webhookRepository.update(webhook.id, {
        totalDeliveries: webhook.totalDeliveries + 1,
        successfulDeliveries: webhook.successfulDeliveries + 1,
        lastTriggeredAt: new Date()
      });
      
      logger.info('Webhook delivery successful', {
        deliveryId: delivery.id,
        webhookId: webhook.id,
        statusCode: response.status,
        duration
      });
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Update delivery as failed
      await this.deliveryRepository.update(delivery.id, {
        status: 'failed',
        statusCode: error.response?.status,
        responseHeaders: error.response?.headers,
        responseBody: this.truncateResponse(error.response?.data),
        duration,
        error: error.message
      });
      
      throw error;
    }
  }
  
  private async handleDeliveryFailure(
    delivery: WebhookDelivery,
    webhook: WebhookEndpoint,
    error: any
  ): Promise<void> {
    // Update webhook statistics
    await this.webhookRepository.update(webhook.id, {
      totalDeliveries: webhook.totalDeliveries + 1,
      failedDeliveries: webhook.failedDeliveries + 1
    });
    
    // Check if we should retry
    if (webhook.retryEnabled && delivery.attemptNumber < webhook.maxRetries) {
      await this.scheduleRetry(delivery, webhook);
    } else {
      await this.markDeliveryAbandoned(delivery, 'Max retries exceeded');
      
      // Optionally disable webhook after too many failures
      await this.checkWebhookHealth(webhook);
    }
    
    logger.warn('Webhook delivery failed', {
      deliveryId: delivery.id,
      webhookId: webhook.id,
      attemptNumber: delivery.attemptNumber,
      error: error.message,
      willRetry: delivery.attemptNumber < webhook.maxRetries
    });
  }
  
  private async scheduleRetry(delivery: WebhookDelivery, webhook: WebhookEndpoint): Promise<void> {
    const nextAttempt = delivery.attemptNumber + 1;
    const delayMs = this.calculateRetryDelay(nextAttempt);
    
    // Create new delivery attempt
    const retryDelivery: WebhookDelivery = {
      ...delivery,
      id: this.generateDeliveryId(),
      attemptNumber: nextAttempt,
      status: 'pending',
      createdAt: new Date(),
      statusCode: undefined,
      responseHeaders: undefined,
      responseBody: undefined,
      deliveredAt: undefined,
      duration: undefined,
      error: undefined
    };
    
    await this.deliveryRepository.save(retryDelivery);
    
    // Queue for retry
    await this.queueService.enqueue('webhook-delivery', {
      deliveryId: retryDelivery.id,
      delayMs
    });
  }
  
  private calculateRetryDelay(attemptNumber: number): number {
    // Exponential backoff: 2^attempt seconds (with jitter)
    const baseDelay = Math.pow(2, attemptNumber) * 1000;
    const jitter = Math.random() * 0.1 * baseDelay;
    return Math.floor(baseDelay + jitter);
  }
  
  private async checkWebhookHealth(webhook: WebhookEndpoint): Promise<void> {
    const recentDeliveries = await this.deliveryRepository.findRecent(webhook.id, 100);
    const failureRate = recentDeliveries.filter(d => d.status === 'failed').length / recentDeliveries.length;
    
    // Disable webhook if failure rate is too high
    if (failureRate > 0.95 && recentDeliveries.length >= 20) {
      await this.webhookRepository.update(webhook.id, {
        active: false,
        updatedAt: new Date()
      });
      
      // Notify webhook owner
      await this.notifyWebhookDisabled(webhook);
      
      logger.warn('Webhook automatically disabled due to high failure rate', {
        webhookId: webhook.id,
        failureRate,
        totalDeliveries: recentDeliveries.length
      });
    }
  }
}
```

### Event System Integration

```typescript
// events/webhookEvents.ts
export interface WebhookEvent {
  id: string;
  type: string;
  data: any;
  timestamp: Date;
  source: string;
}

export class WebhookEventEmitter {
  constructor(private webhookService: WebhookService) {}
  
  // Analysis events
  async emitAnalysisStarted(analysis: Analysis): Promise<void> {
    await this.emit('analysis.started', {
      analysis: {
        id: analysis.id,
        repository_id: analysis.repositoryId,
        status: analysis.status,
        started_at: analysis.startedAt,
        commit: analysis.commit,
        branch: analysis.branch
      }
    });
  }
  
  async emitAnalysisCompleted(analysis: Analysis): Promise<void> {
    await this.emit('analysis.completed', {
      analysis: {
        id: analysis.id,
        repository_id: analysis.repositoryId,
        status: analysis.status,
        started_at: analysis.startedAt,
        completed_at: analysis.completedAt,
        duration_seconds: analysis.durationSeconds,
        statistics: analysis.statistics,
        commit: analysis.commit,
        branch: analysis.branch
      }
    });
  }
  
  async emitAnalysisFailed(analysis: Analysis, error: string): Promise<void> {
    await this.emit('analysis.failed', {
      analysis: {
        id: analysis.id,
        repository_id: analysis.repositoryId,
        status: analysis.status,
        started_at: analysis.startedAt,
        failed_at: new Date(),
        error_message: error,
        commit: analysis.commit,
        branch: analysis.branch
      }
    });
  }
  
  // Pattern events
  async emitPatternDetected(pattern: Pattern, analysis: Analysis): Promise<void> {
    await this.emit('pattern.detected', {
      pattern: {
        id: pattern.id,
        repository_id: pattern.repositoryId,
        analysis_id: pattern.analysisId,
        type: pattern.type,
        name: pattern.name,
        confidence: pattern.confidence,
        occurrences: pattern.occurrences,
        locations: pattern.locations
      },
      analysis: {
        id: analysis.id,
        commit: analysis.commit,
        branch: analysis.branch
      }
    });
  }
  
  // Repository events
  async emitRepositoryAdded(repository: Repository): Promise<void> {
    await this.emit('repository.added', {
      repository: {
        id: repository.id,
        name: repository.name,
        url: repository.url,
        provider: repository.provider,
        owner_id: repository.ownerId,
        organization_id: repository.organizationId,
        created_at: repository.createdAt
      }
    });
  }
  
  // Query events
  async emitQueryCompleted(query: QueryExecution): Promise<void> {
    await this.emit('query.completed', {
      query: {
        id: query.id,
        conversation_id: query.conversationId,
        repository_id: query.repositoryId,
        query_text: query.queryText,
        answer: query.answer,
        confidence: query.confidence,
        response_time_ms: query.responseTimeMs,
        tokens_used: query.tokensUsed,
        sources_count: query.sourcesCount,
        completed_at: query.completedAt
      }
    });
  }
  
  // Marketplace events
  async emitPatternPurchased(purchase: PatternPurchase): Promise<void> {
    await this.emit('pattern.purchased', {
      purchase: {
        id: purchase.id,
        pattern_id: purchase.patternId,
        user_id: purchase.userId,
        price_cents: purchase.priceCents,
        currency: purchase.currency,
        purchased_at: purchase.purchasedAt
      }
    });
  }
  
  private async emit(eventType: string, data: any): Promise<void> {
    const event: WebhookEvent = {
      id: crypto.randomUUID(),
      type: eventType,
      data,
      timestamp: new Date(),
      source: 'ccl-api'
    };
    
    try {
      await this.webhookService.triggerWebhooks(event);
    } catch (error) {
      logger.error('Failed to trigger webhooks', {
        eventType,
        eventId: event.id,
        error: error.message
      });
    }
  }
}

// Integration with existing services
export function setupWebhookIntegration(eventEmitter: WebhookEventEmitter) {
  // Analysis service integration
  AnalysisService.on('analysis.started', (analysis) => {
    eventEmitter.emitAnalysisStarted(analysis);
  });
  
  AnalysisService.on('analysis.completed', (analysis) => {
    eventEmitter.emitAnalysisCompleted(analysis);
  });
  
  AnalysisService.on('analysis.failed', (analysis, error) => {
    eventEmitter.emitAnalysisFailed(analysis, error);
  });
  
  // Pattern service integration
  PatternService.on('pattern.detected', (pattern, analysis) => {
    eventEmitter.emitPatternDetected(pattern, analysis);
  });
  
  // Repository service integration
  RepositoryService.on('repository.added', (repository) => {
    eventEmitter.emitRepositoryAdded(repository);
  });
  
  // Query service integration
  QueryService.on('query.completed', (query) => {
    eventEmitter.emitQueryCompleted(query);
  });
}
```

### Webhook Signature Verification

```typescript
// utils/webhookSecurity.ts
export class WebhookSecurity {
  static verifySignature(
    payload: string,
    signature: string,
    secret: string,
    timestamp: number,
    tolerance: number = 300 // 5 minutes
  ): boolean {
    // Check timestamp tolerance
    const currentTime = Math.floor(Date.now() / 1000);
    if (Math.abs(currentTime - timestamp) > tolerance) {
      throw new Error('Request timestamp outside tolerance');
    }
    
    // Extract signature from header
    const elements = signature.split(',');
    const signatureElements: Record<string, string> = {};
    
    for (const element of elements) {
      const [key, value] = element.split('=');
      signatureElements[key] = value;
    }
    
    if (!signatureElements.sha256) {
      throw new Error('No sha256 signature found');
    }
    
    // Compute expected signature
    const signaturePayload = `${timestamp}.${payload}`;
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(signaturePayload)
      .digest('hex');
    
    // Use constant time comparison
    return crypto.timingSafeEqual(
      Buffer.from(signatureElements.sha256, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }
  
  static generateTestSignature(payload: string, secret: string): { signature: string; timestamp: number } {
    const timestamp = Math.floor(Date.now() / 1000);
    const signaturePayload = `${timestamp}.${payload}`;
    const signature = crypto
      .createHmac('sha256', secret)
      .update(signaturePayload)
      .digest('hex');
    
    return {
      signature: `sha256=${signature}`,
      timestamp
    };
  }
}

// Express middleware for webhook signature verification
export function verifyWebhookSignature(secret: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    const signature = req.headers['x-ccl-signature-256'] as string;
    const timestamp = parseInt(req.headers['x-ccl-timestamp'] as string);
    
    if (!signature || !timestamp) {
      return res.status(400).json({
        error: 'Missing signature or timestamp'
      });
    }
    
    try {
      const isValid = WebhookSecurity.verifySignature(
        req.body,
        signature,
        secret,
        timestamp
      );
      
      if (!isValid) {
        return res.status(401).json({
          error: 'Invalid signature'
        });
      }
      
      next();
    } catch (error) {
      return res.status(401).json({
        error: error.message
      });
    }
  };
}
```

### Webhook Management API

```typescript
// routes/webhooks.ts
export class WebhookController {
  // Create webhook endpoint
  async createWebhook(req: Request, res: Response) {
    try {
      const webhook = await this.webhookService.createWebhook({
        userId: req.user.id,
        organizationId: req.body.organization_id,
        url: req.body.url,
        description: req.body.description,
        events: req.body.events,
        filters: req.body.filters
      });
      
      res.status(201).json({
        webhook: this.formatWebhookResponse(webhook)
      });
    } catch (error) {
      res.status(400).json({
        error: error.message
      });
    }
  }
  
  // List user's webhooks
  async listWebhooks(req: Request, res: Response) {
    const webhooks = await this.webhookService.findByUser(req.user.id);
    
    res.json({
      webhooks: webhooks.map(w => this.formatWebhookResponse(w))
    });
  }
  
  // Get webhook details
  async getWebhook(req: Request, res: Response) {
    const webhook = await this.webhookService.findById(req.params.id);
    
    if (!webhook || !this.canAccessWebhook(req.user, webhook)) {
      return res.status(404).json({
        error: 'Webhook not found'
      });
    }
    
    res.json({
      webhook: this.formatWebhookResponse(webhook)
    });
  }
  
  // Update webhook
  async updateWebhook(req: Request, res: Response) {
    const webhook = await this.webhookService.updateWebhook(req.params.id, {
      url: req.body.url,
      description: req.body.description,
      events: req.body.events,
      filters: req.body.filters,
      active: req.body.active
    });
    
    res.json({
      webhook: this.formatWebhookResponse(webhook)
    });
  }
  
  // Delete webhook
  async deleteWebhook(req: Request, res: Response) {
    await this.webhookService.deleteWebhook(req.params.id);
    
    res.status(204).send();
  }
  
  // Get webhook deliveries
  async getWebhookDeliveries(req: Request, res: Response) {
    const deliveries = await this.webhookService.getDeliveries(
      req.params.id,
      {
        limit: parseInt(req.query.limit as string) || 20,
        offset: parseInt(req.query.offset as string) || 0
      }
    );
    
    res.json({
      deliveries: deliveries.map(d => this.formatDeliveryResponse(d))
    });
  }
  
  // Redeliver webhook
  async redeliverWebhook(req: Request, res: Response) {
    const delivery = await this.webhookService.redeliver(req.params.deliveryId);
    
    res.json({
      delivery: this.formatDeliveryResponse(delivery)
    });
  }
  
  // Test webhook endpoint
  async testWebhook(req: Request, res: Response) {
    const result = await this.webhookService.testWebhook(req.params.id);
    
    res.json({
      test_result: result
    });
  }
  
  private formatWebhookResponse(webhook: WebhookEndpoint) {
    return {
      id: webhook.id,
      url: webhook.url,
      description: webhook.description,
      events: webhook.events,
      filters: webhook.filters,
      active: webhook.active,
      created_at: webhook.createdAt,
      updated_at: webhook.updatedAt,
      last_triggered_at: webhook.lastTriggeredAt,
      statistics: {
        total_deliveries: webhook.totalDeliveries,
        successful_deliveries: webhook.successfulDeliveries,
        failed_deliveries: webhook.failedDeliveries,
        success_rate: webhook.totalDeliveries > 0 
          ? webhook.successfulDeliveries / webhook.totalDeliveries 
          : 0
      }
    };
  }
}
```

## Validation Loop

### Level 1: Webhook Delivery Testing
```typescript
// Test webhook delivery functionality
describe('Webhook Delivery', () => {
  test('successful delivery', async () => {
    const mockEndpoint = nock('https://example.com')
      .post('/webhook')
      .reply(200, { received: true });
    
    const webhook = await createTestWebhook({
      url: 'https://example.com/webhook',
      events: ['analysis.completed']
    });
    
    const event = createTestEvent('analysis.completed');
    await webhookService.triggerWebhooks(event);
    
    // Wait for async delivery
    await new Promise(resolve => setTimeout(resolve, 100));
    
    expect(mockEndpoint.isDone()).toBe(true);
    
    const deliveries = await getWebhookDeliveries(webhook.id);
    expect(deliveries[0].status).toBe('success');
  });
  
  test('retry on failure', async () => {
    const mockEndpoint = nock('https://example.com')
      .post('/webhook')
      .reply(500, 'Internal Server Error')
      .post('/webhook')
      .reply(200, { received: true });
    
    const event = createTestEvent('analysis.completed');
    await webhookService.triggerWebhooks(event);
    
    // Wait for retry
    await new Promise(resolve => setTimeout(resolve, 2500));
    
    expect(mockEndpoint.isDone()).toBe(true);
  });
});
```

### Level 2: Security Testing
```typescript
// Test webhook signature verification
describe('Webhook Security', () => {
  test('signature verification', () => {
    const payload = JSON.stringify({ test: 'data' });
    const secret = 'test_secret';
    const timestamp = Math.floor(Date.now() / 1000);
    
    const { signature } = WebhookSecurity.generateTestSignature(payload, secret);
    
    const isValid = WebhookSecurity.verifySignature(
      payload,
      signature,
      secret,
      timestamp
    );
    
    expect(isValid).toBe(true);
  });
  
  test('rejects invalid signature', () => {
    const payload = JSON.stringify({ test: 'data' });
    const secret = 'test_secret';
    const timestamp = Math.floor(Date.now() / 1000);
    
    expect(() => {
      WebhookSecurity.verifySignature(
        payload,
        'sha256=invalid_signature',
        secret,
        timestamp
      );
    }).toThrow();
  });
});
```

### Level 3: Load Testing
```typescript
// Test webhook system under load
describe('Webhook Load Testing', () => {
  test('handles high volume', async () => {
    const events = Array.from({ length: 1000 }, (_, i) => 
      createTestEvent('pattern.detected', { id: i })
    );
    
    const startTime = Date.now();
    
    // Trigger all events
    await Promise.all(
      events.map(event => webhookService.triggerWebhooks(event))
    );
    
    const queueTime = Date.now() - startTime;
    expect(queueTime).toBeLessThan(5000); // Should queue within 5s
    
    // Wait for deliveries to complete
    await waitForDeliveries(events.length);
    
    const totalTime = Date.now() - startTime;
    expect(totalTime).toBeLessThan(30000); // Should complete within 30s
  });
});
```

## Final Validation Checklist

- [ ] Event-driven webhook triggers working
- [ ] HMAC signature verification implemented
- [ ] Retry logic with exponential backoff
- [ ] Delivery status tracking complete
- [ ] Event filtering and subscriptions working
- [ ] Rate limiting protects against abuse
- [ ] Dead letter queue handles failed deliveries
- [ ] Webhook endpoint validation functional
- [ ] Management API fully implemented
- [ ] Security testing passed

## Anti-Patterns to Avoid

1. **DON'T skip signature verification** - Security vulnerability
2. **DON'T retry indefinitely** - Can overwhelm systems
3. **DON'T send large payloads** - May be rejected
4. **DON'T ignore delivery failures** - Silent data loss
5. **DON'T synchronously deliver** - Blocks request processing
6. **DON'T expose sensitive data** - Filter webhook payloads
7. **DON'T forget idempotency** - Duplicate events cause issues
8. **DON'T skip timeout configuration** - Hanging requests
9. **DON'T allow unlimited retries** - Resource exhaustion
10. **DON'T ignore webhook health** - Degraded service quality