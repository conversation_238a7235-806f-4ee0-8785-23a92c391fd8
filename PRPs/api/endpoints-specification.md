# API Endpoints Specification

name: "API Endpoints Specification"
description: |
  Complete REST API endpoint specifications for the CCL platform including all routes, parameters, request/response formats, and error handling.
  
  Core Principles:
  - **RESTful Design**: Follow REST conventions consistently
  - **Versioned API**: Support backward compatibility
  - **Comprehensive Validation**: Input validation on all endpoints
  - **Consistent Responses**: Standardized response formats
  - **Rate Limited**: Protect against abuse

## Goal

Define and implement all REST API endpoints for CCL platform with complete request/response specifications, validation rules, and error handling patterns.

## Why

The API is the primary interface for:
- External integrations
- SDK implementations
- Web and mobile applications
- Third-party developers
- Enterprise customers

This specification ensures:
- Consistent API behavior
- Clear documentation
- Easy SDK development
- Reliable integrations
- Scalable API design

## What

### User-Visible Behavior
- Predictable API responses
- Clear error messages
- Fast response times
- Comprehensive documentation
- Version stability

### Technical Requirements
- [ ] All endpoints documented with OpenAPI 3.0
- [ ] Request/response validation
- [ ] Standardized error responses
- [ ] Rate limiting per tier
- [ ] API versioning strategy
- [ ] Pagination for list endpoints
- [ ] Filtering and sorting support
- [ ] CORS configuration

### Success Criteria
- [ ] <100ms response time (p95)
- [ ] 99.9% uptime SLA
- [ ] All endpoints documented
- [ ] SDK generation from OpenAPI
- [ ] Rate limits enforced

## All Needed Context

### Documentation & References
- url: https://swagger.io/specification/
  why: OpenAPI 3.0 specification
- url: https://cloud.google.com/apigee/docs/api-platform/get-started/what-apigee
  why: API Gateway configuration
- file: docs/api/README.md
  why: Current API documentation source

### Base Configuration

```yaml
API Configuration:
  base_url: https://api.ccl.dev
  versions: [v1, v2-beta]
  protocols: [https]
  content_type: application/json
  
Rate Limits:
  free: 1000/hour (burst: 50, concurrent: 10)
  pro: 10000/hour (burst: 500, concurrent: 50)
  team: 100000/hour (burst: 5000, concurrent: 200)
  enterprise: unlimited
  
Common Headers:
  request:
    - Authorization: Bearer <token> (required)
    - Content-Type: application/json (required for POST/PUT)
    - Accept: application/json (optional)
    - X-Request-ID: <unique_id> (optional)
    - X-CCL-Version: <version> (optional)
  response:
    - X-Request-ID: <request_id>
    - X-Response-Time: <processing_time_ms>
    - X-CCL-Version: <api_version>
    - Cache-Control: <caching_directives>
    - ETag: <resource_version>
    - X-RateLimit-Limit: <limit>
    - X-RateLimit-Remaining: <remaining>
    - X-RateLimit-Reset: <reset_timestamp>
    - X-RateLimit-Reset-After: <seconds>
```

### Authentication & Authorization

```yaml
Authentication Types:
  api_key:
    - Bearer token in Authorization header
    - API key in X-API-Key header
  oauth2:
    - Authorization Code flow for web apps
    - Client Credentials flow for server apps
    - Refresh tokens for long-lived access
    
API Key Scopes:
  read:analysis: Read analysis results
  write:analysis: Create analyses
  read:patterns: Read patterns
  write:patterns: Create/modify patterns
  read:queries: Read conversations
  write:queries: Create queries
  admin: Full access
  
OAuth 2.0 Flow:
  authorize_url: https://auth.ccl.dev/oauth/authorize
  token_url: https://auth.ccl.dev/oauth/token
  scopes: [read:analysis, write:analysis, read:patterns, write:patterns]
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Always validate repository ownership
- **CRITICAL**: Sanitize all user inputs
- **CRITICAL**: Verify webhook signatures using HMAC-SHA256
- **GOTCHA**: Pagination tokens expire after 24 hours
- **GOTCHA**: Large response bodies are automatically paginated
- **GOTCHA**: Streaming responses use Server-Sent Events (SSE)
- **WARNING**: File uploads limited to 100MB
- **WARNING**: Analysis timeout after 30 minutes
- **TIP**: Use ETag headers for caching
- **TIP**: Include request ID in all responses
- **TIP**: Use cursor-based pagination for real-time data
- **TIP**: Enable webhook retries with exponential backoff

## Implementation Blueprint

### Core Endpoint Groups

```yaml
# OpenAPI 3.0 Specification
openapi: 3.0.0
info:
  title: CCL API
  version: 1.0.0
  description: Codebase Context Layer API
  
servers:
  - url: https://api.ccl.dev/v1
    description: Production API
  - url: https://staging-api.ccl.dev/v1
    description: Staging API
    
security:
  - bearerAuth: []
  - apiKey: []
    
paths:
  # Analysis Endpoints
  /analyze:
    post:
      summary: Start repository analysis
      operationId: startAnalysis
      tags:
        - Analysis
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnalysisRequest'
      responses:
        '202':
          description: Analysis started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'
          
  /analysis/{analysisId}:
    get:
      summary: Get analysis status and results
      operationId: getAnalysis
      tags:
        - Analysis
      parameters:
        - name: analysisId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Analysis details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisDetails'
        '404':
          $ref: '#/components/responses/NotFound'
          
  /analysis:
    get:
      summary: List analyses
      operationId: listAnalyses
      tags:
        - Analysis
      parameters:
        - name: repository_id
          in: query
          schema:
            type: string
        - name: status
          in: query
          schema:
            type: string
            enum: [pending, running, completed, failed]
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of analyses
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisList'
                
  # Pattern Endpoints
  /patterns:
    get:
      summary: List detected patterns
      operationId: listPatterns
      tags:
        - Patterns
      parameters:
        - name: repository_id
          in: query
          schema:
            type: string
        - name: type
          in: query
          schema:
            type: string
            enum: [architectural, design, idiom, anti-pattern]
        - name: language
          in: query
          schema:
            type: string
        - name: min_confidence
          in: query
          schema:
            type: number
            minimum: 0
            maximum: 1
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: List of patterns
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PatternList'
                
    post:
      summary: Create custom pattern
      operationId: createPattern
      tags:
        - Patterns
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatternCreate'
      responses:
        '201':
          description: Pattern created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Pattern'
                
  /patterns/{patternId}:
    get:
      summary: Get pattern details
      operationId: getPattern
      tags:
        - Patterns
      parameters:
        - name: patternId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Pattern details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PatternDetails'
                
  /patterns/validate:
    post:
      summary: Validate code against patterns
      operationId: validatePatterns
      tags:
        - Patterns
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatternValidationRequest'
      responses:
        '200':
          description: Validation results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PatternValidationResponse'
                
  # Query Endpoints
  /query:
    post:
      summary: Execute natural language query
      operationId: executeQuery
      tags:
        - Queries
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QueryRequest'
      responses:
        '200':
          description: Query response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryResponse'
            text/event-stream:
              schema:
                type: string
                description: Server-sent events for streaming
                
  /conversations:
    get:
      summary: List conversations
      operationId: listConversations
      tags:
        - Queries
      parameters:
        - name: repository_id
          in: query
          schema:
            type: string
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: List of conversations
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConversationList'
                
    post:
      summary: Create new conversation
      operationId: createConversation
      tags:
        - Queries
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConversationCreate'
      responses:
        '201':
          description: Conversation created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Conversation'
                
  /conversations/{conversationId}/messages:
    get:
      summary: Get conversation messages
      operationId: getMessages
      tags:
        - Queries
      parameters:
        - name: conversationId
          in: path
          required: true
          schema:
            type: string
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: List of messages
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MessageList'
    post:
      summary: Add message to conversation
      operationId: addMessage
      tags:
        - Queries
      parameters:
        - name: conversationId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                include_code:
                  type: boolean
      responses:
        '201':
          description: Message added
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryResponse'

  # Repository Endpoints
  /repositories:
    get:
      summary: List repositories
      operationId: listRepositories
      tags:
        - Repositories
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: List of repositories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RepositoryList'
                
    post:
      summary: Add repository
      operationId: addRepository
      tags:
        - Repositories
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RepositoryCreate'
      responses:
        '201':
          description: Repository added
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Repository'
                
  /repositories/{repositoryId}:
    get:
      summary: Get repository details
      operationId: getRepository
      tags:
        - Repositories
      parameters:
        - name: repositoryId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Repository details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RepositoryDetails'
    patch:
      summary: Update repository
      operationId: updateRepository
      tags:
        - Repositories
      parameters:
        - name: repositoryId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                branch:
                  type: string
                auto_analyze:
                  type: boolean
                settings:
                  type: object
                  properties:
                    exclude_paths:
                      type: array
                      items:
                        type: string
    delete:
      summary: Remove repository
      operationId: deleteRepository
      tags:
        - Repositories
      parameters:
        - name: repositoryId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Repository deleted
          
  # Marketplace Endpoints
  /marketplace/patterns:
    get:
      summary: Browse marketplace patterns
      operationId: browseMarketplace
      tags:
        - Marketplace
      parameters:
        - name: category
          in: query
          schema:
            type: string
        - name: language
          in: query
          schema:
            type: string
        - name: search
          in: query
          schema:
            type: string
        - name: sort
          in: query
          schema:
            type: string
            enum: [popular, recent, rating, price]
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: Marketplace patterns
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketplacePatternList'
    post:
      summary: Publish a pattern to the marketplace
      operationId: publishPattern
      tags:
        - Marketplace
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MarketplacePatternCreate'
      responses:
        '201':
          description: Pattern published
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketplacePattern'

  /marketplace/patterns/{patternId}:
    get:
      summary: Get marketplace pattern details
      operationId: getMarketplacePattern
      tags:
        - Marketplace
      parameters:
        - name: patternId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Marketplace pattern details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MarketplacePattern'

  /marketplace/purchase:
    post:
      summary: Purchase a pattern
      operationId: purchasePattern
      tags:
        - Marketplace
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseRequest'
      responses:
        '200':
          description: Purchase successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseResponse'
                
  /marketplace/patterns/{patternId}/reviews:
    get:
      summary: Get pattern reviews
      operationId: getPatternReviews
      tags:
        - Marketplace
      parameters:
        - name: patternId
          in: path
          required: true
          schema:
            type: string
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: Pattern reviews
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReviewList'
                
    post:
      summary: Add pattern review
      operationId: addPatternReview
      tags:
        - Marketplace
      parameters:
        - name: patternId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReviewCreateRequest'
      responses:
        '201':
          description: Review added
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Review'
                
  /marketplace/licenses:
    get:
      summary: Get user licenses
      operationId: getUserLicenses
      tags:
        - Marketplace
      parameters:
        - name: pattern_id
          in: query
          schema:
            type: string
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: User licenses
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseList'
                
  /marketplace/revenue:
    get:
      summary: Get revenue analytics (for publishers)
      operationId: getRevenueAnalytics
      tags:
        - Marketplace
      parameters:
        - name: start_date
          in: query
          schema:
            type: string
            format: date
        - name: end_date
          in: query
          schema:
            type: string
            format: date
        - name: pattern_id
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Revenue analytics
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RevenueAnalytics'
                
  /marketplace/payouts:
    get:
      summary: Get payout history
      operationId: getPayoutHistory
      tags:
        - Marketplace
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: Payout history
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PayoutList'
                
    post:
      summary: Request payout
      operationId: requestPayout
      tags:
        - Marketplace
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PayoutRequest'
      responses:
        '201':
          description: Payout requested
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PayoutResponse'

  # Webhooks Endpoints
  /webhooks:
    post:
      summary: Receive webhook notifications
      operationId: handleWebhook
      tags:
        - Webhooks
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookPayload'
      responses:
        '200':
          description: Webhook received
          
  # Code Generation Endpoints
  /generate:
    post:
      summary: Generate code from a pattern
      operationId: generateCode
      tags:
        - Code Generation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateRequest'
      responses:
        '200':
          description: Code generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateResponse'

  # Simulations Endpoints
  /simulate:
    post:
      summary: Simulate an architecture change
      operationId: simulateChange
      tags:
        - Simulations
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SimulationRequest'
      responses:
        '202':
          description: Simulation started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimulationResponse'
                
  /simulate/{simulationId}:
    get:
      summary: Get simulation results
      operationId: getSimulationResults
      tags:
        - Simulations
      parameters:
        - name: simulationId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Simulation results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimulationResults'
                
  # AI/ML Intelligence Endpoints
  /ai/embeddings:
    post:
      summary: Generate code embeddings
      operationId: generateEmbeddings
      tags:
        - AI/ML
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmbeddingRequest'
      responses:
        '200':
          description: Embeddings generated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmbeddingResponse'
                
  /ai/similarity:
    post:
      summary: Find similar code patterns
      operationId: findSimilarCode
      tags:
        - AI/ML
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SimilarityRequest'
      responses:
        '200':
          description: Similar code patterns found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SimilarityResponse'
                
  /ai/models:
    get:
      summary: List available AI models
      operationId: listAIModels
      tags:
        - AI/ML
      responses:
        '200':
          description: Available AI models
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIModelList'
                
    post:
      summary: Train custom model
      operationId: trainCustomModel
      tags:
        - AI/ML
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ModelTrainingRequest'
      responses:
        '202':
          description: Model training started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ModelTrainingResponse'
                
  /ai/models/{modelId}:
    get:
      summary: Get model details
      operationId: getModelDetails
      tags:
        - AI/ML
      parameters:
        - name: modelId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Model details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIModel'
                
  # Real-time Collaboration Endpoints
  /collaboration/sessions:
    post:
      summary: Create collaboration session
      operationId: createSession
      tags:
        - Collaboration
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionCreateRequest'
      responses:
        '201':
          description: Session created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollaborationSession'
                
    get:
      summary: List active sessions
      operationId: listSessions
      tags:
        - Collaboration
      parameters:
        - name: repository_id
          in: query
          schema:
            type: string
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/offset'
      responses:
        '200':
          description: Active sessions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionList'
                
  /collaboration/sessions/{sessionId}:
    get:
      summary: Get session details
      operationId: getSession
      tags:
        - Collaboration
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Session details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionDetails'
                
    patch:
      summary: Update session
      operationId: updateSession
      tags:
        - Collaboration
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionUpdateRequest'
      responses:
        '200':
          description: Session updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollaborationSession'
                
    delete:
      summary: End session
      operationId: endSession
      tags:
        - Collaboration
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Session ended
          
  /collaboration/sessions/{sessionId}/join:
    post:
      summary: Join collaboration session
      operationId: joinSession
      tags:
        - Collaboration
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Joined session
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionJoinResponse'
                
  /collaboration/sessions/{sessionId}/leave:
    post:
      summary: Leave collaboration session
      operationId: leaveSession
      tags:
        - Collaboration
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Left session
          
  /collaboration/sessions/{sessionId}/events:
    get:
      summary: Get session events (SSE)
      operationId: getSessionEvents
      tags:
        - Collaboration
      parameters:
        - name: sessionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Session events stream
          content:
            text/event-stream:
              schema:
                type: string
                description: Server-sent events for real-time updates
                
  # Language Parser Plugin Endpoints
  /parsers:
    get:
      summary: List available language parsers
      operationId: listParsers
      tags:
        - Language Parsers
      responses:
        '200':
          description: Available parsers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParserList'
                
    post:
      summary: Install custom parser plugin
      operationId: installParser
      tags:
        - Language Parsers
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParserInstallRequest'
      responses:
        '201':
          description: Parser installed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParserInfo'
                
  /parsers/{language}:
    get:
      summary: Get parser details
      operationId: getParser
      tags:
        - Language Parsers
      parameters:
        - name: language
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Parser details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParserDetails'
                
    put:
      summary: Update parser plugin
      operationId: updateParser
      tags:
        - Language Parsers
      parameters:
        - name: language
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParserUpdateRequest'
      responses:
        '200':
          description: Parser updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParserInfo'
                
  /parsers/{language}/validate:
    post:
      summary: Validate parser configuration
      operationId: validateParser
      tags:
        - Language Parsers
      parameters:
        - name: language
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParserValidationRequest'
      responses:
        '200':
          description: Validation results
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParserValidationResponse'
                
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    apiKey:
      type: apiKey
      in: header
      name: X-API-Key
      
  parameters:
    limit:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
    offset:
      name: offset
      in: query
      schema:
        type: integer
        minimum: 0
        default: 0
```

### Request/Response Schemas

```yaml
schemas:
  # Analysis Schemas
  AnalysisRequest:
    type: object
    required:
      - repository_url
    properties:
      repository_url:
        type: string
        format: uri
        example: https://github.com/facebook/react
      branch:
        type: string
        default: main
      commit:
        type: string
        pattern: '^[a-f0-9]{40}$'
      languages:
        type: array
        items:
          type: string
      options:
        $ref: '#/components/schemas/AnalysisOptions'
      webhook_url:
        type: string
        format: uri
        
  AnalysisOptions:
    type: object
    properties:
      incremental:
        type: boolean
        default: false
      deep_pattern_analysis:
        type: boolean
        default: true
      generate_embeddings:
        type: boolean
        default: true
      include_paths:
        type: array
        items:
          type: string
      exclude_paths:
        type: array
        items:
          type: string
      pattern_confidence_threshold:
        type: number
        minimum: 0
        maximum: 1
        default: 0.8
        
  AnalysisResponse:
    type: object
    properties:
      analysis_id:
        type: string
        format: uuid
      status:
        type: string
        enum: [pending, running, completed, failed]
      created_at:
        type: string
        format: date-time
      estimated_completion:
        type: string
        format: date-time
      repository:
        $ref: '#/components/schemas/RepositoryInfo'
      webhook_url:
        type: string
        
  AnalysisDetails:
    allOf:
      - $ref: '#/components/schemas/AnalysisResponse'
      - type: object
        properties:
          started_at:
            type: string
            format: date-time
          completed_at:
            type: string
            format: date-time
          duration_seconds:
            type: integer
          statistics:
            $ref: '#/components/schemas/AnalysisStatistics'
          results:
            $ref: '#/components/schemas/AnalysisResults'
          error:
            $ref: '#/components/schemas/Error'
            
  # Pattern Schemas
  Pattern:
    type: object
    properties:
      id:
        type: string
      repository_id:
        type: string
      type:
        type: string
        enum: [architectural, design, idiom, anti-pattern]
      name:
        type: string
      description:
        type: string
      confidence:
        type: number
      occurrences:
        type: integer
      first_seen:
        type: string
        format: date-time
      last_seen:
        type: string
        format: date-time
      locations:
        type: array
        items:
          $ref: '#/components/schemas/PatternLocation'
      template:
        $ref: '#/components/schemas/PatternTemplate'
      quality_score:
        type: number
      tags:
        type: array
        items:
          type: string
          
  PatternLocation:
    type: object
    properties:
      file:
        type: string
      lines:
        type: array
        items:
          type: integer
        minItems: 2
        maxItems: 2
      snippet:
        type: string
        
  PatternTemplate:
    type: object
    properties:
      code:
        type: string
      variables:
        type: array
        items:
          type: string
      language:
        type: string
        
  # Query Schemas
  QueryRequest:
    type: object
    required:
      - query
    properties:
      query:
        type: string
        minLength: 1
        maxLength: 1000
      repository_id:
        type: string
      conversation_id:
        type: string
      context:
        $ref: '#/components/schemas/QueryContext'
      stream:
        type: boolean
        default: false
      model:
        type: string
        enum: [fast, advanced]
        default: advanced
        
  QueryContext:
    type: object
    properties:
      focus_area:
        type: string
      include_code:
        type: boolean
        default: true
      max_examples:
        type: integer
        minimum: 0
        maximum: 10
        default: 3
        user_role:
          type: string
          enum: [junior_developer, senior_developer, architect, team_lead]
        focus_areas:
          type: array
          items:
            type: string
            enum: [security, performance, maintainability, scalability]
        
  QueryResponse:
    type: object
    properties:
      answer:
        type: string
      confidence:
        type: number
        minimum: 0
        maximum: 1
      conversation_id:
        type: string
      sources:
        type: array
        items:
          $ref: '#/components/schemas/QuerySource'
      code_examples:
        type: array
        items:
          $ref: '#/components/schemas/CodeExample'
      patterns_referenced:
        type: array
        items:
          $ref: '#/components/schemas/PatternReference'
      suggested_questions:
        type: array
        items:
          type: string
      metadata:
        $ref: '#/components/schemas/QueryMetadata'
        
  QuerySource:
    type: object
    properties:
      file:
        type: string
      lines:
        type: array
        items:
          type: integer
        minItems: 2
        maxItems: 2
      relevance:
        type: number
        minimum: 0
        maximum: 1
      snippet:
        type: string
        
  CodeExample:
    type: object
    properties:
      title:
        type: string
      file:
        type: string
      code:
        type: string
      language:
        type: string
      line_numbers:
        type: array
        items:
          type: integer
          
  PatternReference:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      relevance:
        type: number
        minimum: 0
        maximum: 1
        
  QueryMetadata:
    type: object
    properties:
      processing_time_ms:
        type: integer
      tokens_used:
        type: integer
      cache_hit:
        type: boolean
      model_version:
        type: string
  
  # Repository Schemas
  RepositoryCreate:
    type: object
    required:
      - url
    properties:
      url:
        type: string
        format: uri
      name:
        type: string
      branch:
        type: string
        default: main
      provider:
        type: string
        enum: [github, gitlab, bitbucket]
      authentication:
        type: object
        properties:
          type:
            type: string
            enum: [token, ssh_key]
          token:
            type: string
          ssh_key:
            type: string
      auto_analyze:
        type: boolean
        default: true
      analysis_schedule:
        type: string
        
  Repository:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      url:
        type: string
        format: uri
      provider:
        type: string
      branch:
        type: string
      created_at:
        type: string
        format: date-time
      last_analysis:
        $ref: '#/components/schemas/AnalysisDetails'
      statistics:
        $ref: '#/components/schemas/RepositoryStatistics'
      settings:
        $ref: '#/components/schemas/RepositorySettings'
        
  RepositoryStatistics:
    type: object
    properties:
      total_files:
        type: integer
      total_lines:
        type: integer
      primary_language:
        type: string
      languages:
        type: object
        additionalProperties:
          type: number
      contributors:
        type: integer
      age_days:
        type: integer
        
  RepositorySettings:
    type: object
    properties:
      auto_analyze:
        type: boolean
      analysis_schedule:
        type: string
      webhook_url:
        type: string
        format: uri
      exclude_paths:
        type: array
        items:
          type: string
          
  # Marketplace Schemas
  MarketplacePattern:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      description:
        type: string
      author:
        $ref: '#/components/schemas/Author'
      price:
        type: number
        minimum: 0
      currency:
        type: string
        enum: [USD, EUR, GBP]
      rating:
        type: number
        minimum: 1
        maximum: 5
      reviews:
        type: integer
      downloads:
        type: integer
      updated_at:
        type: string
        format: date-time
      preview:
        $ref: '#/components/schemas/PatternPreview'
      tags:
        type: array
        items:
          type: string
          
  Author:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      verified:
        type: boolean
      avatar_url:
        type: string
        format: uri
        
  PatternPreview:
    type: object
    properties:
      template_snippet:
        type: string
      examples:
        type: integer
      languages:
        type: array
        items:
          type: string
          
  PurchaseRequest:
    type: object
    required:
      - pattern_id
      - payment_method_id
    properties:
      pattern_id:
        type: string
      payment_method_id:
        type: string
      license_type:
        type: string
        enum: [individual, team, enterprise]
        
  PurchaseResponse:
    type: object
    properties:
      purchase_id:
        type: string
      pattern_id:
        type: string
      status:
        type: string
        enum: [completed, failed, pending]
      amount:
        type: number
      currency:
        type: string
      license:
        $ref: '#/components/schemas/License'
      download_url:
        type: string
        format: uri
      receipt_url:
        type: string
        format: uri
        
  License:
    type: object
    properties:
      id:
        type: string
      type:
        type: string
        enum: [individual, team, enterprise]
      pattern_id:
        type: string
      seats:
        type: integer
      expires_at:
        type: string
        format: date-time
      key:
        type: string
      status:
        type: string
        enum: [active, expired, suspended]
      purchased_at:
        type: string
        format: date-time
        
  LicenseList:
    type: object
    properties:
      licenses:
        type: array
        items:
          $ref: '#/components/schemas/License'
      pagination:
        $ref: '#/components/schemas/Pagination'
        
  Review:
    type: object
    properties:
      id:
        type: string
      pattern_id:
        type: string
      user:
        $ref: '#/components/schemas/User'
      rating:
        type: integer
        minimum: 1
        maximum: 5
      title:
        type: string
      content:
        type: string
      helpful_votes:
        type: integer
      created_at:
        type: string
        format: date-time
      updated_at:
        type: string
        format: date-time
      verified_purchase:
        type: boolean
        
  ReviewList:
    type: object
    properties:
      reviews:
        type: array
        items:
          $ref: '#/components/schemas/Review'
      pagination:
        $ref: '#/components/schemas/Pagination'
      rating_summary:
        type: object
        properties:
          average_rating:
            type: number
          total_reviews:
            type: integer
          rating_distribution:
            type: object
            properties:
              "5":
                type: integer
              "4":
                type: integer
              "3":
                type: integer
              "2":
                type: integer
              "1":
                type: integer
                
  ReviewCreateRequest:
    type: object
    required:
      - rating
      - content
    properties:
      rating:
        type: integer
        minimum: 1
        maximum: 5
      title:
        type: string
        maxLength: 100
      content:
        type: string
        maxLength: 2000
        
  RevenueAnalytics:
    type: object
    properties:
      total_revenue:
        type: number
      total_sales:
        type: integer
      period_revenue:
        type: number
      period_sales:
        type: integer
      revenue_by_pattern:
        type: array
        items:
          type: object
          properties:
            pattern_id:
              type: string
            pattern_name:
              type: string
            revenue:
              type: number
            sales:
              type: integer
      revenue_by_period:
        type: array
        items:
          type: object
          properties:
            period:
              type: string
              format: date
            revenue:
              type: number
            sales:
              type: integer
      commission_rate:
        type: number
        description: Platform commission rate (0-1)
      net_revenue:
        type: number
        description: Revenue after commission
        
  PayoutList:
    type: object
    properties:
      payouts:
        type: array
        items:
          $ref: '#/components/schemas/Payout'
      pagination:
        $ref: '#/components/schemas/Pagination'
        
  Payout:
    type: object
    properties:
      id:
        type: string
      amount:
        type: number
      currency:
        type: string
        enum: [USD, EUR, GBP]
      status:
        type: string
        enum: [pending, processing, completed, failed]
      requested_at:
        type: string
        format: date-time
      processed_at:
        type: string
        format: date-time
      method:
        type: string
        enum: [stripe, paypal, bank_transfer]
      period_start:
        type: string
        format: date
      period_end:
        type: string
        format: date
      transaction_fee:
        type: number
      net_amount:
        type: number
        
  PayoutRequest:
    type: object
    required:
      - amount
      - method
    properties:
      amount:
        type: number
        minimum: 10
      method:
        type: string
        enum: [stripe, paypal, bank_transfer]
      currency:
        type: string
        enum: [USD, EUR, GBP]
        default: USD
        
  PayoutResponse:
    type: object
    properties:
      payout:
        $ref: '#/components/schemas/Payout'
      estimated_processing_time:
        type: string
        description: Human-readable processing time estimate
        
  # Code Generation Schemas
  GenerateRequest:
    type: object
    required:
      - pattern_id
      - variables
    properties:
      pattern_id:
        type: string
      variables:
        type: object
        additionalProperties: true
      options:
        type: object
        properties:
          include_tests:
            type: boolean
            default: true
          include_documentation:
            type: boolean
            default: true
          style_guide:
            type: string
            
  GenerateResponse:
    type: object
    properties:
      generated_code:
        type: object
        properties:
          main:
            $ref: '#/components/schemas/GeneratedFile'
          tests:
            $ref: '#/components/schemas/GeneratedFile'
          documentation:
            $ref: '#/components/schemas/GeneratedFile'
      integration_instructions:
        type: array
        items:
          type: string
          
  GeneratedFile:
    type: object
    properties:
      filename:
        type: string
      code:
        type: string
      language:
        type: string
        
  # Simulation Schemas
  SimulationRequest:
    type: object
    required:
      - repository_id
      - simulation_type
      - changes
    properties:
      repository_id:
        type: string
      simulation_type:
        type: string
        enum: [refactoring, migration, scaling, modernization]
      changes:
        type: object
        properties:
          description:
            type: string
          target_services:
            type: array
            items:
              $ref: '#/components/schemas/TargetService'
          constraints:
            $ref: '#/components/schemas/SimulationConstraints'
            
  TargetService:
    type: object
    properties:
      name:
        type: string
      components:
        type: array
        items:
          type: string
          
  SimulationConstraints:
    type: object
    properties:
      preserve_api:
        type: boolean
      max_downtime_hours:
        type: number
      gradual_migration:
        type: boolean
        
  SimulationResponse:
    type: object
    properties:
      simulation_id:
        type: string
      feasibility:
        type: number
        minimum: 0
        maximum: 1
      estimated_effort:
        $ref: '#/components/schemas/EffortEstimate'
      risk_assessment:
        $ref: '#/components/schemas/RiskAssessment'
      migration_plan:
        $ref: '#/components/schemas/MigrationPlan'
      impact_analysis:
        $ref: '#/components/schemas/ImpactAnalysis'
        
  EffortEstimate:
    type: object
    properties:
      developer_days:
        type: integer
      calendar_days:
        type: integer
      team_size:
        type: integer
        
  RiskAssessment:
    type: object
    properties:
      overall_risk:
        type: string
        enum: [low, medium, high]
      factors:
        type: array
        items:
          $ref: '#/components/schemas/RiskFactor'
          
  RiskFactor:
    type: object
    properties:
      factor:
        type: string
      risk:
        type: string
        enum: [low, medium, high]
      mitigation:
        type: string
        
  MigrationPlan:
    type: object
    properties:
      phases:
        type: array
        items:
          $ref: '#/components/schemas/MigrationPhase'
          
  MigrationPhase:
    type: object
    properties:
      phase:
        type: integer
      name:
        type: string
      duration_days:
        type: integer
      tasks:
        type: array
        items:
          type: string
          
  ImpactAnalysis:
    type: object
    properties:
      affected_files:
        type: integer
      affected_tests:
        type: integer
      api_changes:
        type: integer
      breaking_changes:
        type: integer
        
  # Webhook Schemas
  WebhookPayload:
    type: object
    properties:
      id:
        type: string
      type:
        type: string
        enum: [analysis.started, analysis.completed, analysis.failed, pattern.detected, repository.updated, session.created, session.ended, model.trained]
      created:
        type: string
        format: date-time
      data:
        type: object
        additionalProperties: true
        
  # AI/ML Schemas
  EmbeddingRequest:
    type: object
    required:
      - code
    properties:
      code:
        type: string
        description: Code snippet to generate embeddings for
      language:
        type: string
        description: Programming language
      context:
        type: object
        properties:
          file_path:
            type: string
          repository_id:
            type: string
          
  EmbeddingResponse:
    type: object
    properties:
      embedding:
        type: array
        items:
          type: number
        description: High-dimensional vector representation
      model_version:
        type: string
      dimensions:
        type: integer
      processing_time_ms:
        type: integer
        
  SimilarityRequest:
    type: object
    required:
      - code
    properties:
      code:
        type: string
      language:
        type: string
      repository_id:
        type: string
      similarity_threshold:
        type: number
        minimum: 0
        maximum: 1
        default: 0.8
      max_results:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
        
  SimilarityResponse:
    type: object
    properties:
      matches:
        type: array
        items:
          $ref: '#/components/schemas/CodeMatch'
      total_matches:
        type: integer
      processing_time_ms:
        type: integer
        
  CodeMatch:
    type: object
    properties:
      code_snippet:
        type: string
      file_path:
        type: string
      line_start:
        type: integer
      line_end:
        type: integer
      similarity_score:
        type: number
        minimum: 0
        maximum: 1
      repository_id:
        type: string
      context:
        type: object
        
  AIModelList:
    type: object
    properties:
      models:
        type: array
        items:
          $ref: '#/components/schemas/AIModel'
      total:
        type: integer
        
  AIModel:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      version:
        type: string
      type:
        type: string
        enum: [embedding, similarity, pattern_detection, code_completion]
      status:
        type: string
        enum: [available, training, unavailable]
      metrics:
        type: object
        properties:
          accuracy:
            type: number
          precision:
            type: number
          recall:
            type: number
          f1_score:
            type: number
      created_at:
        type: string
        format: date-time
      updated_at:
        type: string
        format: date-time
        
  ModelTrainingRequest:
    type: object
    required:
      - name
      - type
      - training_data
    properties:
      name:
        type: string
      type:
        type: string
        enum: [embedding, similarity, pattern_detection, code_completion]
      training_data:
        type: object
        properties:
          repository_ids:
            type: array
            items:
              type: string
          patterns:
            type: array
            items:
              type: string
          hyperparameters:
            type: object
            additionalProperties: true
            
  ModelTrainingResponse:
    type: object
    properties:
      training_job_id:
        type: string
      model_id:
        type: string
      status:
        type: string
        enum: [started, running, completed, failed]
      estimated_completion:
        type: string
        format: date-time
        
  # Real-time Collaboration Schemas
  SessionCreateRequest:
    type: object
    required:
      - repository_id
      - name
    properties:
      repository_id:
        type: string
      name:
        type: string
      description:
        type: string
      max_participants:
        type: integer
        minimum: 1
        maximum: 50
        default: 10
      settings:
        type: object
        properties:
          voice_enabled:
            type: boolean
            default: false
          screen_sharing:
            type: boolean
            default: true
          code_editing:
            type: boolean
            default: true
          
  CollaborationSession:
    type: object
    properties:
      id:
        type: string
      repository_id:
        type: string
      name:
        type: string
      description:
        type: string
      owner:
        $ref: '#/components/schemas/User'
      participants:
        type: array
        items:
          $ref: '#/components/schemas/Participant'
      status:
        type: string
        enum: [active, paused, ended]
      created_at:
        type: string
        format: date-time
      updated_at:
        type: string
        format: date-time
      websocket_url:
        type: string
        format: uri
      settings:
        type: object
        
  SessionList:
    type: object
    properties:
      sessions:
        type: array
        items:
          $ref: '#/components/schemas/CollaborationSession'
      pagination:
        $ref: '#/components/schemas/Pagination'
        
  SessionDetails:
    allOf:
      - $ref: '#/components/schemas/CollaborationSession'
      - type: object
        properties:
          activity_log:
            type: array
            items:
              $ref: '#/components/schemas/ActivityEvent'
          shared_state:
            type: object
            properties:
              cursor_positions:
                type: object
                additionalProperties: true
              selections:
                type: object
                additionalProperties: true
              annotations:
                type: array
                items:
                  $ref: '#/components/schemas/Annotation'
                  
  SessionUpdateRequest:
    type: object
    properties:
      name:
        type: string
      description:
        type: string
      settings:
        type: object
        
  SessionJoinResponse:
    type: object
    properties:
      session:
        $ref: '#/components/schemas/CollaborationSession'
      participant_id:
        type: string
      websocket_url:
        type: string
        format: uri
      auth_token:
        type: string
        
  User:
    type: object
    properties:
      id:
        type: string
      username:
        type: string
      email:
        type: string
      display_name:
        type: string
      avatar_url:
        type: string
        format: uri
        
  Participant:
    type: object
    properties:
      id:
        type: string
      user:
        $ref: '#/components/schemas/User'
      role:
        type: string
        enum: [owner, collaborator, viewer]
      status:
        type: string
        enum: [active, away, offline]
      joined_at:
        type: string
        format: date-time
      last_activity:
        type: string
        format: date-time
      cursor_position:
        type: object
        properties:
          file:
            type: string
          line:
            type: integer
          column:
            type: integer
            
  ActivityEvent:
    type: object
    properties:
      id:
        type: string
      type:
        type: string
        enum: [user_joined, user_left, code_edited, file_opened, annotation_added]
      participant_id:
        type: string
      timestamp:
        type: string
        format: date-time
      data:
        type: object
        additionalProperties: true
        
  Annotation:
    type: object
    properties:
      id:
        type: string
      type:
        type: string
        enum: [comment, highlight, suggestion]
      file_path:
        type: string
      line_start:
        type: integer
      line_end:
        type: integer
      content:
        type: string
      author:
        $ref: '#/components/schemas/User'
      created_at:
        type: string
        format: date-time
      resolved:
        type: boolean
        default: false
        
  # Language Parser Schemas
  ParserList:
    type: object
    properties:
      parsers:
        type: array
        items:
          $ref: '#/components/schemas/ParserInfo'
      total:
        type: integer
        
  ParserInfo:
    type: object
    properties:
      language:
        type: string
      name:
        type: string
      version:
        type: string
      type:
        type: string
        enum: [built-in, plugin, custom]
      status:
        type: string
        enum: [active, inactive, error]
      supports:
        type: array
        items:
          type: string
          enum: [ast, syntax_highlighting, error_detection, code_completion]
      file_extensions:
        type: array
        items:
          type: string
      created_at:
        type: string
        format: date-time
      updated_at:
        type: string
        format: date-time
        
  ParserDetails:
    allOf:
      - $ref: '#/components/schemas/ParserInfo'
      - type: object
        properties:
          configuration:
            type: object
            properties:
              grammar_rules:
                type: array
                items:
                  type: string
              syntax_patterns:
                type: object
                additionalProperties: true
              error_patterns:
                type: array
                items:
                  type: string
          performance_metrics:
            type: object
            properties:
              avg_parse_time_ms:
                type: number
              success_rate:
                type: number
              error_rate:
                type: number
          usage_statistics:
            type: object
            properties:
              files_parsed:
                type: integer
              lines_parsed:
                type: integer
              last_used:
                type: string
                format: date-time
                
  ParserInstallRequest:
    type: object
    required:
      - language
      - plugin_url
    properties:
      language:
        type: string
      plugin_url:
        type: string
        format: uri
      name:
        type: string
      version:
        type: string
      configuration:
        type: object
        additionalProperties: true
        
  ParserUpdateRequest:
    type: object
    properties:
      plugin_url:
        type: string
        format: uri
      version:
        type: string
      configuration:
        type: object
        additionalProperties: true
      enabled:
        type: boolean
        
  ParserValidationRequest:
    type: object
    required:
      - code_sample
    properties:
      code_sample:
        type: string
      configuration:
        type: object
        additionalProperties: true
        
  ParserValidationResponse:
    type: object
    properties:
      valid:
        type: boolean
      ast:
        type: object
        description: Abstract Syntax Tree
        additionalProperties: true
      errors:
        type: array
        items:
          type: object
          properties:
            line:
              type: integer
            column:
              type: integer
            message:
              type: string
            severity:
              type: string
              enum: [error, warning, info]
      warnings:
        type: array
        items:
          type: object
          properties:
            line:
              type: integer
            column:
              type: integer
            message:
              type: string
      performance:
        type: object
        properties:
          parse_time_ms:
            type: number
          memory_usage_mb:
            type: number
          
  # Enhanced Simulation Schemas
  SimulationResults:
    allOf:
      - $ref: '#/components/schemas/SimulationResponse'
      - type: object
        properties:
          status:
            type: string
            enum: [running, completed, failed]
          completed_at:
            type: string
            format: date-time
          detailed_analysis:
            type: object
            properties:
              code_changes:
                type: array
                items:
                  $ref: '#/components/schemas/CodeChange'
              performance_impact:
                $ref: '#/components/schemas/PerformanceImpact'
              security_implications:
                type: array
                items:
                  $ref: '#/components/schemas/SecurityImplication'
              test_coverage_impact:
                type: object
                properties:
                  current_coverage:
                    type: number
                  projected_coverage:
                    type: number
                  affected_tests:
                    type: integer
                    
  CodeChange:
    type: object
    properties:
      file_path:
        type: string
      change_type:
        type: string
        enum: [add, modify, delete, rename]
      lines_added:
        type: integer
      lines_removed:
        type: integer
      complexity_change:
        type: number
      impact_score:
        type: number
        minimum: 0
        maximum: 1
        
  PerformanceImpact:
    type: object
    properties:
      memory_usage_change:
        type: number
        description: Percentage change in memory usage
      cpu_usage_change:
        type: number
        description: Percentage change in CPU usage
      response_time_change:
        type: number
        description: Percentage change in response time
      throughput_change:
        type: number
        description: Percentage change in throughput
      bottlenecks:
        type: array
        items:
          type: string
          
  SecurityImplication:
    type: object
    properties:
      type:
        type: string
        enum: [vulnerability, risk_reduction, compliance_impact]
      severity:
        type: string
        enum: [low, medium, high, critical]
      description:
        type: string
      affected_components:
        type: array
        items:
          type: string
      recommendation:
        type: string
        
  # Common Schemas
  Pagination:
    type: object
    properties:
      total:
        type: integer
      limit:
        type: integer
      offset:
        type: integer
      has_more:
        type: boolean
      next_cursor:
        type: string
      prev_cursor:
        type: string
        
  # Error Schemas
  Error:
    type: object
    properties:
      code:
        type: string
      message:
        type: string
      details:
        type: object
      request_id:
        type: string
      timestamp:
        type: string
        format: date-time
        
  ValidationError:
    allOf:
      - $ref: '#/components/schemas/Error'
      - type: object
        properties:
          validation_errors:
            type: array
            items:
              type: object
              properties:
                field:
                  type: string
                message:
                  type: string
                code:
                  type: string
```

### Error Handling

```typescript
// Common error responses
enum ErrorCode {
  // Client errors (4xx)
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMITED = 'RATE_LIMITED',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  
  // Server errors (5xx)
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  TIMEOUT = 'TIMEOUT'
}

interface ErrorResponse {
  error: {
    code: ErrorCode;
    message: string;
    details?: any;
    request_id: string;
    timestamp: string;
  };
}

// Error response examples
const errorResponses = {
  400: {
    error: {
      code: 'VALIDATION_ERROR',
      message: 'Invalid request parameters',
      details: {
        errors: [
          {
            field: 'repository_url',
            message: 'Invalid URL format',
            code: 'INVALID_FORMAT'
          }
        ]
      },
      request_id: 'req_abc123',
      timestamp: '2025-01-15T10:00:00Z'
    }
  },
  
  401: {
    error: {
      code: 'UNAUTHORIZED',
      message: 'Invalid or missing authentication token',
      request_id: 'req_abc123',
      timestamp: '2025-01-15T10:00:00Z'
    }
  },
  
  429: {
    error: {
      code: 'RATE_LIMITED',
      message: 'Rate limit exceeded',
      details: {
        limit: 1000,
        remaining: 0,
        reset_at: '2025-01-15T11:00:00Z',
        retry_after: 3600
      },
      request_id: 'req_abc123',
      timestamp: '2025-01-15T10:00:00Z'
    }
  }
};
```

### Pagination Implementation

```typescript
interface PaginationParams {
  limit: number;  // 1-100, default 20
  offset: number; // >= 0, default 0
  cursor?: string; // For cursor-based pagination
}

interface PaginationResponse<T> {
  data: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    has_more: boolean;
    next_cursor?: string;
    prev_cursor?: string;
  };
}

// Cursor-based pagination for real-time data
class CursorPagination {
  static encode(lastId: string, timestamp: Date): string {
    const data = { id: lastId, ts: timestamp.toISOString() };
    return Buffer.from(JSON.stringify(data)).toString('base64');
  }
  
  static decode(cursor: string): { id: string; ts: string } {
    const json = Buffer.from(cursor, 'base64').toString();
    return JSON.parse(json);
  }
}
```

### Rate Limiting Implementation

```typescript
interface RateLimitConfig {
  tier: 'free' | 'pro' | 'team' | 'enterprise';
  limits: {
    requests_per_hour: number;
    burst_size: number;
    concurrent_requests: number;
  };
}

const rateLimits: Record<string, RateLimitConfig> = {
  free: {
    tier: 'free',
    limits: {
      requests_per_hour: 1000,
      burst_size: 50,
      concurrent_requests: 10
    }
  },
  pro: {
    tier: 'pro',
    limits: {
      requests_per_hour: 10000,
      burst_size: 500,
      concurrent_requests: 50
    }
  },
  team: {
    tier: 'team',
    limits: {
      requests_per_hour: 100000,
      burst_size: 5000,
      concurrent_requests: 200
    }
  },
  enterprise: {
    tier: 'enterprise',
    limits: {
      requests_per_hour: -1, // Unlimited
      burst_size: -1,
      concurrent_requests: -1
    }
  }
};

// Rate limit headers
interface RateLimitHeaders {
  'X-RateLimit-Limit': string;
  'X-RateLimit-Remaining': string;
  'X-RateLimit-Reset': string;
  'X-RateLimit-Reset-After': string;
  'Retry-After'?: string;
}
```

### API Versioning Strategy

```typescript
// Version negotiation
enum ApiVersion {
  V1 = 'v1',
  V2 = 'v2'
}

interface VersionConfig {
  version: ApiVersion;
  deprecated: boolean;
  sunset_date?: string;
  changes: string[];
}

const versions: Record<ApiVersion, VersionConfig> = {
  [ApiVersion.V1]: {
    version: ApiVersion.V1,
    deprecated: false,
    changes: ['Initial release']
  },
  [ApiVersion.V2]: {
    version: ApiVersion.V2,
    deprecated: false,
    changes: [
      'Added pattern marketplace endpoints',
      'Enhanced query response format',
      'Improved error messages'
    ]
  }
};

// Version selection middleware
function selectVersion(req: Request): ApiVersion {
  // 1. Check URL path
  const pathMatch = req.path.match(/^\/v(\d+)\//);
  if (pathMatch) {
    return `v${pathMatch[1]}` as ApiVersion;
  }
  
  // 2. Check Accept header
  const acceptHeader = req.headers['accept'];
  if (acceptHeader?.includes('version=')) {
    const version = acceptHeader.match(/version=v(\d+)/)?.[1];
    if (version) {
      return `v${version}` as ApiVersion;
    }
  }
  
  // 3. Default to latest stable
  return ApiVersion.V1;
}
```

## Validation Loop

### Level 1: Request Validation
```typescript
// Validate all incoming requests
import Joi from 'joi';

const analysisRequestSchema = Joi.object({
  repository_url: Joi.string().uri().required(),
  branch: Joi.string().default('main'),
  commit: Joi.string().pattern(/^[a-f0-9]{40}$/),
  languages: Joi.array().items(Joi.string()),
  options: Joi.object({
    incremental: Joi.boolean(),
    deep_pattern_analysis: Joi.boolean(),
    generate_embeddings: Joi.boolean(),
    include_paths: Joi.array().items(Joi.string()),
    exclude_paths: Joi.array().items(Joi.string()),
    pattern_confidence_threshold: Joi.number().min(0).max(1)
  }),
  webhook_url: Joi.string().uri()
});

async function validateRequest(schema: Joi.Schema, data: any) {
  try {
    return await schema.validateAsync(data, { abortEarly: false });
  } catch (error) {
    throw new ValidationError(error.details);
  }
}
```

### Level 2: Integration Testing
```typescript
// Test all API endpoints
describe('API Endpoints', () => {
  test('POST /analyze - valid request', async () => {
    const response = await api.post('/analyze', {
      repository_url: 'https://github.com/test/repo'
    });
    
    expect(response.status).toBe(202);
    expect(response.data).toHaveProperty('analysis_id');
    expect(response.data.status).toBe('pending');
  });
  
  test('GET /patterns - pagination', async () => {
    const response = await api.get('/patterns?limit=10&offset=20');
    
    expect(response.status).toBe(200);
    expect(response.data.patterns).toHaveLength(10);
    expect(response.data.pagination.offset).toBe(20);
  });
  
  test('Rate limiting enforcement', async () => {
    // Exhaust rate limit
    for (let i = 0; i < 1001; i++) {
      await api.get('/patterns');
    }
    
    const response = await api.get('/patterns');
    expect(response.status).toBe(429);
    expect(response.headers['retry-after']).toBeDefined();
  });
});
```

### Level 3: Load Testing
```yaml
# k6 load test script
import http from 'k6/http';
import { check } from 'k6';

export const options = {
  stages: [
    { duration: '2m', target: 100 },
    { duration: '5m', target: 100 },
    { duration: '2m', target: 0 },
  ],
  thresholds: {
    http_req_duration: ['p(95)<100'], // 95% of requests under 100ms
    http_req_failed: ['rate<0.1'], // Error rate under 10%
  },
};

export default function() {
  const response = http.get('https://api.ccl.dev/v1/patterns');
  
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 100ms': (r) => r.timings.duration < 100,
  });
}
```

## Final Validation Checklist

- [ ] All endpoints documented in OpenAPI spec
- [ ] Request validation on all inputs
- [ ] Consistent error response format
- [ ] Rate limiting properly configured
- [ ] Pagination works correctly
- [ ] API versioning implemented
- [ ] Authentication/authorization working
- [ ] CORS headers configured
- [ ] Response times meet SLA
- [ ] Load testing passed

## Anti-Patterns to Avoid

1. **DON'T expose internal IDs** - Use UUIDs or hashed IDs
2. **DON'T return sensitive data** - Filter responses
3. **DON'T allow unlimited results** - Always paginate
4. **DON'T skip input validation** - Validate everything
5. **DON'T use GET for mutations** - Follow REST principles
6. **DON'T ignore API versioning** - Plan for changes
7. **DON'T return stack traces** - Use proper error messages
8. **DON'T allow SQL injection** - Parameterize queries
9. **DON'T forget rate limiting** - Protect your API
10. **DON'T skip monitoring** - Track all requests
