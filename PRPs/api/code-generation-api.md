# PRP: Code Generation API

## 1. Overview

This document outlines the specifications for the Code Generation API endpoint. This API allows developers to generate code snippets, files, and documentation from predefined or custom patterns.

## 2. Endpoint Specification

### `POST /generate`

This endpoint generates code based on a specified pattern and a set of variables.

#### Request Body

```json
{
  "pattern_id": "pat_abc123",
  "variables": {
    "EntityName": "Order",
    "fields": ["id", "customer_id", "total", "status"]
  },
  "options": {
    "include_tests": true,
    "include_documentation": true,
    "style_guide": "repository_style_guide_v2"
  }
}
```

-   **`pattern_id`** (string, required): The ID of the pattern to use for code generation.
-   **`variables`** (object, required): A key-value map of variables to be injected into the pattern template.
-   **`options`** (object, optional): A set of options to customize the code generation process.
    -   **`include_tests`** (boolean, optional): If true, generates test files alongside the main code.
    -   **`include_documentation`** (boolean, optional): If true, generates a markdown documentation file.
    -   **`style_guide`** (string, optional): The name of a style guide to apply to the generated code.

#### Response Body

```json
{
  "generated_code": {
    "main": {
      "filename": "OrderRepository.js",
      "code": "// Generated implementation",
      "language": "javascript"
    },
    "tests": {
      "filename": "OrderRepository.test.js",
      "code": "// Generated tests",
      "language": "javascript"
    },
    "documentation": {
      "filename": "OrderRepository.md",
      "content": "# OrderRepository Documentation"
    }
  },
  "integration_instructions": [
    "1. Create file at src/repositories/OrderRepository.js",
    "2. Update dependency injection in src/config/dependencies.js",
    "3. Run tests with npm test"
  ]
}
```

-   **`generated_code`** (object): An object containing the generated code artifacts.
    -   **`main`** (object): The primary generated code file.
    -   **`tests`** (object, optional): The generated test file.
    -   **`documentation`** (object, optional): The generated documentation file.
-   **`integration_instructions`** (array of strings): A list of steps required to integrate the generated code into the project.

## 3. Success Criteria

-   The endpoint must be able to generate code from any valid pattern.
-   The generated code must be syntactically correct and adhere to the specified style guide.
-   The API must return a `200 OK` status on success and a `400 Bad Request` on invalid input.
-   The response must include clear integration instructions.
