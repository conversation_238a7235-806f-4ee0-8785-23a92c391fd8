name: "REST API Implementation"
description: |
  Implementation of the comprehensive REST API for the CCL platform including all endpoints,
  authentication, validation, error handling, and API documentation with OpenAPI specification.

---

## Goal
Implement a complete, production-ready REST API for the CCL platform that provides all necessary endpoints for code analysis, pattern management, user operations, and marketplace functionality with enterprise-grade security and performance.

## Why
- **Primary Interface**: Main way external systems interact with CCL
- **Developer Experience**: Clean, well-documented API for easy integration
- **Business Critical**: API drives all user interactions and integrations
- **Scalability**: Must handle high-volume requests efficiently

## What
A comprehensive REST API that:
- Provides all core CCL functionality via HTTP endpoints
- Implements proper authentication and authorization
- Includes comprehensive input validation and error handling
- Supports API versioning and backward compatibility
- Provides detailed OpenAPI documentation
- Implements rate limiting and security measures

### Success Criteria
- [ ] All core API endpoints implemented and functional
  - [ ] Analysis endpoints (POST /analyze, GET /analysis/{id}, GET /analysis)
  - [ ] Pattern endpoints (GET /patterns, POST /patterns, POST /patterns/validate)
  - [ ] Query endpoints (POST /query, conversation management)
  - [ ] Repository management (CRUD operations)
  - [ ] Simulation endpoint (POST /simulate)
  - [ ] Code generation (POST /generate)
  - [ ] Marketplace endpoints (search, purchase, publish)
- [ ] Authentication and authorization working
  - [ ] API key authentication
  - [ ] OAuth 2.0 flow implementation
  - [ ] Scope-based authorization
- [ ] Input validation comprehensive
  - [ ] Request schema validation
  - [ ] Field-level validation with detailed error messages
- [ ] Error handling consistent across all endpoints
  - [ ] Standard error response format
  - [ ] Comprehensive error codes
  - [ ] Rate limiting error responses
- [ ] API documentation complete with OpenAPI spec
  - [ ] All endpoints documented
  - [ ] Request/response schemas defined
  - [ ] Authentication flows documented
- [ ] Rate limiting and security measures active
  - [ ] Tier-based rate limiting
  - [ ] Rate limit headers
  - [ ] Security headers and CORS
- [ ] Performance meets SLO (<100ms p95)
- [ ] API versioning strategy implemented
- [ ] Webhook system functional
  - [ ] Event delivery
  - [ ] Signature verification
  - [ ] Retry logic

## All Needed Context

### API Specifications
```yaml
API Details:
  base_url: https://api.ccl.dev/v1
  protocol: HTTPS only
  format: JSON (application/json)
  authentication: Bearer token (JWT)
  versioning: URL path versioning (/v1, /v2)
  
Performance Requirements:
  response_time: <100ms (p95)
  availability: 99.9%
  rate_limit: 1000 requests/minute per API key
  
Security:
  authentication: JWT tokens
  authorization: Role-based access control
  encryption: TLS 1.3 minimum
  input_validation: Comprehensive validation
```

### Current Codebase Context
```bash
# Reference API patterns from:
docs/api/README.md           # Complete API specification
examples/api/
├── authentication.js        # Auth implementation patterns
├── validation.js           # Input validation patterns
├── error_handling.js       # Error handling patterns
└── rate_limiting.js        # Rate limiting implementation
```

### API Endpoint Categories
```yaml
Authentication Endpoints:
  - POST /auth/login
  - POST /auth/logout
  - POST /auth/refresh
  - GET /auth/me

Analysis Endpoints:
  - POST /analyze
  - GET /analysis/{id}
  - GET /analysis/{id}/status
  - GET /analysis (list analyses)
  - DELETE /analysis/{id}

Pattern Endpoints:
  - GET /patterns
  - GET /patterns/{id}
  - POST /patterns
  - PUT /patterns/{id}
  - DELETE /patterns/{id}
  - POST /patterns/validate (validate code against patterns)

Query Endpoints:
  - POST /query
  - GET /conversations
  - GET /conversations/{id}
  - POST /conversations/{id}/messages
  - GET /conversations/{id}/messages
  - POST /conversations (create conversation)

Repository Management:
  - POST /repositories
  - GET /repositories/{id}
  - PATCH /repositories/{id}
  - DELETE /repositories/{id}
  - GET /repositories (list repositories)

Marketplace Endpoints:
  - GET /marketplace/patterns
  - GET /marketplace/patterns/{id}
  - POST /marketplace/purchase
  - POST /marketplace/patterns (publish pattern)

Code Generation:
  - POST /generate (generate code from patterns)

Simulation Endpoints:
  - POST /simulate (simulate architecture changes)

Webhook Management:
  - Core webhook events: analysis.started, analysis.completed, analysis.failed, pattern.detected, repository.updated
  - Extended webhook events: pattern.validated, pattern.published, repository.added, repository.removed, pattern.purchased, pattern.downloaded, query.completed, conversation.started
  - Webhook security with HMAC signatures
  - Event filtering and subscription management
  - Retry logic with exponential backoff
  - Dead letter queue handling
  - Note: See [Webhooks PRP](./webhooks.md) for complete webhook system implementation details

User Management:
  - GET /users/profile
  - PUT /users/profile
  - GET /users/usage
  - GET /users/api-keys
  - POST /users/api-keys
```

### Authentication & Authorization
```yaml
Authentication Methods:
  api_key:
    type: API Key
    location: Authorization header
    format: "Bearer {api_key}"

  oauth2:
    type: OAuth 2.0
    flows:
      authorization_code:
        authorization_url: https://auth.ccl.dev/oauth/authorize
        token_url: https://auth.ccl.dev/oauth/token

OAuth 2.0 Flow:
  step_1: |
    Redirect to: https://auth.ccl.dev/oauth/authorize
    ?client_id=YOUR_CLIENT_ID
    &redirect_uri=YOUR_REDIRECT_URI
    &response_type=code
    &scope=read:analysis write:analysis
    &state=RANDOM_STATE

  step_2: |
    User authorizes, redirected to:
    YOUR_REDIRECT_URI?code=AUTH_CODE&state=RANDOM_STATE

  step_3: |
    Exchange code for token:
    POST https://auth.ccl.dev/oauth/token
    {
      "grant_type": "authorization_code",
      "code": "AUTH_CODE",
      "client_id": "YOUR_CLIENT_ID",
      "client_secret": "YOUR_CLIENT_SECRET"
    }

  step_4: |
    Receive access token:
    {
      "access_token": "ACCESS_TOKEN",
      "token_type": "Bearer",
      "expires_in": 3600,
      "refresh_token": "REFRESH_TOKEN"
    }

API Key Scopes:
  read:analysis: Read analysis results (GET /analysis/*, GET /patterns/*)
  write:analysis: Create analyses (POST /analyze)
  read:patterns: Read patterns (GET /patterns/*, GET /marketplace/*)
  write:patterns: Create/modify patterns (POST /patterns, PUT /patterns/*)
  read:queries: Read conversations (GET /conversations/*)
  write:queries: Create queries (POST /query, POST /conversations/*)
  admin: Full access (All endpoints)

Rate Limits by Tier:
  Free:
    requests_per_hour: 1000
    burst: 50
    concurrent: 10
  Pro:
    requests_per_hour: 10000
    burst: 500
    concurrent: 50
  Team:
    requests_per_hour: 100000
    burst: 5000
    concurrent: 200
  Enterprise:
    requests_per_hour: unlimited
    burst: unlimited
    concurrent: unlimited

Rate Limit Headers:
  X-RateLimit-Limit: Current limit
  X-RateLimit-Remaining: Remaining requests
  X-RateLimit-Reset: Reset timestamp
  X-RateLimit-Reset-After: Seconds until reset
```

### Error Handling Standards
```yaml
Error Response Format:
  error:
    code: string          # Machine-readable error code
    message: string       # Human-readable error message
    field: string         # Field-specific error (optional)
    details: object       # Additional error details
  request_id: string      # Request ID for support

HTTP Status Codes:
  200: Success
  201: Created
  400: Bad Request (validation errors)
  401: Unauthorized (authentication required)
  403: Forbidden (insufficient permissions)
  404: Not Found
  409: Conflict (resource already exists)
  422: Unprocessable Entity (business logic error)
  429: Too Many Requests (rate limit exceeded)
  500: Internal Server Error
  503: Service Unavailable

Standard Error Codes:
  INVALID_REQUEST: Malformed request
  VALIDATION_ERROR: Invalid field values
  AUTHENTICATION_REQUIRED: Missing authentication
  INVALID_API_KEY: Invalid or expired API key
  INSUFFICIENT_PERMISSIONS: Lacks required scope
  NOT_FOUND: Resource not found
  CONFLICT: Resource conflict
  RATE_LIMITED: Too many requests
  INTERNAL_ERROR: Server error
  SERVICE_UNAVAILABLE: Temporary outage

Field Validation Error Format:
  error:
    code: VALIDATION_ERROR
    message: Invalid request parameters
    validation_errors:
      - field: repository_url
        code: invalid_format
        message: Must be a valid Git URL
      - field: languages[2]
        code: unsupported
        message: Language 'cobol' is not supported

Rate Limit Error Format:
  error:
    code: RATE_LIMITED
    message: Rate limit exceeded
    retry_after: 58
    limit: 1000
    reset_at: "2025-01-15T12:00:00Z"
```

## Detailed API Specifications

### Analysis Endpoints

#### POST /analyze
**Request:**
```json
{
  "repository_url": "https://github.com/facebook/react",
  "branch": "main",
  "commit": "abc123",
  "languages": ["javascript", "typescript"],
  "options": {
    "incremental": false,
    "deep_pattern_analysis": true,
    "generate_embeddings": true,
    "include_paths": ["src", "lib"],
    "exclude_paths": ["node_modules", "test", "dist"],
    "pattern_confidence_threshold": 0.8
  },
  "webhook_url": "https://your-app.com/webhooks/ccl"
}
```

**Response (201):**
```json
{
  "analysis_id": "ana_1234567890abcdef",
  "status": "pending",
  "created_at": "2025-01-15T10:00:00Z",
  "estimated_completion": "2025-01-15T10:05:00Z",
  "repository": {
    "id": "repo_0987654321fedcba",
    "name": "facebook/react",
    "url": "https://github.com/facebook/react"
  },
  "webhook_url": "https://your-app.com/webhooks/ccl"
}
```

#### GET /analysis/{analysis_id}
**Response (200):**
```json
{
  "analysis_id": "ana_1234567890abcdef",
  "status": "completed",
  "started_at": "2025-01-15T10:00:00Z",
  "completed_at": "2025-01-15T10:04:32Z",
  "duration_seconds": 272,
  "repository": {
    "id": "repo_0987654321fedcba",
    "name": "facebook/react",
    "url": "https://github.com/facebook/react",
    "branch": "main",
    "commit": "abc123def456"
  },
  "statistics": {
    "files_analyzed": 2847,
    "total_lines": 385293,
    "patterns_detected": 156,
    "languages": {
      "javascript": 65.3,
      "typescript": 28.4,
      "css": 4.2,
      "other": 2.1
    },
    "components_identified": 234,
    "complexity_score": 7.2
  },
  "results": {
    "architecture": {
      "type": "component-based",
      "framework": "react",
      "patterns": ["flux", "hooks", "context"],
      "layers": ["components", "hooks", "utils", "core"]
    },
    "quality_metrics": {
      "maintainability_index": 78.5,
      "technical_debt_ratio": 0.12,
      "code_duplication": 2.3
    }
  }
}
```

### Simulation Endpoints

#### POST /simulate
**Request:**
```json
{
  "repository_id": "repo_0987654321fedcba",
  "simulation_type": "refactoring",
  "changes": {
    "description": "Convert monolith to microservices",
    "target_services": [
      {
        "name": "AuthService",
        "components": ["auth", "users", "permissions"]
      },
      {
        "name": "PaymentService",
        "components": ["payments", "billing", "invoices"]
      }
    ],
    "constraints": {
      "preserve_api": true,
      "max_downtime_hours": 2,
      "gradual_migration": true
    }
  }
}
```

**Response (200):**
```json
{
  "simulation_id": "sim_xyz123",
  "feasibility": 0.85,
  "estimated_effort": {
    "developer_days": 45,
    "calendar_days": 21,
    "team_size": 3
  },
  "risk_assessment": {
    "overall_risk": "medium",
    "factors": [
      {
        "factor": "Data consistency",
        "risk": "high",
        "mitigation": "Implement distributed transactions"
      }
    ]
  },
  "migration_plan": {
    "phases": [
      {
        "phase": 1,
        "name": "Extract Authentication",
        "duration_days": 7,
        "tasks": [
          "Create AuthService repository",
          "Move auth components",
          "Update API gateway"
        ]
      }
    ]
  },
  "impact_analysis": {
    "affected_files": 234,
    "affected_tests": 567,
    "api_changes": 12,
    "breaking_changes": 0
  }
}
```

### Code Generation Endpoints

#### POST /generate
**Request:**
```json
{
  "pattern_id": "pat_abc123",
  "variables": {
    "EntityName": "Order",
    "fields": ["id", "customer_id", "total", "status"]
  },
  "options": {
    "include_tests": true,
    "include_documentation": true,
    "style_guide": "repository_style_guide_v2"
  }
}
```

**Response (200):**
```json
{
  "generated_code": {
    "main": {
      "filename": "OrderRepository.js",
      "code": "// Generated implementation",
      "language": "javascript"
    },
    "tests": {
      "filename": "OrderRepository.test.js",
      "code": "// Generated tests",
      "language": "javascript"
    },
    "documentation": {
      "filename": "OrderRepository.md",
      "content": "# OrderRepository Documentation"
    }
  },
  "integration_instructions": [
    "1. Create file at src/repositories/OrderRepository.js",
    "2. Update dependency injection in src/config/dependencies.js",
    "3. Run tests with npm test"
  ]
}
```

### Marketplace Endpoints

#### GET /marketplace/patterns
**Query Parameters:**
| Parameter | Type | Description |
|-----------|------|-------------|
| `q` | string | Search query |
| `category` | string | Category filter |
| `language` | string | Language filter |
| `author` | string | Author filter |
| `min_rating` | float | Minimum rating (1-5) |
| `price_range` | string | free, paid, 0-10, 10-50, 50+ |
| `sort` | string | popular, recent, rating, price_asc, price_desc |

**Response (200):**
```json
{
  "patterns": [
    {
      "id": "mkt_pat_123",
      "name": "Advanced JWT Authentication",
      "description": "Production-ready JWT implementation with refresh tokens",
      "author": {
        "id": "usr_456",
        "name": "Jane Developer",
        "verified": true
      },
      "price": 9.99,
      "currency": "USD",
      "rating": 4.8,
      "reviews": 234,
      "downloads": 5678,
      "updated_at": "2025-01-10T12:00:00Z",
      "preview": {
        "template_snippet": "// Preview code",
        "examples": 3,
        "languages": ["javascript", "typescript"]
      },
      "tags": ["auth", "jwt", "security", "production-ready"]
    }
  ],
  "facets": {
    "categories": {
      "security": 145,
      "data-access": 89,
      "api": 67
    },
    "price_ranges": {
      "free": 234,
      "0-10": 156,
      "10-50": 78,
      "50+": 23
    }
  },
  "pagination": {
    "total": 491,
    "limit": 20,
    "offset": 0
  }
}
```

#### GET /marketplace/patterns/{pattern_id}
**Response (200):**
Returns detailed pattern information including full template, examples, and documentation.

#### POST /marketplace/purchase
**Request:**
```json
{
  "pattern_id": "mkt_pat_123",
  "payment_method_id": "pm_1234567890",
  "license_type": "team"
}
```

**Response (200):**
```json
{
  "purchase_id": "pur_abc123",
  "pattern_id": "mkt_pat_123",
  "status": "completed",
  "amount": 9.99,
  "currency": "USD",
  "license": {
    "type": "team",
    "seats": 10,
    "expires_at": null,
    "key": "LIC-XXXX-XXXX-XXXX"
  },
  "download_url": "https://api.ccl.dev/v1/downloads/pur_abc123",
  "receipt_url": "https://ccl.dev/receipts/pur_abc123"
}
```

#### POST /marketplace/patterns
**Request:**
```json
{
  "name": "Microservices Communication Pattern",
  "description": "Battle-tested patterns for service-to-service communication",
  "category": "architecture",
  "price": 19.99,
  "license": "single-use",
  "pattern": {
    "code": "// Pattern template code",
    "variables": ["ServiceName", "Protocol"],
    "language": "javascript"
  },
  "documentation": "# Comprehensive docs here",
  "demo_repository": "https://github.com/author/pattern-demo",
  "support_email": "<EMAIL>"
}
```

**Response (201):**
```json
{
  "pattern_id": "mkt_pat_456",
  "status": "pending_review",
  "estimated_review_time": "2-3 business days",
  "submission_id": "sub_789"
}
```

### Webhook Events

CCL sends webhooks for the following events:

#### Core Events (from source API documentation)
| Event | Description | Payload |
|-------|-------------|---------|
| `analysis.started` | Analysis began | Analysis object |
| `analysis.completed` | Analysis finished | Full analysis results |
| `analysis.failed` | Analysis error | Error details |
| `pattern.detected` | New pattern found | Pattern object |
| `repository.updated` | Repository changed | Repository object |

#### Extended Events (additional events available)
| Event | Description | Payload |
|-------|-------------|---------|
| `pattern.validated` | Pattern validation completed | Pattern validation results |
| `pattern.published` | Pattern published to marketplace | Published pattern object |
| `repository.added` | New repository added | Repository object |
| `repository.removed` | Repository removed | Repository object |
| `pattern.purchased` | Pattern purchased from marketplace | Purchase object |
| `pattern.downloaded` | Pattern downloaded | Download object |
| `query.completed` | Query processing completed | Query results |
| `conversation.started` | New conversation started | Conversation object |

#### Webhook Payload Format
```json
{
  "id": "evt_abc123",
  "type": "analysis.completed",
  "created": "2025-01-15T10:04:32Z",
  "data": {
    // Event-specific data
  }
}
```

#### Webhook Security
Webhooks are secured using HMAC-SHA256 signatures. Verify webhook signatures using:

```javascript
const crypto = require('crypto');

function verifyWebhook(payload, signature, secret) {
  const expected = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex');

  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(`sha256=${expected}`)
  );
}
```

**Note:** For complete webhook system implementation including event filtering, retry logic, and delivery management, see the [Webhooks PRP](./webhooks.md).

## Implementation Blueprint

### Phase 1: API Foundation
1. **API Gateway Setup**
   ```yaml
   # API Gateway configuration
   swagger: "3.0.0"
   info:
     title: CCL Platform API
     version: "1.0.0"
     description: Codebase Context Layer Platform API
   
   servers:
     - url: https://api.ccl.dev/v1
       description: Production server
     - url: https://api-staging.ccl.dev/v1
       description: Staging server
   ```

2. **Authentication Middleware**
   ```javascript
   // Authentication middleware implementation
   const authenticateToken = async (req, res, next) => {
     const authHeader = req.headers['authorization'];
     const token = authHeader && authHeader.split(' ')[1];
     
     if (!token) {
       return res.status(401).json({
         error: {
           code: 'UNAUTHORIZED',
           message: 'Access token required'
         }
       });
     }
     
     try {
       const decoded = jwt.verify(token, process.env.JWT_SECRET);
       req.user = decoded;
       next();
     } catch (error) {
       return res.status(403).json({
         error: {
           code: 'FORBIDDEN',
           message: 'Invalid or expired token'
         }
       });
     }
   };
   ```

3. **Input Validation Framework**
   ```javascript
   // Input validation using Joi
   const analysisSchema = Joi.object({
     repository_url: Joi.string().uri().required(),
     branch: Joi.string().default('main'),
     include_patterns: Joi.array().items(Joi.string()),
     exclude_patterns: Joi.array().items(Joi.string()),
     analysis_type: Joi.string().valid('full', 'incremental').default('full')
   });
   ```

### Phase 2: Core API Endpoints
1. **Analysis Endpoints**
   ```javascript
   // POST /analyze
   app.post('/analyze', authenticateToken, validateInput(analysisSchema), async (req, res) => {
     try {
       const analysis = await analysisService.startAnalysis(req.body, req.user);
       res.status(201).json({
         analysis_id: analysis.id,
         status: 'pending',
         created_at: analysis.createdAt,
         estimated_completion: analysis.estimatedCompletion,
         repository: analysis.repository,
         webhook_url: analysis.webhookUrl
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });

   // GET /analysis/{id}
   app.get('/analysis/:id', authenticateToken, async (req, res) => {
     try {
       const analysis = await analysisService.getAnalysis(req.params.id, req.user);
       res.json({
         analysis_id: analysis.id,
         status: analysis.status,
         started_at: analysis.startedAt,
         completed_at: analysis.completedAt,
         duration_seconds: analysis.durationSeconds,
         repository: analysis.repository,
         statistics: analysis.statistics,
         results: analysis.results
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });

   // GET /analysis (list analyses)
   app.get('/analysis', authenticateToken, async (req, res) => {
     try {
       const analyses = await analysisService.listAnalyses(req.query, req.user);
       res.json({
         analyses: analyses.items,
         pagination: analyses.pagination
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

2. **Pattern Endpoints**
   ```javascript
   // GET /patterns
   app.get('/patterns', authenticateToken, async (req, res) => {
     try {
       const patterns = await patternService.searchPatterns(req.query, req.user);
       res.json({
         patterns: patterns.items,
         pagination: patterns.pagination,
         filters: patterns.appliedFilters
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });

   // POST /patterns/validate
   app.post('/patterns/validate', authenticateToken, async (req, res) => {
     try {
       const validation = await patternService.validateCode(req.body, req.user);
       res.json({
         valid: validation.valid,
         confidence: validation.confidence,
         violations: validation.violations,
         suggestions: validation.suggestions
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

3. **Query Endpoints**
   ```javascript
   // POST /query
   app.post('/query', authenticateToken, async (req, res) => {
     try {
       const result = await queryService.processQuery(req.body, req.user);
       res.json({
         answer: result.answer,
         confidence: result.confidence,
         conversation_id: result.conversationId,
         sources: result.sources,
         code_examples: result.codeExamples,
         patterns_referenced: result.patternsReferenced,
         suggested_questions: result.suggestedQuestions,
         metadata: result.metadata
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });

   // POST /conversations
   app.post('/conversations', authenticateToken, async (req, res) => {
     try {
       const conversation = await queryService.createConversation(req.body, req.user);
       res.status(201).json(conversation);
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

### Phase 3: Advanced Features
1. **Rate Limiting**
   ```javascript
   const rateLimit = require('express-rate-limit');
   
   const createRateLimiter = (windowMs, max) => {
     return rateLimit({
       windowMs,
       max,
       message: {
         error: {
           code: 'RATE_LIMIT_EXCEEDED',
           message: 'Too many requests, please try again later'
         }
       },
       standardHeaders: true,
       legacyHeaders: false
     });
   };
   
   // Apply different limits based on user tier
   app.use('/analyze', createRateLimiter(60 * 60 * 1000, getUserRateLimit));
   ```

2. **API Versioning**
   ```javascript
   // Version-specific routing
   app.use('/v1', v1Router);
   app.use('/v2', v2Router);
   
   // Backward compatibility middleware
   const handleVersionCompatibility = (req, res, next) => {
     const version = req.path.split('/')[1];
     if (version === 'v1') {
       // Apply v1 compatibility transformations
       req.body = transformV1Request(req.body);
     }
     next();
   };
   ```

3. **Webhook Support**
   ```javascript
   // Webhook delivery system
   const deliverWebhook = async (url, payload, secret) => {
     const signature = crypto
       .createHmac('sha256', secret)
       .update(JSON.stringify(payload))
       .digest('hex');

     await axios.post(url, payload, {
       headers: {
         'X-CCL-Signature': `sha256=${signature}`,
         'Content-Type': 'application/json'
       },
       timeout: 10000
     });
   };
   ```

4. **Repository Management Endpoints**
   ```javascript
   // POST /repositories
   app.post('/repositories', authenticateToken, async (req, res) => {
     try {
       const repository = await repositoryService.createRepository(req.body, req.user);
       res.status(201).json(repository);
     } catch (error) {
       handleApiError(error, res);
     }
   });

   // GET /repositories/{id}
   app.get('/repositories/:id', authenticateToken, async (req, res) => {
     try {
       const repository = await repositoryService.getRepository(req.params.id, req.user);
       res.json({
         id: repository.id,
         name: repository.name,
         url: repository.url,
         provider: repository.provider,
         branch: repository.branch,
         created_at: repository.createdAt,
         last_analysis: repository.lastAnalysis,
         statistics: repository.statistics,
         settings: repository.settings
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

5. **Simulation Endpoints**
   ```javascript
   // POST /simulate
   app.post('/simulate', authenticateToken, async (req, res) => {
     try {
       const simulation = await simulationService.simulateArchitectureChange(req.body, req.user);
       res.json({
         simulation_id: simulation.id,
         feasibility: simulation.feasibility,
         estimated_effort: simulation.estimatedEffort,
         risk_assessment: simulation.riskAssessment,
         migration_plan: simulation.migrationPlan,
         impact_analysis: simulation.impactAnalysis
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

6. **Code Generation Endpoints**
   ```javascript
   // POST /generate
   app.post('/generate', authenticateToken, async (req, res) => {
     try {
       const generated = await codeGenService.generateFromPattern(req.body, req.user);
       res.json({
         generated_code: generated.code,
         integration_instructions: generated.instructions
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

7. **Marketplace Endpoints**
   ```javascript
   // GET /marketplace/patterns
   app.get('/marketplace/patterns', authenticateToken, async (req, res) => {
     try {
       const patterns = await marketplaceService.searchPatterns(req.query, req.user);
       res.json({
         patterns: patterns.items,
         facets: patterns.facets,
         pagination: patterns.pagination
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });

   // POST /marketplace/purchase
   app.post('/marketplace/purchase', authenticateToken, async (req, res) => {
     try {
       const purchase = await marketplaceService.purchasePattern(req.body, req.user);
       res.json({
         purchase_id: purchase.id,
         pattern_id: purchase.patternId,
         status: purchase.status,
         amount: purchase.amount,
         currency: purchase.currency,
         license: purchase.license,
         download_url: purchase.downloadUrl,
         receipt_url: purchase.receiptUrl
       });
     } catch (error) {
       handleApiError(error, res);
     }
   });
   ```

### Phase 4: Documentation & Testing
1. **OpenAPI Specification**
   ```yaml
   # Complete OpenAPI spec
   paths:
     /analyze:
       post:
         summary: Start code analysis
         operationId: startAnalysis
         requestBody:
           required: true
           content:
             application/json:
               schema:
                 $ref: '#/components/schemas/AnalysisRequest'
         responses:
           '201':
             description: Analysis started successfully
             content:
               application/json:
                 schema:
                   $ref: '#/components/schemas/AnalysisResponse'
   ```

2. **API Testing Suite**
   ```javascript
   // Comprehensive API tests
   describe('Analysis API', () => {
     test('POST /analyze should start analysis', async () => {
       const response = await request(app)
         .post('/analyze')
         .set('Authorization', `Bearer ${validToken}`)
         .send(validAnalysisRequest)
         .expect(201);
       
       expect(response.body).toHaveProperty('id');
       expect(response.body.status).toBe('started');
     });
   });
   ```

## Validation Gates

### API Functionality Testing
```bash
# Authentication test
curl -X POST https://api.ccl.dev/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Analysis endpoint test
curl -X POST https://api.ccl.dev/v1/analyze \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/test/repo"}'

# Pattern search test
curl -H "Authorization: Bearer $TOKEN" \
  "https://api.ccl.dev/v1/patterns?category=design-patterns&limit=10"
```

### Performance Testing
```bash
# Load testing with Apache Bench
ab -n 1000 -c 10 -H "Authorization: Bearer $TOKEN" \
  https://api.ccl.dev/v1/patterns

# Rate limiting test
for i in {1..150}; do
  curl -H "Authorization: Bearer $TOKEN" \
    https://api.ccl.dev/v1/patterns
done
```

### Security Testing
```bash
# Test without authentication
curl -X POST https://api.ccl.dev/v1/analyze \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/test/repo"}'

# Test with invalid token
curl -X POST https://api.ccl.dev/v1/analyze \
  -H "Authorization: Bearer invalid_token" \
  -H "Content-Type: application/json" \
  -d '{"repository_url": "https://github.com/test/repo"}'
```

## Success Metrics

### Performance Metrics
- **Response Time**: <100ms (p95)
- **Availability**: 99.9%
- **Throughput**: 1000+ requests/second
- **Error Rate**: <1%

### Security Metrics
- **Authentication Success Rate**: >99%
- **Authorization Accuracy**: 100%
- **Input Validation Coverage**: 100%
- **Security Scan Results**: No high/critical issues

### Developer Experience Metrics
- **API Documentation Completeness**: 100%
- **Error Message Clarity**: >90% helpful
- **SDK Integration Success**: >95%
- **Developer Satisfaction**: >4.5/5

## Final Validation Checklist

### Core Functionality
- [ ] All API endpoints implemented and tested
  - [ ] Analysis endpoints (analyze, status, list)
  - [ ] Pattern endpoints (CRUD, validation)
  - [ ] Query endpoints (query, conversations)
  - [ ] Repository management endpoints
  - [ ] Simulation endpoints
  - [ ] Code generation endpoints
  - [ ] Marketplace endpoints (search, purchase, publish)
- [ ] Authentication and authorization working
  - [ ] API key authentication
  - [ ] OAuth 2.0 flow
  - [ ] Scope validation
- [ ] Input validation comprehensive
  - [ ] Schema validation for all endpoints
  - [ ] Field-level validation
  - [ ] Custom validation rules
- [ ] Error handling consistent
  - [ ] Standard error format
  - [ ] Comprehensive error codes
  - [ ] Validation error details
- [ ] Rate limiting functional
  - [ ] Tier-based limits
  - [ ] Rate limit headers
  - [ ] Proper error responses

### Documentation & Testing
- [ ] API documentation complete
  - [ ] All endpoints documented
  - [ ] Request/response examples
  - [ ] Error response examples
- [ ] OpenAPI specification accurate
  - [ ] Complete schema definitions
  - [ ] Authentication flows
  - [ ] Error responses
- [ ] Performance benchmarks met
  - [ ] Response time <100ms (p95)
  - [ ] Throughput >1000 req/sec
  - [ ] Error rate <1%
- [ ] Security testing passed
  - [ ] Authentication bypass tests
  - [ ] Authorization tests
  - [ ] Input validation tests
  - [ ] Rate limiting tests

### Advanced Features
- [ ] Webhook delivery working
  - [ ] Event generation
  - [ ] Signature verification
  - [ ] Retry mechanism
- [ ] API versioning implemented
  - [ ] Version routing
  - [ ] Backward compatibility
  - [ ] Deprecation handling
- [ ] Monitoring and alerting configured
  - [ ] Request metrics
  - [ ] Error tracking
  - [ ] Performance monitoring
  - [ ] Security alerts

---

## Cross-References

### Related PRPs
- **[Authentication PRP](../security/authentication.md)** - OAuth 2.0 and API key implementation details
- **[Authorization PRP](../security/authorization.md)** - Scope-based access control
- **[Webhooks PRP](./webhooks.md)** - Webhook system implementation
- **[GraphQL API PRP](./graphql-api.md)** - Alternative API interface
- **[Simulations API PRP](./simulations-api.md)** - Architecture simulation details
- **[Code Generation API PRP](./code-generation-api.md)** - Pattern-based code generation
- **[Marketplace PRP](../services/marketplace.md)** - Pattern marketplace functionality

### Source Documentation
- **[CCL API Documentation](../../prp_docs/ccl-api-docs.md)** - Complete API specification
- **[CCL Master Architecture](../../prp_docs/ccl-master-architecture.md)** - System architecture overview
- **[CCL Security & Compliance](../../prp_docs/ccl-security-compliance.md)** - Security requirements
- **[CCL Technical Specification](../../prp_docs/ccl-technical-spec.md)** - Implementation details

### Implementation References
- **[API Documentation](../../docs/api/README.md)** - API implementation patterns
- **[Security Documentation](../../docs/security/README.md)** - Security implementation
- **[Architecture Documentation](../../docs/architecture/README.md)** - System architecture

---

## Implementation Notes

### API Design Best Practices
- Follow RESTful principles consistently
- Use proper HTTP status codes
- Implement comprehensive error handling
- Provide clear and helpful error messages

### Security Implementation
- Validate all input data thoroughly
- Implement proper authentication and authorization
- Use HTTPS for all communications
- Implement rate limiting and abuse prevention

### Performance Optimization
- Implement efficient database queries
- Use caching for frequently accessed data
- Optimize response payload sizes
- Monitor and optimize API performance continuously
