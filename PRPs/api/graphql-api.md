# GraphQL API Implementation

name: "GraphQL API Implementation"
description: |
  Complete GraphQL API implementation for flexible data querying and real-time subscriptions in the CCL platform.
  
  Core Principles:
  - **Schema-First Design**: Define schema before implementation
  - **Type Safety**: Strong typing throughout
  - **Efficient Queries**: Solve N+1 problems with DataLoaders
  - **Real-Time Updates**: Subscriptions for live data
  - **Permission Based**: Field-level authorization

## Goal

Implement a comprehensive GraphQL API that provides flexible querying capabilities, real-time subscriptions, and efficient data fetching for the CCL platform.

## Why

GraphQL provides advantages over REST for:
- Frontend flexibility in data fetching
- Reduced over-fetching and under-fetching
- Real-time capabilities with subscriptions
- Strong typing and introspection
- Single endpoint for all operations

This enables:
- Efficient mobile applications
- Real-time collaboration features
- Flexible frontend development
- Better developer experience
- Reduced API calls

## What

### User-Visible Behavior
- Flexible data querying from frontend
- Real-time updates for collaboration
- Efficient mobile app performance
- Type-safe API interactions
- Interactive API playground

### Technical Requirements
- [ ] Complete GraphQL schema definition
- [ ] Query resolvers for all data types
- [ ] Mutation resolvers for all operations
- [ ] Subscription resolvers for real-time updates
- [ ] DataLoader implementation for N+1 prevention
- [ ] Authentication and authorization
- [ ] Error handling and validation
- [ ] Performance optimization

### Success Criteria
- [ ] <50ms resolver response time
- [ ] Schema covers all data needs
- [ ] Real-time subscriptions working
- [ ] N+1 queries eliminated
- [ ] Type safety enforced

## All Needed Context

### Documentation & References
- url: https://graphql.org/learn/schema/
  why: GraphQL schema design principles
- url: https://www.apollographql.com/docs/apollo-server/
  why: Apollo Server implementation guide
- url: https://github.com/graphql/dataloader
  why: DataLoader for efficient data fetching
- file: docs/api/README.md
  why: Current API requirements

### Schema Design Principles

```yaml
Schema Organization:
  - Query: Read operations
  - Mutation: Write operations  
  - Subscription: Real-time updates
  - Types: Domain objects
  - Inputs: Operation parameters
  - Enums: Predefined values
  - Interfaces: Shared contracts
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Use DataLoaders to prevent N+1 queries
- **CRITICAL**: Implement query depth limiting
- **GOTCHA**: Subscription memory leaks with poor cleanup
- **GOTCHA**: Large query complexity can cause timeouts
- **WARNING**: Introspection should be disabled in production
- **TIP**: Use schema stitching for service boundaries
- **TIP**: Cache schema compilation for performance

## Implementation Blueprint

### Core GraphQL Schema

```graphql
# schema.graphql
scalar DateTime
scalar JSON
scalar Upload

schema {
  query: Query
  mutation: Mutation
  subscription: Subscription
}

# Root Query Type
type Query {
  # Repository queries
  repository(id: ID!): Repository
  repositories(
    first: Int = 20
    after: String
    filter: RepositoryFilter
  ): RepositoryConnection!
  
  # Analysis queries
  analysis(id: ID!): Analysis
  analyses(
    first: Int = 20
    after: String
    filter: AnalysisFilter
  ): AnalysisConnection!
  
  # Pattern queries
  pattern(id: ID!): Pattern
  patterns(
    first: Int = 50
    after: String
    filter: PatternFilter
  ): PatternConnection!
  
  # Query conversations
  conversation(id: ID!): Conversation
  conversations(
    first: Int = 20
    after: String
    filter: ConversationFilter
  ): ConversationConnection!
  
  # Search across all content
  search(
    query: String!
    type: SearchType
    first: Int = 20
    after: String
  ): SearchConnection!
  
  # User and organization
  me: User
  organization(id: ID!): Organization
  
  # Marketplace
  marketplace: MarketplaceQuery!
}

# Root Mutation Type
type Mutation {
  # Repository operations
  addRepository(input: AddRepositoryInput!): AddRepositoryPayload!
  updateRepository(input: UpdateRepositoryInput!): UpdateRepositoryPayload!
  deleteRepository(input: DeleteRepositoryInput!): DeleteRepositoryPayload!
  
  # Analysis operations
  startAnalysis(input: StartAnalysisInput!): StartAnalysisPayload!
  stopAnalysis(input: StopAnalysisInput!): StopAnalysisPayload!
  
  # Pattern operations
  createPattern(input: CreatePatternInput!): CreatePatternPayload!
  updatePattern(input: UpdatePatternInput!): UpdatePatternPayload!
  deletePattern(input: DeletePatternInput!): DeletePatternPayload!
  validatePattern(input: ValidatePatternInput!): ValidatePatternPayload!
  
  # Query operations
  executeQuery(input: ExecuteQueryInput!): ExecuteQueryPayload!
  createConversation(input: CreateConversationInput!): CreateConversationPayload!
  addMessage(input: AddMessageInput!): AddMessagePayload!
  
  # User operations
  updateProfile(input: UpdateProfileInput!): UpdateProfilePayload!
  updatePreferences(input: UpdatePreferencesInput!): UpdatePreferencesPayload!
  
  # Marketplace operations
  publishPattern(input: PublishPatternInput!): PublishPatternPayload!
  purchasePattern(input: PurchasePatternInput!): PurchasePatternPayload!
}

# Root Subscription Type
type Subscription {
  # Analysis updates
  analysisUpdated(analysisId: ID!): AnalysisUpdate!
  
  # Real-time conversation
  messageAdded(conversationId: ID!): Message!
  userTyping(conversationId: ID!): TypingIndicator!
  
  # Collaboration updates
  collaborationUpdated(repositoryId: ID!): CollaborationUpdate!
  
  # Pattern updates
  patternDetected(repositoryId: ID!): PatternDetection!
  
  # Notification updates
  notificationReceived: Notification!
}

# Core Domain Types
type Repository {
  id: ID!
  name: String!
  url: String!
  provider: RepositoryProvider!
  owner: User!
  organization: Organization
  
  # Metadata
  description: String
  language: String!
  languages: [LanguageStats!]!
  totalFiles: Int!
  totalLines: Int!
  lastAnalyzedAt: DateTime
  createdAt: DateTime!
  updatedAt: DateTime!
  
  # Related data
  analyses(
    first: Int = 10
    after: String
    filter: AnalysisFilter
  ): AnalysisConnection!
  
  patterns(
    first: Int = 50
    after: String
    filter: PatternFilter
  ): PatternConnection!
  
  conversations(
    first: Int = 20
    after: String
  ): ConversationConnection!
  
  # Computed fields
  qualityScore: Float
  complexityScore: Float
  healthStatus: RepositoryHealth!
  
  # Permissions
  permissions: RepositoryPermissions!
}

type Analysis {
  id: ID!
  repository: Repository!
  status: AnalysisStatus!
  
  # Timing
  startedAt: DateTime!
  completedAt: DateTime
  duration: Int # seconds
  
  # Metadata
  commit: String
  branch: String!
  triggeredBy: User!
  options: AnalysisOptions!
  
  # Results
  statistics: AnalysisStatistics
  results: AnalysisResults
  error: AnalysisError
  
  # Related data
  patterns(
    first: Int = 50
    after: String
    filter: PatternFilter
  ): PatternConnection!
  
  files(
    first: Int = 100
    after: String
    filter: FileFilter
  ): FileAnalysisConnection!
}

type Pattern {
  id: ID!
  repository: Repository!
  analysis: Analysis!
  
  # Pattern details
  type: PatternType!
  name: String!
  description: String!
  confidence: Float!
  
  # Occurrences
  occurrences: Int!
  firstSeen: DateTime!
  lastSeen: DateTime!
  
  # Location information
  locations: [PatternLocation!]!
  
  # Pattern content
  template: PatternTemplate!
  examples: [PatternExample!]!
  
  # Quality metrics
  qualityScore: Float!
  severity: PatternSeverity
  
  # Relationships
  relatedPatterns: [Pattern!]!
  violatedBy: [PatternViolation!]!
  
  # Marketplace info (if published)
  marketplaceInfo: MarketplacePatternInfo
  
  # Metadata
  tags: [String!]!
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Conversation {
  id: ID!
  repository: Repository
  user: User!
  
  # Conversation details
  title: String
  lastMessageAt: DateTime!
  messageCount: Int!
  
  # Messages
  messages(
    first: Int = 50
    after: String
  ): MessageConnection!
  
  # Context
  context: ConversationContext!
  
  # Metadata
  createdAt: DateTime!
  updatedAt: DateTime!
}

type Message {
  id: ID!
  conversation: Conversation!
  role: MessageRole!
  content: String!
  
  # User info (for user messages)
  user: User
  
  # AI response metadata
  confidence: Float
  model: String
  tokensUsed: Int
  
  # Sources and references
  sources: [QuerySource!]!
  codeExamples: [CodeExample!]!
  patternsReferenced: [PatternReference!]!
  
  # Feedback
  feedback: MessageFeedback
  
  # Metadata
  createdAt: DateTime!
}

# Supporting Types
type User {
  id: ID!
  email: String!
  name: String!
  avatarUrl: String
  
  # Profile
  bio: String
  location: String
  website: String
  company: String
  
  # Subscription
  subscriptionTier: SubscriptionTier!
  subscriptionExpiresAt: DateTime
  
  # Usage stats
  apiUsage: ApiUsage!
  
  # Preferences
  preferences: UserPreferences!
  
  # Related data
  repositories(
    first: Int = 20
    after: String
  ): RepositoryConnection!
  
  conversations(
    first: Int = 20
    after: String
  ): ConversationConnection!
  
  # Permissions
  permissions: [Permission!]!
  
  # Metadata
  createdAt: DateTime!
  lastActiveAt: DateTime
}

# Connection Types (for pagination)
type RepositoryConnection {
  edges: [RepositoryEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type RepositoryEdge {
  node: Repository!
  cursor: String!
}

type AnalysisConnection {
  edges: [AnalysisEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type AnalysisEdge {
  node: Analysis!
  cursor: String!
}

type PatternConnection {
  edges: [PatternEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type PatternEdge {
  node: Pattern!
  cursor: String!
}

type MessageConnection {
  edges: [MessageEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type MessageEdge {
  node: Message!
  cursor: String!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

# Input Types
input AddRepositoryInput {
  name: String!
  url: String!
  provider: RepositoryProvider!
  organizationId: ID
  autoAnalyze: Boolean = true
}

input StartAnalysisInput {
  repositoryId: ID!
  branch: String = "main"
  commit: String
  options: AnalysisOptionsInput
}

input AnalysisOptionsInput {
  incremental: Boolean = false
  deepPatternAnalysis: Boolean = true
  generateEmbeddings: Boolean = true
  includePaths: [String!]
  excludePaths: [String!]
  patternConfidenceThreshold: Float = 0.8
}

input ExecuteQueryInput {
  query: String!
  repositoryId: ID
  conversationId: ID
  context: QueryContextInput
  stream: Boolean = false
  model: QueryModel = ADVANCED
}

input QueryContextInput {
  focusArea: String
  includeCode: Boolean = true
  maxExamples: Int = 3
}

# Filter Types
input RepositoryFilter {
  provider: RepositoryProvider
  language: String
  hasAnalysis: Boolean
  organizationId: ID
  search: String
}

input AnalysisFilter {
  status: AnalysisStatus
  startedAfter: DateTime
  startedBefore: DateTime
  repositoryId: ID
}

input PatternFilter {
  type: PatternType
  minConfidence: Float
  language: String
  repositoryId: ID
  analysisId: ID
  search: String
}

# Enum Types
enum RepositoryProvider {
  GITHUB
  GITLAB
  BITBUCKET
  LOCAL
}

enum AnalysisStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum PatternType {
  ARCHITECTURAL
  DESIGN
  IDIOM
  ANTI_PATTERN
  SECURITY
  PERFORMANCE
}

enum PatternSeverity {
  INFO
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum MessageRole {
  USER
  ASSISTANT
  SYSTEM
}

enum SubscriptionTier {
  FREE
  PRO
  TEAM
  ENTERPRISE
}

enum QueryModel {
  FAST
  ADVANCED
}

enum SearchType {
  ALL
  REPOSITORIES
  PATTERNS
  CONVERSATIONS
  CODE
}

# Union Types
union SearchResult = Repository | Pattern | Conversation | CodeSnippet

# Interface Types
interface Node {
  id: ID!
}

interface Timestamped {
  createdAt: DateTime!
  updatedAt: DateTime!
}

# Payload Types (for mutations)
type AddRepositoryPayload {
  repository: Repository
  errors: [UserError!]!
}

type StartAnalysisPayload {
  analysis: Analysis
  errors: [UserError!]!
}

type ExecuteQueryPayload {
  message: Message
  conversation: Conversation
  errors: [UserError!]!
}

type UserError {
  field: String
  message: String!
  code: String
}
```

### Resolver Implementation

```typescript
// resolvers/index.ts
import { Resolvers } from '../generated/graphql';
import { Context } from '../context';
import { queryResolvers } from './query';
import { mutationResolvers } from './mutation';
import { subscriptionResolvers } from './subscription';
import { scalarResolvers } from './scalars';

export const resolvers: Resolvers<Context> = {
  ...scalarResolvers,
  Query: queryResolvers,
  Mutation: mutationResolvers,
  Subscription: subscriptionResolvers,
  
  // Type resolvers
  Repository: {
    owner: async (parent, args, { dataSources, user }) => {
      return dataSources.userAPI.findById(parent.ownerId);
    },
    
    organization: async (parent, args, { dataSources }) => {
      if (!parent.organizationId) return null;
      return dataSources.organizationAPI.findById(parent.organizationId);
    },
    
    analyses: async (parent, args, { dataSources }) => {
      return dataSources.analysisAPI.findByRepository(
        parent.id,
        args.first,
        args.after,
        args.filter
      );
    },
    
    patterns: async (parent, args, { dataSources }) => {
      return dataSources.patternAPI.findByRepository(
        parent.id,
        args.first,
        args.after,
        args.filter
      );
    },
    
    qualityScore: async (parent, args, { dataSources }) => {
      const analysis = await dataSources.analysisAPI.getLatestForRepository(parent.id);
      return analysis?.results?.qualityMetrics?.maintainabilityIndex;
    },
    
    permissions: async (parent, args, { user, dataSources }) => {
      return dataSources.permissionAPI.getRepositoryPermissions(parent.id, user.id);
    }
  },
  
  Analysis: {
    repository: async (parent, args, { dataSources }) => {
      return dataSources.repositoryAPI.findById(parent.repositoryId);
    },
    
    triggeredBy: async (parent, args, { dataSources }) => {
      return dataSources.userAPI.findById(parent.triggeredById);
    },
    
    patterns: async (parent, args, { dataSources }) => {
      return dataSources.patternAPI.findByAnalysis(
        parent.id,
        args.first,
        args.after,
        args.filter
      );
    }
  },
  
  Pattern: {
    repository: async (parent, args, { dataSources }) => {
      return dataSources.repositoryAPI.findById(parent.repositoryId);
    },
    
    analysis: async (parent, args, { dataSources }) => {
      return dataSources.analysisAPI.findById(parent.analysisId);
    },
    
    relatedPatterns: async (parent, args, { dataSources }) => {
      return dataSources.patternAPI.findRelated(parent.id);
    },
    
    marketplaceInfo: async (parent, args, { dataSources }) => {
      return dataSources.marketplaceAPI.getPatternInfo(parent.id);
    }
  },
  
  User: {
    repositories: async (parent, args, { dataSources, user }) => {
      // Only show repositories user has access to
      return dataSources.repositoryAPI.findByUser(
        parent.id,
        args.first,
        args.after,
        user
      );
    },
    
    apiUsage: async (parent, args, { dataSources }) => {
      return dataSources.usageAPI.getUsageStats(parent.id);
    },
    
    preferences: async (parent, args, { dataSources }) => {
      return dataSources.userAPI.getPreferences(parent.id);
    }
  },
  
  // Union type resolvers
  SearchResult: {
    __resolveType: (obj) => {
      if ('url' in obj) return 'Repository';
      if ('confidence' in obj) return 'Pattern';
      if ('messageCount' in obj) return 'Conversation';
      if ('lineNumber' in obj) return 'CodeSnippet';
      return null;
    }
  }
};
```

### DataLoader Implementation

```typescript
// dataloaders/index.ts
import DataLoader from 'dataloader';
import { Context } from '../context';

export function createDataLoaders(context: Context) {
  return {
    // User DataLoader
    userLoader: new DataLoader(async (userIds: readonly string[]) => {
      const users = await context.dataSources.userAPI.findByIds([...userIds]);
      return userIds.map(id => users.find(user => user.id === id) || null);
    }),
    
    // Repository DataLoader
    repositoryLoader: new DataLoader(async (repositoryIds: readonly string[]) => {
      const repositories = await context.dataSources.repositoryAPI.findByIds([...repositoryIds]);
      return repositoryIds.map(id => repositories.find(repo => repo.id === id) || null);
    }),
    
    // Analysis DataLoader
    analysisLoader: new DataLoader(async (analysisIds: readonly string[]) => {
      const analyses = await context.dataSources.analysisAPI.findByIds([...analysisIds]);
      return analysisIds.map(id => analyses.find(analysis => analysis.id === id) || null);
    }),
    
    // Pattern DataLoader
    patternLoader: new DataLoader(async (patternIds: readonly string[]) => {
      const patterns = await context.dataSources.patternAPI.findByIds([...patternIds]);
      return patternIds.map(id => patterns.find(pattern => pattern.id === id) || null);
    }),
    
    // Patterns by Repository DataLoader
    patternsByRepositoryLoader: new DataLoader(async (repositoryIds: readonly string[]) => {
      const allPatterns = await context.dataSources.patternAPI.findByRepositoryIds([...repositoryIds]);
      return repositoryIds.map(repoId => 
        allPatterns.filter(pattern => pattern.repositoryId === repoId)
      );
    }),
    
    // User permissions DataLoader
    userPermissionsLoader: new DataLoader(async (userIds: readonly string[]) => {
      const permissions = await context.dataSources.permissionAPI.findByUserIds([...userIds]);
      return userIds.map(userId => 
        permissions.filter(permission => permission.userId === userId)
      );
    })
  };
}

// Usage in resolvers
export const repositoryResolvers = {
  patterns: async (parent: Repository, args: any, { dataSources, loaders }: Context) => {
    // Use DataLoader to batch requests
    const patterns = await loaders.patternsByRepositoryLoader.load(parent.id);
    
    // Apply filtering and pagination
    return dataSources.patternAPI.applyFiltersAndPagination(
      patterns,
      args.first,
      args.after,
      args.filter
    );
  }
};
```

### Subscription Implementation

```typescript
// subscriptions/index.ts
import { PubSub } from 'graphql-subscriptions';
import { RedisPubSub } from 'graphql-redis-subscriptions';
import { withFilter } from 'graphql-subscriptions';

// Use Redis for production
const pubsub = process.env.NODE_ENV === 'production' 
  ? new RedisPubSub({
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD
      }
    })
  : new PubSub();

// Subscription event types
export enum SubscriptionEvent {
  ANALYSIS_UPDATED = 'ANALYSIS_UPDATED',
  MESSAGE_ADDED = 'MESSAGE_ADDED',
  USER_TYPING = 'USER_TYPING',
  PATTERN_DETECTED = 'PATTERN_DETECTED',
  NOTIFICATION_RECEIVED = 'NOTIFICATION_RECEIVED'
}

export const subscriptionResolvers = {
  analysisUpdated: {
    subscribe: withFilter(
      () => pubsub.asyncIterator([SubscriptionEvent.ANALYSIS_UPDATED]),
      (payload, variables, context) => {
        // Only send updates for analyses user has access to
        return payload.analysisUpdated.id === variables.analysisId &&
               context.user && 
               context.dataSources.permissionAPI.canViewAnalysis(
                 context.user.id, 
                 payload.analysisUpdated.id
               );
      }
    )
  },
  
  messageAdded: {
    subscribe: withFilter(
      () => pubsub.asyncIterator([SubscriptionEvent.MESSAGE_ADDED]),
      (payload, variables, context) => {
        return payload.messageAdded.conversationId === variables.conversationId &&
               context.user &&
               context.dataSources.permissionAPI.canViewConversation(
                 context.user.id,
                 variables.conversationId
               );
      }
    )
  },
  
  userTyping: {
    subscribe: withFilter(
      () => pubsub.asyncIterator([SubscriptionEvent.USER_TYPING]),
      (payload, variables, context) => {
        return payload.userTyping.conversationId === variables.conversationId &&
               payload.userTyping.userId !== context.user?.id; // Don't send to self
      }
    )
  },
  
  patternDetected: {
    subscribe: withFilter(
      () => pubsub.asyncIterator([SubscriptionEvent.PATTERN_DETECTED]),
      async (payload, variables, context) => {
        return payload.patternDetected.repositoryId === variables.repositoryId &&
               context.user &&
               await context.dataSources.permissionAPI.canViewRepository(
                 context.user.id,
                 variables.repositoryId
               );
      }
    )
  },
  
  notificationReceived: {
    subscribe: withFilter(
      () => pubsub.asyncIterator([SubscriptionEvent.NOTIFICATION_RECEIVED]),
      (payload, variables, context) => {
        return payload.notificationReceived.userId === context.user?.id;
      }
    )
  }
};

// Helper functions to publish events
export const publishAnalysisUpdate = (analysis: Analysis) => {
  pubsub.publish(SubscriptionEvent.ANALYSIS_UPDATED, {
    analysisUpdated: analysis
  });
};

export const publishMessageAdded = (message: Message) => {
  pubsub.publish(SubscriptionEvent.MESSAGE_ADDED, {
    messageAdded: message
  });
};

export const publishUserTyping = (conversationId: string, userId: string, isTyping: boolean) => {
  pubsub.publish(SubscriptionEvent.USER_TYPING, {
    userTyping: {
      conversationId,
      userId,
      isTyping,
      timestamp: new Date()
    }
  });
};
```

### Security and Authorization

```typescript
// auth/permissions.ts
import { AuthenticationError, ForbiddenError } from 'apollo-server-express';
import { Context } from '../context';

export class PermissionService {
  // Repository permissions
  async canViewRepository(userId: string, repositoryId: string): Promise<boolean> {
    const repository = await this.repositoryAPI.findById(repositoryId);
    if (!repository) return false;
    
    // Owner can always view
    if (repository.ownerId === userId) return true;
    
    // Organization members can view
    if (repository.organizationId) {
      const membership = await this.organizationAPI.getMembership(
        repository.organizationId,
        userId
      );
      return membership !== null;
    }
    
    // Public repositories can be viewed by anyone
    return repository.isPublic;
  }
  
  async canModifyRepository(userId: string, repositoryId: string): Promise<boolean> {
    const repository = await this.repositoryAPI.findById(repositoryId);
    if (!repository) return false;
    
    // Owner can always modify
    if (repository.ownerId === userId) return true;
    
    // Organization admins can modify
    if (repository.organizationId) {
      const membership = await this.organizationAPI.getMembership(
        repository.organizationId,
        userId
      );
      return membership?.role === 'admin';
    }
    
    return false;
  }
  
  // Analysis permissions
  async canViewAnalysis(userId: string, analysisId: string): Promise<boolean> {
    const analysis = await this.analysisAPI.findById(analysisId);
    if (!analysis) return false;
    
    return this.canViewRepository(userId, analysis.repositoryId);
  }
  
  // Pattern permissions
  async canCreatePattern(userId: string, repositoryId: string): Promise<boolean> {
    return this.canViewRepository(userId, repositoryId);
  }
  
  // Conversation permissions
  async canViewConversation(userId: string, conversationId: string): Promise<boolean> {
    const conversation = await this.conversationAPI.findById(conversationId);
    if (!conversation) return false;
    
    // Owner can always view
    if (conversation.userId === userId) return true;
    
    // Repository collaborators can view
    if (conversation.repositoryId) {
      return this.canViewRepository(userId, conversation.repositoryId);
    }
    
    return false;
  }
}

// Authorization directives
export const authDirectives = {
  requireAuth: (next: any, source: any, args: any, context: Context) => {
    if (!context.user) {
      throw new AuthenticationError('Authentication required');
    }
    return next();
  },
  
  requireRepositoryAccess: async (next: any, source: any, args: any, context: Context) => {
    if (!context.user) {
      throw new AuthenticationError('Authentication required');
    }
    
    const repositoryId = args.repositoryId || source.repositoryId;
    const canView = await context.dataSources.permissionAPI.canViewRepository(
      context.user.id,
      repositoryId
    );
    
    if (!canView) {
      throw new ForbiddenError('Insufficient permissions');
    }
    
    return next();
  }
};
```

### Query Complexity Analysis

```typescript
// security/queryComplexity.ts
import { createComplexityLimitRule } from 'graphql-query-complexity';
import { separateOperations } from 'graphql';

export const complexityLimitRule = createComplexityLimitRule(1000, {
  maximumComplexity: 1000,
  variables: {},
  introspection: true,
  scalarCost: 1,
  objectCost: 1,
  listFactor: 10,
  createError: (max: number, actual: number) => {
    return new Error(
      `Query complexity of ${actual} exceeds maximum complexity of ${max}`
    );
  },
  estimators: [
    // Custom complexity estimators
    {
      Repository: {
        patterns: ({ args }) => {
          const first = args.first || 50;
          return Math.min(first, 100) * 2; // Each pattern costs 2 points
        },
        analyses: ({ args }) => {
          const first = args.first || 10;
          return Math.min(first, 50) * 3; // Each analysis costs 3 points
        }
      },
      
      Analysis: {
        files: ({ args }) => {
          const first = args.first || 100;
          return Math.min(first, 500) * 1; // Each file costs 1 point
        }
      }
    }
  ]
});

// Query depth limiting
export const depthLimit = require('graphql-depth-limit')(10);
```

## Validation Loop

### Level 1: Schema Validation
```typescript
// Test schema compilation and validation
import { buildSchema } from 'graphql';
import { readFileSync } from 'fs';

describe('GraphQL Schema', () => {
  test('schema compiles without errors', () => {
    const schemaString = readFileSync('./schema.graphql', 'utf8');
    expect(() => buildSchema(schemaString)).not.toThrow();
  });
  
  test('all types are properly defined', () => {
    const schema = buildSchema(schemaString);
    const typeMap = schema.getTypeMap();
    
    expect(typeMap.Repository).toBeDefined();
    expect(typeMap.Pattern).toBeDefined();
    expect(typeMap.Analysis).toBeDefined();
  });
});
```

### Level 2: Resolver Testing
```typescript
// Test resolver functionality
describe('GraphQL Resolvers', () => {
  test('Repository.patterns resolver', async () => {
    const mockRepository = { id: 'repo1', ownerId: 'user1' };
    const mockContext = createMockContext();
    
    const result = await resolvers.Repository.patterns(
      mockRepository,
      { first: 10 },
      mockContext
    );
    
    expect(result.edges).toBeDefined();
    expect(result.pageInfo).toBeDefined();
  });
  
  test('authorization works correctly', async () => {
    const mockContext = createMockContext({ user: null });
    
    await expect(
      resolvers.Query.repository(null, { id: 'repo1' }, mockContext)
    ).rejects.toThrow('Authentication required');
  });
});
```

### Level 3: Integration Testing
```typescript
// Test complete GraphQL operations
import { createTestClient } from 'apollo-server-testing';

describe('GraphQL Integration', () => {
  const { query, mutate } = createTestClient(server);
  
  test('query repository with patterns', async () => {
    const GET_REPOSITORY = gql`
      query GetRepository($id: ID!) {
        repository(id: $id) {
          id
          name
          patterns(first: 5) {
            edges {
              node {
                id
                name
                confidence
              }
            }
            pageInfo {
              hasNextPage
            }
          }
        }
      }
    `;
    
    const response = await query({
      query: GET_REPOSITORY,
      variables: { id: 'repo1' }
    });
    
    expect(response.errors).toBeUndefined();
    expect(response.data.repository).toBeDefined();
  });
  
  test('subscription receives real-time updates', async () => {
    const ANALYSIS_UPDATED = gql`
      subscription AnalysisUpdated($analysisId: ID!) {
        analysisUpdated(analysisId: $analysisId) {
          id
          status
        }
      }
    `;
    
    // Test subscription setup and event delivery
    // Implementation depends on test framework
  });
});
```

## Final Validation Checklist

- [ ] Complete schema covers all domain objects
- [ ] All resolvers implemented with proper typing
- [ ] DataLoaders prevent N+1 queries
- [ ] Authentication and authorization working
- [ ] Subscriptions handle real-time updates
- [ ] Query complexity limiting configured
- [ ] Error handling provides useful messages
- [ ] Performance meets SLA requirements
- [ ] Schema introspection disabled in production
- [ ] Subscription memory leaks prevented

## Anti-Patterns to Avoid

1. **DON'T skip DataLoaders** - Causes N+1 query problems
2. **DON'T expose sensitive fields** - Use authorization
3. **DON'T allow unlimited query depth** - Enable depth limiting
4. **DON'T ignore query complexity** - Prevent DoS attacks
5. **DON'T leak subscription connections** - Clean up properly
6. **DON'T return internal errors** - Sanitize error messages
7. **DON'T skip input validation** - Validate all inputs
8. **DON'T allow introspection in production** - Security risk
9. **DON'T forget pagination** - Large result sets hurt performance
10. **DON'T mix business logic in resolvers** - Keep resolvers thin