name: "Collaboration Service Implementation"
description: |
  Implementation of the TypeScript-based real-time collaboration service for team features,
  shared workspaces, live code analysis sessions, and collaborative pattern development.

---

## Goal
Implement the Collaboration service as the real-time teamwork hub of the CCL platform, enabling shared code analysis sessions, team pattern development, collaborative workspaces, and real-time communication with sub-50ms latency.

## Why
- **Team Productivity**: Enable seamless team collaboration on code analysis
- **Real-time Interaction**: Live sharing of analysis results and insights
- **Knowledge Sharing**: Collaborative pattern development and curation
- **Enterprise Features**: Team management and workspace organization

## What
A high-performance TypeScript service that:
- Manages real-time collaborative sessions
- Provides shared workspaces and team management
- Enables live code analysis sharing
- Supports collaborative pattern development
- Handles real-time messaging and notifications
- Manages team permissions and access control

### Success Criteria
- [ ] Real-time updates <50ms latency
- [ ] WebSocket connections stable for >1 hour
- [ ] Collaborative sessions support 50+ concurrent users
- [ ] Team management features functional
- [ ] Shared workspace synchronization working
- [ ] Real-time notifications delivered
- [ ] Conflict resolution system operational
- [ ] 99.95% uptime requirement met

## All Needed Context

### Service Specifications
```yaml
Service Details:
  name: collaboration
  language: TypeScript/Node.js 20+
  runtime: Cloud Run
  port: 8005
  
Architecture:
  pattern: microservice
  communication: WebSocket + REST + Server-Sent Events
  data_store: Firestore + Redis + Spanner
  
Performance:
  slo_response_time: <50ms (real-time), <100ms (REST)
  slo_availability: 99.95%
  scaling: 0-200 instances
  memory: 2GB per instance
  cpu: 2 vCPU per instance
```

### Technology Stack
```yaml
Primary Language: TypeScript 5.0+
Runtime: Node.js 20 LTS
Framework: Express.js + Socket.IO
Database: Firestore, Redis, Spanner
Dependencies:
  - express: ^4.18+ # Web framework
  - socket.io: ^4.7+ # Real-time communication
  - @google-cloud/firestore: ^7.1+ # Real-time database
  - @google-cloud/spanner: ^6.12+ # Operational database
  - redis: ^4.6+ # Cache and pub/sub
  - jsonwebtoken: ^9.0+ # JWT authentication
  - zod: ^3.22+ # Schema validation
  - winston: ^3.11+ # Logging
  - prometheus-client: ^15.0+ # Metrics
  - uuid: ^9.0+ # UUID generation
  
Development Tools:
  - jest: Testing framework
  - eslint: Linting
  - prettier: Code formatting
  - typescript: Type checking
  - nodemon: Development server
```

### Current Codebase Context
```bash
# Reference implementation patterns from:
examples/collaboration/
├── websocket_handler.ts    # WebSocket connection patterns
├── session_manager.ts      # Session management patterns
├── team_management.ts      # Team management logic
└── real_time_sync.ts       # Real-time synchronization patterns
```

### Desired Service Structure
```bash
services/collaboration/
├── package.json            # Node.js dependencies
├── tsconfig.json           # TypeScript configuration
├── Dockerfile              # Container definition
├── cloudbuild.yaml         # Build configuration
├── src/
│   ├── index.ts            # Application entry point
│   ├── app.ts              # Express application setup
│   ├── config/             # Configuration management
│   │   ├── index.ts
│   │   └── settings.ts
│   ├── controllers/        # HTTP controllers
│   │   ├── auth.controller.ts      # Authentication
│   │   ├── teams.controller.ts     # Team management
│   │   ├── sessions.controller.ts  # Session management
│   │   └── health.controller.ts    # Health check
│   ├── services/           # Business logic
│   │   ├── collaboration.service.ts # Core collaboration
│   │   ├── session.service.ts      # Session management
│   │   ├── team.service.ts         # Team management
│   │   ├── notification.service.ts # Notifications
│   │   └── sync.service.ts         # Data synchronization
│   ├── websocket/          # WebSocket handlers
│   │   ├── index.ts        # Socket.IO setup
│   │   ├── session.handler.ts      # Session events
│   │   ├── team.handler.ts         # Team events
│   │   └── analysis.handler.ts     # Analysis sharing
│   ├── models/             # Data models
│   │   ├── session.model.ts        # Session models
│   │   ├── team.model.ts           # Team models
│   │   ├── user.model.ts           # User models
│   │   └── message.model.ts        # Message models
│   ├── clients/            # External service clients
│   │   ├── firestore.client.ts     # Firestore client
│   │   ├── redis.client.ts         # Redis client
│   │   ├── spanner.client.ts       # Spanner client
│   │   └── analysis.client.ts      # Analysis service client
│   ├── middleware/         # Express middleware
│   │   ├── auth.middleware.ts      # Authentication
│   │   ├── cors.middleware.ts      # CORS handling
│   │   ├── logging.middleware.ts   # Request logging
│   │   └── validation.middleware.ts # Input validation
│   ├── types/              # TypeScript type definitions
│   │   ├── session.types.ts        # Session types
│   │   ├── team.types.ts           # Team types
│   │   └── websocket.types.ts      # WebSocket types
│   └── utils/              # Utility functions
│       ├── validation.ts   # Input validation
│       ├── crypto.ts       # Cryptographic utilities
│       └── logger.ts       # Logging utilities
├── tests/                  # Test files
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   ├── websocket/         # WebSocket tests
│   └── fixtures/          # Test data
└── docs/
    ├── README.md          # Service documentation
    ├── websocket-api.md   # WebSocket API documentation
    └── rest-api.md        # REST API documentation
```

### Integration Requirements
```yaml
Upstream Dependencies:
  - analysis-engine: Real-time analysis results
  - query-intelligence: Shared query sessions
  - marketplace: Team pattern sharing
  - auth: User authentication and team permissions
  
Downstream Consumers:
  - web: Real-time UI updates and collaboration features
  - mobile: Mobile collaboration features
  - desktop: Desktop application integration
  
Event Subscriptions:
  - analysis.completed: Share analysis results in real-time
  - pattern.detected: Notify team of new patterns
  - user.joined_team: Team membership updates
  
Event Publications:
  - session.started: Collaboration session started
  - session.ended: Collaboration session ended
  - message.sent: Real-time message sent
  - analysis.shared: Analysis shared with team
  
External APIs:
  - Firestore: Real-time data synchronization
  - Redis: Pub/sub for real-time events
  - Spanner: Team and session metadata
  - SendGrid: Email notifications
```

### Known Gotchas & Library Quirks
```yaml
Node.js/TypeScript-Specific:
  - Event loop: Avoid blocking operations, use async/await properly
  - Memory leaks: Clean up event listeners and timers
  - Error handling: Use proper error boundaries and logging
  
Socket.IO:
  - Connection management: Handle reconnections and cleanup
  - Room management: Properly join/leave rooms to avoid memory leaks
  - Scaling: Use Redis adapter for multi-instance scaling
  
Firestore:
  - Real-time listeners: Manage listener lifecycle properly
  - Offline support: Handle offline/online state changes
  - Security rules: Implement proper security rules for team data
  
Redis:
  - Connection pooling: Use connection pooling for better performance
  - Pub/sub: Handle subscription cleanup on disconnect
  - Memory usage: Monitor Redis memory usage with large teams
  
Performance:
  - WebSocket scaling: Use sticky sessions or Redis adapter
  - Real-time updates: Batch updates to avoid overwhelming clients
  - Memory management: Monitor memory usage with long-running sessions
```

## Implementation Blueprint

### Phase 1: Service Foundation
1. **Project Setup**
   ```bash
   mkdir -p services/collaboration
   cd services/collaboration
   npm init -y
   npm install typescript @types/node ts-node nodemon
   ```

2. **Core Dependencies**
   - Express.js for HTTP API
   - Socket.IO for real-time communication
   - Firestore for real-time data
   - Redis for pub/sub and caching

3. **Basic Service Structure**
   - Express application setup
   - Socket.IO integration
   - Health check endpoints
   - Logging and metrics

### Phase 2: Real-time Communication
1. **WebSocket Infrastructure**
   - Socket.IO server setup
   - Connection management
   - Room-based communication
   - Authentication middleware

2. **Session Management**
   - Collaborative session creation
   - User presence tracking
   - Session state synchronization
   - Conflict resolution

3. **Real-time Events**
   - Analysis result sharing
   - Live cursor tracking
   - Real-time messaging
   - Notification delivery

### Phase 3: Team Management
1. **Team Operations**
   - Team creation and management
   - Member invitation and removal
   - Role-based permissions
   - Team settings and preferences

2. **Workspace Management**
   - Shared workspace creation
   - Workspace permissions
   - Resource sharing
   - Access control

3. **Collaboration Features**
   - Shared analysis sessions
   - Collaborative pattern development
   - Team discussions
   - Activity tracking

### Phase 4: Data Synchronization
1. **Real-time Sync**
   - Firestore real-time listeners
   - Conflict resolution algorithms
   - Operational transformation
   - State reconciliation

2. **Offline Support**
   - Offline data caching
   - Sync on reconnection
   - Conflict resolution
   - Data integrity

3. **Performance Optimization**
   - Connection pooling
   - Data compression
   - Efficient updates
   - Memory management

## Validation Gates

### Development Validation
```bash
# Code Quality
npm run lint
npm run type-check
npm run format:check

# Unit Tests
npm test

# Integration Tests
npm run test:integration

# WebSocket Tests
npm run test:websocket

# Performance Tests
npm run test:performance
```

### Real-time Communication Validation
```bash
# WebSocket Connection Test
wscat -c ws://localhost:8005/socket.io/?EIO=4&transport=websocket

# Session Management Test
node tests/integration/session-management.test.js

# Team Collaboration Test
node tests/integration/team-collaboration.test.js

# Load Testing
artillery run tests/load/websocket-load-test.yml
```

### API Validation
```bash
# Health Check
curl -f http://localhost:8005/health

# Team Creation
curl -X POST http://localhost:8005/teams \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name": "Test Team", "description": "Test team for validation"}'

# Session Creation
curl -X POST http://localhost:8005/sessions \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"team_id": "team-123", "type": "analysis", "name": "Code Review Session"}'
```

## Success Metrics

### Real-time Performance Metrics
- **Message Latency**: <50ms (p95)
- **Connection Stability**: >99% uptime per connection
- **Concurrent Users**: 50+ per session
- **Memory Usage**: <2GB per instance

### Collaboration Metrics
- **Session Success Rate**: >99%
- **Data Sync Accuracy**: >99.9%
- **Conflict Resolution Success**: >95%
- **User Engagement**: >80% active participation

### Business Metrics
- **Team Adoption Rate**: >60%
- **Session Duration**: >15 minutes average
- **Feature Usage**: >70% of collaboration features used
- **User Satisfaction**: >4.5/5

## Final Validation Checklist
- [ ] All unit tests pass (>85% coverage)
- [ ] Integration tests pass
- [ ] WebSocket functionality validated
- [ ] Real-time synchronization working
- [ ] Team management functional
- [ ] Performance benchmarks met
- [ ] Security validation passed
- [ ] API documentation complete
- [ ] WebSocket API documented
- [ ] Service documentation updated
- [ ] Monitoring configured
- [ ] Deployment successful

---

## Implementation Notes

### TypeScript Best Practices
- Use strict TypeScript configuration
- Define comprehensive type definitions
- Implement proper error handling with typed errors
- Use dependency injection for better testability

### Real-time Architecture
- Implement proper connection lifecycle management
- Use rooms for efficient message broadcasting
- Handle reconnection scenarios gracefully
- Implement proper authentication for WebSocket connections

### Performance Optimization
- Use Redis adapter for Socket.IO scaling
- Implement connection pooling for database clients
- Use efficient data structures for real-time state
- Monitor and optimize memory usage

### Security Considerations
- Validate all WebSocket messages
- Implement proper authentication and authorization
- Use secure communication protocols
- Audit team access and permissions regularly
