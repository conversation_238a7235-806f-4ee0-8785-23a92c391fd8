name: "Pattern Mining Service Implementation"
description: |
  Implementation of the Python-based ML service for pattern detection, learning, and classification
  using advanced machine learning algorithms and Vertex AI for training and inference.

---

## Goal
Implement the Pattern Mining service as the machine learning core of the CCL platform, capable of detecting coding patterns, learning from code analysis, training custom models, and providing pattern recommendations with high accuracy.

## Why
- **AI-Powered Intelligence**: Core ML capability for pattern recognition
- **Continuous Learning**: Improves pattern detection over time
- **Business Differentiation**: Unique pattern marketplace foundation
- **Developer Productivity**: Automated pattern detection and suggestions

## What
A sophisticated ML service that:
- Detects coding patterns from AST analysis
- Trains custom ML models on code patterns
- Classifies and categorizes detected patterns
- Provides pattern recommendations and suggestions
- Manages pattern lifecycle and versioning
- Integrates with Vertex AI for model training

### Success Criteria
- [ ] Pattern detection accuracy >95%
- [ ] Model training completes <5 minutes for standard datasets
- [ ] Pattern classification accuracy >90%
- [ ] Real-time pattern scoring <50ms
- [ ] Integration with Vertex AI working
- [ ] Pattern storage and retrieval functional
- [ ] ML pipeline automated and monitored
- [ ] Memory usage <8GB for training, <4GB for inference
- [ ] Model deployment latency <30 seconds
- [ ] 99% uptime requirement met

## All Needed Context

### Service Specifications
```yaml
Service Details:
  name: pattern-mining
  language: Python 3.11+
  runtime: Cloud Run
  port: 8003
  
Architecture:
  pattern: microservice
  communication: REST + gRPC + Events
  data_store: BigQuery (analytics) + Cloud Storage (models)
  service_boundaries: strict
  
Performance:
  slo_response_time: <50ms (scoring), <5min (training)
  slo_availability: 99%
  scaling: 0-100 instances
  memory: 8GB per instance (training), 4GB (inference)
  cpu: 4 vCPU per instance
```

### Technology Stack
```yaml
Primary Language: Python 3.11+
Framework: FastAPI + MLflow
ML/AI: Vertex AI, scikit-learn, TensorFlow, PyTorch
Database: BigQuery (analytics only)
Storage: Google Cloud Storage (models + training data)
Operational Data: Via analysis-engine API
Dependencies:
  - fastapi: 0.104+ # Web framework
  - uvicorn: 0.24+ # ASGI server
  - mlflow: 2.8+ # ML lifecycle management
  - google-cloud-aiplatform: 1.38+ # Vertex AI
  - google-cloud-bigquery: 3.13+ # Analytics database
  - google-cloud-storage: 2.10+ # Model storage
  - scikit-learn: 1.3+ # Traditional ML
  - tensorflow: 2.15+ # Deep learning
  - torch: 2.1+ # PyTorch for advanced models
  - pandas: 2.1+ # Data manipulation
  - numpy: 1.24+ # Numerical computing
  - joblib: 1.3+ # Model serialization
  - redis: 5.0+ # Cache for model artifacts
  - pydantic: 2.5+ # Data validation
  - httpx: 0.25+ # Service-to-service communication
  - google-cloud-pubsub: 2.18+ # Event messaging
  - circuitbreaker: 1.4+ # Circuit breaker pattern
  
Development Tools:
  - pytest: Testing framework
  - black: Code formatting
  - ruff: Linting
  - mypy: Type checking
  - jupyter: Notebook development
```

### Current Codebase Context
```bash
# Reference implementation patterns from:
examples/pattern-mining/
├── pattern_detector.py     # Pattern detection algorithms
├── ml_pipeline.py          # ML training pipeline
├── feature_extraction.py   # AST feature extraction
└── model_training.py       # Model training patterns
```

### Desired Service Structure
```bash
services/pattern-mining/
├── pyproject.toml          # Python dependencies
├── Dockerfile              # Container definition
├── cloudbuild.yaml         # Build configuration
├── notebooks/              # Jupyter notebooks for research
├── src/
│   ├── pattern_mining/
│   │   ├── __init__.py
│   │   ├── main.py         # FastAPI application
│   │   ├── config/         # Configuration management
│   │   │   ├── __init__.py
│   │   │   └── settings.py
│   │   ├── api/            # API endpoints
│   │   │   ├── __init__.py
│   │   │   ├── patterns.py # Pattern endpoints
│   │   │   ├── training.py # Training endpoints
│   │   │   └── health.py   # Health check
│   │   ├── services/       # Business logic
│   │   │   ├── __init__.py
│   │   │   ├── pattern_detector.py # Core detection
│   │   │   ├── feature_extractor.py # Feature extraction
│   │   │   ├── model_trainer.py    # Model training
│   │   │   ├── pattern_classifier.py # Classification
│   │   │   └── recommendation_engine.py # Recommendations
│   │   ├── models/         # Data models
│   │   │   ├── __init__.py
│   │   │   ├── pattern.py  # Pattern models
│   │   │   ├── training.py # Training models
│   │   │   └── features.py # Feature models
│   │   ├── ml/             # ML components
│   │   │   ├── __init__.py
│   │   │   ├── algorithms/ # ML algorithms
│   │   │   ├── pipelines/  # ML pipelines
│   │   │   ├── models/     # Model definitions
│   │   │   └── evaluation/ # Model evaluation
│   │   ├── clients/        # External service clients
│   │   │   ├── __init__.py
│   │   │   ├── vertex_ai.py # Vertex AI client
│   │   │   ├── bigquery.py # BigQuery client
│   │   │   ├── spanner.py  # Spanner client
│   │   │   └── storage.py  # Cloud Storage client
│   │   └── utils/          # Utility functions
│   │       ├── __init__.py
│   │       ├── ast_utils.py # AST processing
│   │       └── metrics.py  # Performance metrics
├── tests/                  # Test files
│   ├── unit/              # Unit tests
│   ├── integration/       # Integration tests
│   ├── ml/                # ML-specific tests
│   └── fixtures/          # Test data
└── docs/
    ├── README.md          # Service documentation
    ├── ml_models.md       # ML model documentation
    └── api.md             # API documentation
```

### Integration Requirements
```yaml
Upstream Dependencies:
  - analysis-engine: AST data and code analysis results
  - query-intelligence: Pattern usage analytics
  
Downstream Consumers:
  - marketplace: Pattern validation and scoring
  - query-intelligence: Pattern-enhanced responses
  - web: Pattern visualization and management
  
Event Subscriptions:
  - analysis.completed: New AST data for pattern detection
  - pattern.validated: Pattern validation results
  
Event Publications:
  - pattern.detected: New pattern discovered
  - pattern.trained: Model training completed
  - pattern.classified: Pattern classification updated
  
External APIs:
  - Vertex AI: Model training and inference
  - BigQuery: Pattern analytics and data warehouse
  - Cloud Storage: Model and dataset storage
  - Spanner: Pattern metadata and relationships
```

### Known Gotchas & Library Quirks
```yaml
Python ML-Specific:
  - Memory management: Large datasets require careful memory handling
  - Model serialization: Use joblib for scikit-learn, native formats for TF/PyTorch
  - Async ML: Most ML operations are CPU-bound, use thread pools
  
Vertex AI:
  - Training jobs: Long-running operations require proper monitoring
  - Model deployment: Handle cold starts and scaling delays
  - Custom models: Ensure proper containerization and dependencies
  
BigQuery:
  - Query costs: Optimize queries to minimize data scanning
  - Streaming inserts: Use batch inserts for better performance
  - Schema evolution: Handle schema changes gracefully
  
ML Pipeline:
  - Feature drift: Monitor feature distributions over time
  - Model degradation: Implement model performance monitoring
  - Data quality: Validate input data quality continuously
  
Performance:
  - Model inference: Cache models in memory for faster predictions
  - Feature extraction: Parallelize AST processing for large codebases
  - Training data: Use efficient data loading and preprocessing
```

## Implementation Blueprint

### Phase 1: Service Foundation
1. **Project Setup**
   ```bash
   mkdir -p services/pattern-mining/src/pattern_mining
   cd services/pattern-mining
   ```

2. **Core Dependencies**
   - FastAPI for web framework
   - MLflow for ML lifecycle management
   - Vertex AI for training and inference
   - BigQuery for data warehouse

3. **Basic Service Structure**
   - FastAPI application setup
   - Health check endpoints
   - Configuration management
   - Logging and metrics

### Phase 2: Pattern Detection Engine
1. **Feature Extraction**
   - AST feature extraction from analysis results
   - Code structure analysis
   - Dependency pattern extraction
   - Architectural pattern detection

2. **Pattern Detection Algorithms**
   - Rule-based pattern detection
   - ML-based pattern classification
   - Similarity-based pattern matching
   - Ensemble pattern detection

3. **Pattern Classification**
   - Design pattern classification
   - Anti-pattern detection
   - Architectural pattern recognition
   - Custom pattern categories

### Phase 3: ML Pipeline Implementation
1. **Data Pipeline**
   - Training data collection from BigQuery
   - Feature engineering and preprocessing
   - Data validation and quality checks
   - Dataset versioning and management

2. **Model Training**
   - Automated model training pipelines
   - Hyperparameter optimization
   - Cross-validation and evaluation
   - Model versioning and registry

3. **Model Deployment**
   - Model serving infrastructure
   - A/B testing framework
   - Model monitoring and alerting
   - Automated retraining triggers

### Phase 4: API Implementation
1. **Pattern API Endpoints**
   ```python
   # POST /patterns/detect - Detect patterns in code
   # GET /patterns/{id} - Get pattern details
   # POST /patterns/classify - Classify detected patterns
   # GET /patterns/recommendations - Get pattern recommendations
   ```

2. **Training API Endpoints**
   ```python
   # POST /training/start - Start model training
   # GET /training/{job_id} - Get training status
   # POST /models/deploy - Deploy trained model
   # GET /models/performance - Get model metrics
   ```

3. **Analytics Endpoints**
   - Pattern usage analytics
   - Model performance metrics
   - Training job monitoring
   - Pattern trend analysis

## Validation Gates

### Development Validation
```bash
# Code Quality
ruff check src/
black --check src/
mypy src/

# Unit Tests
pytest tests/unit/ -v --cov=src

# ML Tests
pytest tests/ml/ -v

# Integration Tests
pytest tests/integration/ -v

# Model Validation
python tests/ml/test_model_accuracy.py
```

### ML Pipeline Validation
```bash
# Feature Extraction Test
python -m pattern_mining.services.feature_extractor --test

# Pattern Detection Test
python -m pattern_mining.services.pattern_detector --validate

# Model Training Test
python -m pattern_mining.services.model_trainer --dry-run

# Model Performance Test
python tests/ml/test_model_performance.py
```

### API Validation
```bash
# Health Check
curl -f http://localhost:8003/health

# Pattern Detection
curl -X POST http://localhost:8003/patterns/detect \
  -H "Content-Type: application/json" \
  -d '{"ast_data": {...}, "code_metrics": {...}}'

# Model Training
curl -X POST http://localhost:8003/training/start \
  -H "Content-Type: application/json" \
  -d '{"dataset_id": "patterns_v1", "model_type": "classification"}'
```

## Success Metrics

### ML Performance Metrics
- **Pattern Detection Accuracy**: >95%
- **Classification Accuracy**: >90%
- **False Positive Rate**: <5%
- **Model Training Time**: <5 minutes

### Service Performance Metrics
- **Pattern Scoring Time**: <50ms
- **Feature Extraction Time**: <2s per file
- **Memory Usage**: <8GB during training
- **API Response Time**: <100ms

### Business Metrics
- **Pattern Coverage**: >80% of common patterns
- **Recommendation Relevance**: >85%
- **Model Improvement Rate**: >5% per month
- **Pattern Discovery Rate**: >10 new patterns/week

## Final Validation Checklist
- [ ] All unit tests pass (>90% coverage)
- [ ] ML pipeline tests pass
- [ ] Model accuracy meets requirements
- [ ] Integration tests pass
- [ ] Performance benchmarks met
- [ ] Vertex AI integration working
- [ ] BigQuery integration functional
- [ ] API documentation complete
- [ ] ML model documentation updated
- [ ] Monitoring configured
- [ ] Deployment successful
- [ ] Pattern detection validated

---

## Implementation Notes

### ML Best Practices
- Implement proper data versioning with DVC or MLflow
- Use cross-validation for all model evaluation
- Monitor model drift and performance degradation
- Implement automated retraining pipelines

### Pattern Detection Strategy
- Start with rule-based detection for known patterns
- Use ML for complex pattern classification
- Implement ensemble methods for better accuracy
- Continuously update pattern definitions

### Performance Optimization
- Cache trained models in memory
- Use batch processing for large datasets
- Implement efficient feature extraction
- Optimize BigQuery queries for cost and performance
