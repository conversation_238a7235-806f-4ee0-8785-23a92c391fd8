# Pattern Recognition System

name: "Pattern Recognition System"
description: |
  Machine learning system for detecting, classifying, and learning code patterns across repositories using Vertex AI and custom models.
  
  Core Principles:
  - **Continuous Learning**: Models improve with user feedback
  - **Multi-Level Detection**: AST, semantic, and behavioral patterns
  - **Language Agnostic**: Works across programming languages
  - **Explainable AI**: Clear reasoning for pattern detection
  - **Privacy Preserving**: No sensitive code in training data

## Goal

Build a comprehensive pattern recognition system that identifies coding patterns, anti-patterns, architectural patterns, and security vulnerabilities across codebases using machine learning models trained on Vertex AI.

## Why

Pattern recognition is core to CCL's value proposition:
- Automatic discovery of best practices
- Anti-pattern and vulnerability detection
- Code quality insights
- Architecture pattern identification
- Knowledge transfer between projects

This system enables:
- Proactive code quality improvements
- Security vulnerability prevention
- Consistent coding standards
- Accelerated onboarding
- Data-driven architecture decisions

## What

### User-Visible Behavior
- Automatic pattern detection during analysis
- Pattern confidence scores and explanations
- Similar pattern suggestions
- Pattern evolution tracking
- Custom pattern training

### Technical Requirements
- [ ] Multi-model ensemble architecture
- [ ] Real-time and batch inference
- [ ] Online learning capabilities
- [ ] Pattern explanation generation
- [ ] Custom pattern definition
- [ ] Vertex AI model deployment
- [ ] A/B testing framework
- [ ] Model monitoring and drift detection

### Success Criteria
- [ ] >90% precision for common patterns
- [ ] <5s inference time per file
- [ ] Support for 20+ languages
- [ ] Explain 95% of detections
- [ ] Handle 1M+ patterns in catalog

## All Needed Context

### Documentation & References
- url: https://cloud.google.com/vertex-ai/docs/training/custom-training
  why: Custom model training on Vertex AI
- url: https://cloud.google.com/vertex-ai/docs/predictions/online-predictions
  why: Online prediction deployment
- url: https://cloud.google.com/vertex-ai/docs/model-monitoring/overview
  why: Model monitoring for drift detection
- file: packages/ccl-core/src/ml/pattern_recognition.py
  why: Current implementation reference

### Pattern Taxonomy

```yaml
Pattern Categories:
  Structural:
    - Singleton
    - Factory
    - Observer
    - Decorator
    - Strategy
    
  Behavioral:
    - Error Handling
    - Resource Management
    - Concurrency Patterns
    - Caching Strategies
    
  Architectural:
    - MVC/MVP/MVVM
    - Microservices
    - Event-Driven
    - Layered Architecture
    
  Security:
    - SQL Injection
    - XSS Vulnerabilities
    - Authentication Patterns
    - Encryption Usage
    
  Performance:
    - N+1 Queries
    - Memory Leaks
    - Inefficient Loops
    - Cache Misses
    
  Quality:
    - Code Duplication
    - Long Methods
    - God Classes
    - Feature Envy
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Balance between false positives and recall
- **CRITICAL**: Training data must be diverse and unbiased
- **GOTCHA**: AST patterns vary significantly between languages
- **GOTCHA**: Model drift happens as coding practices evolve
- **WARNING**: Privacy concerns with training on user code
- **TIP**: Ensemble models work better than single models
- **TIP**: Use active learning for continuous improvement

## Implementation Blueprint

### Core Pattern Recognition Engine

```python
# packages/ccl-core/src/ml/pattern_recognition.py
import tensorflow as tf
from tensorflow.keras import layers, models
import numpy as np
from typing import List, Dict, Optional, Tuple
import ast
from dataclasses import dataclass
from enum import Enum
from google.cloud import aiplatform
from google.cloud import storage
import json
import hashlib
from sklearn.preprocessing import StandardScaler
import logging

logger = logging.getLogger(__name__)

class PatternType(Enum):
    DESIGN_PATTERN = "design_pattern"
    ANTI_PATTERN = "anti_pattern"
    SECURITY_VULNERABILITY = "security_vulnerability"
    PERFORMANCE_ISSUE = "performance_issue"
    ARCHITECTURAL = "architectural"
    QUALITY_ISSUE = "quality_issue"

@dataclass
class PatternDetection:
    pattern_id: str
    pattern_name: str
    pattern_type: PatternType
    confidence: float
    location: Dict[str, any]  # file, start_line, end_line
    explanation: str
    suggestions: List[str]
    examples: List[Dict]
    severity: Optional[str] = None  # For anti-patterns

class PatternRecognitionModel:
    def __init__(self, project_id: str, location: str = "us-central1"):
        self.project_id = project_id
        self.location = location
        self.models = {}
        self.encoders = {}
        self.storage_client = storage.Client()
        
        # Initialize Vertex AI
        aiplatform.init(
            project=project_id,
            location=location,
            staging_bucket=f"gs://{project_id}-ml-staging"
        )
        
    def build_ensemble_model(self) -> Dict[str, tf.keras.Model]:
        """Build ensemble of models for different pattern types"""
        
        models = {}
        
        # Structural Pattern Model (CNN for AST)
        models['structural'] = self._build_cnn_model(
            input_shape=(512, 768),  # AST representation
            num_classes=50  # Number of structural patterns
        )
        
        # Code Quality Model (Dense network)
        models['quality'] = self._build_dense_model(
            input_dim=768,  # Code embedding dimension
            num_classes=30  # Quality issue types
        )
        
        # Security Pattern Model (LSTM for sequential analysis)
        models['security'] = self._build_lstm_model(
            sequence_length=100,
            embedding_dim=768,
            num_classes=25  # Security vulnerability types
        )
        
        # Performance Pattern Model (Attention-based)
        models['performance'] = self._build_attention_model(
            input_dim=768,
            num_heads=8,
            num_classes=20  # Performance issue types
        )
        
        return models
    
    def _build_cnn_model(self, input_shape: Tuple, num_classes: int) -> tf.keras.Model:
        """CNN for structural pattern detection from AST"""
        
        model = models.Sequential([
            layers.Input(shape=input_shape),
            
            # Convolutional blocks
            layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            layers.Conv2D(128, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.MaxPooling2D((2, 2)),
            layers.Dropout(0.25),
            
            layers.Conv2D(256, (3, 3), activation='relu', padding='same'),
            layers.BatchNormalization(),
            layers.GlobalAveragePooling2D(),
            
            # Dense layers
            layers.Dense(512, activation='relu'),
            layers.Dropout(0.5),
            layers.Dense(256, activation='relu'),
            layers.Dropout(0.3),
            
            # Output layer
            layers.Dense(num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy', tf.keras.metrics.TopKCategoricalAccuracy(k=3)]
        )
        
        return model
    
    def _build_dense_model(self, input_dim: int, num_classes: int) -> tf.keras.Model:
        """Dense network for code quality patterns"""
        
        model = models.Sequential([
            layers.Input(shape=(input_dim,)),
            
            layers.Dense(512, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            
            layers.Dense(256, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.3),
            
            layers.Dense(128, activation='relu'),
            layers.BatchNormalization(),
            layers.Dropout(0.2),
            
            layers.Dense(num_classes, activation='sigmoid')  # Multi-label
        ])
        
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='binary_crossentropy',  # Multi-label classification
            metrics=['accuracy', tf.keras.metrics.AUC()]
        )
        
        return model
    
    def _build_lstm_model(self, sequence_length: int, embedding_dim: int, num_classes: int) -> tf.keras.Model:
        """LSTM for security pattern detection"""
        
        model = models.Sequential([
            layers.Input(shape=(sequence_length, embedding_dim)),
            
            layers.LSTM(256, return_sequences=True),
            layers.Dropout(0.3),
            layers.LSTM(128, return_sequences=True),
            layers.Dropout(0.3),
            layers.LSTM(64),
            layers.Dropout(0.2),
            
            layers.Dense(128, activation='relu'),
            layers.Dense(num_classes, activation='softmax')
        ])
        
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy', tf.keras.metrics.Precision(), tf.keras.metrics.Recall()]
        )
        
        return model
    
    def _build_attention_model(self, input_dim: int, num_heads: int, num_classes: int) -> tf.keras.Model:
        """Transformer-based model for performance patterns"""
        
        inputs = layers.Input(shape=(None, input_dim))
        
        # Multi-head attention
        attention_output = layers.MultiHeadAttention(
            num_heads=num_heads,
            key_dim=input_dim // num_heads
        )(inputs, inputs)
        
        # Add & Norm
        attention_output = layers.LayerNormalization()(layers.Add()([inputs, attention_output]))
        
        # Feed forward
        ffn = models.Sequential([
            layers.Dense(512, activation='relu'),
            layers.Dense(input_dim)
        ])
        
        ffn_output = ffn(attention_output)
        output = layers.LayerNormalization()(layers.Add()([attention_output, ffn_output]))
        
        # Global pooling and classification
        output = layers.GlobalAveragePooling1D()(output)
        output = layers.Dense(256, activation='relu')(output)
        output = layers.Dropout(0.3)(output)
        output = layers.Dense(num_classes, activation='softmax')(output)
        
        model = models.Model(inputs=inputs, outputs=output)
        
        model.compile(
            optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
            loss='categorical_crossentropy',
            metrics=['accuracy']
        )
        
        return model
    
    async def detect_patterns(
        self,
        code: str,
        file_path: str,
        language: str,
        ast_representation: Optional[Dict] = None,
        embeddings: Optional[np.ndarray] = None
    ) -> List[PatternDetection]:
        """Detect all patterns in code"""
        
        detections = []
        
        # Prepare features
        features = await self._extract_features(code, language, ast_representation, embeddings)
        
        # Run inference on each model
        predictions = {}
        for model_name, model in self.models.items():
            if model_name in self.endpoints:
                predictions[model_name] = await self._run_inference(
                    self.endpoints[model_name],
                    features[model_name]
                )
        
        # Process predictions into detections
        for model_name, preds in predictions.items():
            pattern_detections = self._process_predictions(
                model_name,
                preds,
                code,
                file_path
            )
            detections.extend(pattern_detections)
        
        # Rank and filter detections
        detections = self._rank_detections(detections)
        
        # Generate explanations
        for detection in detections:
            detection.explanation = await self._generate_explanation(
                detection,
                code,
                features
            )
        
        return detections
    
    async def _extract_features(
        self,
        code: str,
        language: str,
        ast_representation: Optional[Dict],
        embeddings: Optional[np.ndarray]
    ) -> Dict[str, np.ndarray]:
        """Extract features for each model type"""
        
        features = {}
        
        # Structural features from AST
        if ast_representation:
            features['structural'] = self._ast_to_matrix(ast_representation)
        
        # Quality features from embeddings
        if embeddings is not None:
            features['quality'] = embeddings
        else:
            # Generate embeddings if not provided
            from ..embeddings import CodeEmbeddingGenerator
            generator = CodeEmbeddingGenerator()
            embeddings = await generator.generate_embeddings([code])
            features['quality'] = embeddings[0]
        
        # Security features - token sequences
        features['security'] = self._code_to_token_sequence(code, language)
        
        # Performance features - code metrics
        features['performance'] = self._extract_performance_features(code, language)
        
        return features
    
    def _ast_to_matrix(self, ast_data: Dict) -> np.ndarray:
        """Convert AST to matrix representation for CNN"""
        
        # Simplified example - in practice would be more sophisticated
        matrix = np.zeros((512, 768))
        
        def traverse_ast(node, depth=0, position=0):
            if depth >= 512 or position >= 768:
                return position
            
            # Encode node type
            node_type_hash = int(hashlib.md5(
                str(node.get('type', '')).encode()
            ).hexdigest()[:8], 16)
            
            matrix[depth, position] = node_type_hash % 255 / 255.0
            
            # Traverse children
            for child in node.get('children', []):
                position = traverse_ast(child, depth + 1, position + 1)
            
            return position
        
        traverse_ast(ast_data)
        return matrix
    
    def _code_to_token_sequence(self, code: str, language: str) -> np.ndarray:
        """Convert code to token sequence for LSTM"""
        
        # Simplified tokenization - would use language-specific tokenizer
        tokens = code.split()[:100]  # Limit sequence length
        
        # Convert to embeddings (simplified)
        sequence = np.zeros((100, 768))
        for i, token in enumerate(tokens):
            # Hash token to get consistent embedding
            token_hash = int(hashlib.md5(token.encode()).hexdigest()[:16], 16)
            np.random.seed(token_hash % 2**32)
            sequence[i] = np.random.randn(768)
        
        return sequence
    
    def _extract_performance_features(self, code: str, language: str) -> np.ndarray:
        """Extract performance-related features"""
        
        features = []
        
        # Line count
        lines = code.split('\n')
        features.append(len(lines))
        
        # Nesting depth
        max_indent = max(len(line) - len(line.lstrip()) for line in lines if line.strip())
        features.append(max_indent / 4)  # Assuming 4-space indents
        
        # Loop detection
        loop_keywords = ['for', 'while', 'foreach', 'map', 'filter', 'reduce']
        loop_count = sum(keyword in code for keyword in loop_keywords)
        features.append(loop_count)
        
        # Nested loop detection (simplified)
        nested_loops = code.count('for') + code.count('while')
        features.append(nested_loops)
        
        # Database query patterns
        query_patterns = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', '.find(', '.query(']
        query_count = sum(pattern in code for pattern in query_patterns)
        features.append(query_count)
        
        # Pad or truncate to expected dimension
        features = np.array(features)
        if len(features) < 768:
            features = np.pad(features, (0, 768 - len(features)))
        else:
            features = features[:768]
        
        return features.reshape(1, -1, 768)  # Add sequence dimension
    
    def train_on_vertex_ai(
        self,
        dataset_path: str,
        model_type: str,
        hyperparameters: Dict
    ):
        """Train model using Vertex AI"""
        
        # Create custom training job
        job = aiplatform.CustomTrainingJob(
            display_name=f"ccl-pattern-{model_type}-training",
            script_path="training/train_pattern_model.py",
            container_uri="gcr.io/cloud-aiplatform/training/tf-gpu.2-11:latest",
            requirements=[
                "tensorflow==2.11",
                "numpy",
                "scikit-learn",
                "google-cloud-storage"
            ],
            model_serving_container_image_uri="gcr.io/cloud-aiplatform/prediction/tf2-gpu.2-11:latest"
        )
        
        # Define machine specs based on model type
        machine_specs = {
            'structural': {
                "machine_type": "n1-highmem-16",
                "accelerator_type": "NVIDIA_TESLA_V100",
                "accelerator_count": 2
            },
            'quality': {
                "machine_type": "n1-standard-8",
                "accelerator_type": "NVIDIA_TESLA_T4",
                "accelerator_count": 1
            },
            'security': {
                "machine_type": "n1-highmem-8",
                "accelerator_type": "NVIDIA_TESLA_V100",
                "accelerator_count": 1
            },
            'performance': {
                "machine_type": "n1-highmem-16",
                "accelerator_type": "NVIDIA_TESLA_V100",
                "accelerator_count": 2
            }
        }
        
        # Run training
        model = job.run(
            dataset=dataset_path,
            model_display_name=f"ccl-pattern-{model_type}-model",
            args=[
                "--model_type", model_type,
                "--epochs", str(hyperparameters.get('epochs', 50)),
                "--batch_size", str(hyperparameters.get('batch_size', 64)),
                "--learning_rate", str(hyperparameters.get('learning_rate', 0.001)),
                "--validation_split", str(hyperparameters.get('validation_split', 0.2))
            ],
            replica_count=1,
            **machine_specs.get(model_type, machine_specs['quality'])
        )
        
        # Deploy model
        endpoint = model.deploy(
            deployed_model_display_name=f"ccl-pattern-{model_type}-endpoint",
            machine_type="n1-standard-4",
            min_replica_count=1,
            max_replica_count=10,
            accelerator_type="NVIDIA_TESLA_T4" if model_type != 'quality' else None,
            accelerator_count=1 if model_type != 'quality' else 0
        )
        
        self.endpoints[model_type] = endpoint
        return endpoint
    
    async def _run_inference(
        self,
        endpoint: aiplatform.Endpoint,
        features: np.ndarray
    ) -> np.ndarray:
        """Run inference on deployed model"""
        
        # Prepare input
        instances = features.tolist() if features.ndim > 1 else [features.tolist()]
        
        # Get prediction
        prediction = endpoint.predict(instances=instances)
        
        return np.array(prediction.predictions)
    
    def _process_predictions(
        self,
        model_type: str,
        predictions: np.ndarray,
        code: str,
        file_path: str
    ) -> List[PatternDetection]:
        """Convert model predictions to pattern detections"""
        
        detections = []
        pattern_catalog = self._load_pattern_catalog(model_type)
        
        # Get top predictions
        if model_type == 'quality':
            # Multi-label - threshold based
            threshold = 0.5
            detected_indices = np.where(predictions[0] > threshold)[0]
        else:
            # Multi-class - top-k
            k = 3
            detected_indices = np.argsort(predictions[0])[-k:][::-1]
        
        for idx in detected_indices:
            if idx < len(pattern_catalog):
                pattern = pattern_catalog[idx]
                confidence = float(predictions[0][idx])
                
                if confidence > 0.3:  # Minimum confidence threshold
                    detection = PatternDetection(
                        pattern_id=pattern['id'],
                        pattern_name=pattern['name'],
                        pattern_type=PatternType(pattern['type']),
                        confidence=confidence,
                        location={
                            'file': file_path,
                            'start_line': 1,  # Would be refined with AST analysis
                            'end_line': len(code.split('\n'))
                        },
                        explanation="",  # Generated later
                        suggestions=pattern.get('suggestions', []),
                        examples=pattern.get('examples', []),
                        severity=pattern.get('severity')
                    )
                    detections.append(detection)
        
        return detections
    
    def _rank_detections(self, detections: List[PatternDetection]) -> List[PatternDetection]:
        """Rank and filter pattern detections"""
        
        # Remove duplicates
        seen = set()
        unique_detections = []
        for d in detections:
            key = (d.pattern_id, d.location['file'])
            if key not in seen:
                seen.add(key)
                unique_detections.append(d)
        
        # Sort by confidence and severity
        def score_detection(d):
            severity_scores = {
                'critical': 1.0,
                'high': 0.8,
                'medium': 0.5,
                'low': 0.3,
                None: 0.0
            }
            return d.confidence * (1 + severity_scores.get(d.severity, 0))
        
        unique_detections.sort(key=score_detection, reverse=True)
        
        # Limit to top detections per file
        return unique_detections[:20]
    
    async def _generate_explanation(
        self,
        detection: PatternDetection,
        code: str,
        features: Dict
    ) -> str:
        """Generate human-readable explanation for detection"""
        
        # Use Gemini to generate explanation
        from ..gemini_integration import GeminiIntegration, GeminiConfig
        
        gemini = GeminiIntegration(GeminiConfig(
            project_id=self.project_id,
            location=self.location
        ))
        
        prompt = f"""Explain why this code contains the {detection.pattern_name} pattern.

Pattern Type: {detection.pattern_type.value}
Confidence: {detection.confidence:.2%}
Pattern Description: {self._get_pattern_description(detection.pattern_id)}

Code:
```
{code}
```

Provide a clear, concise explanation that:
1. Points to specific code elements that match the pattern
2. Explains why this pattern was detected
3. Describes the implications (positive or negative)
4. Suggests improvements if it's an anti-pattern

Keep the explanation under 150 words."""

        response = await gemini.generate_documentation(
            code=prompt,
            analysis={},
            style="technical"
        )
        
        return response.strip()
    
    def _load_pattern_catalog(self, model_type: str) -> List[Dict]:
        """Load pattern catalog for model type"""
        
        # In production, would load from Cloud Storage
        catalogs = {
            'structural': [
                {'id': 'singleton', 'name': 'Singleton Pattern', 'type': 'design_pattern'},
                {'id': 'factory', 'name': 'Factory Pattern', 'type': 'design_pattern'},
                {'id': 'observer', 'name': 'Observer Pattern', 'type': 'design_pattern'},
                # ... more patterns
            ],
            'quality': [
                {'id': 'long_method', 'name': 'Long Method', 'type': 'quality_issue', 'severity': 'medium'},
                {'id': 'god_class', 'name': 'God Class', 'type': 'quality_issue', 'severity': 'high'},
                {'id': 'duplicate_code', 'name': 'Duplicate Code', 'type': 'quality_issue', 'severity': 'medium'},
                # ... more patterns
            ],
            'security': [
                {'id': 'sql_injection', 'name': 'SQL Injection', 'type': 'security_vulnerability', 'severity': 'critical'},
                {'id': 'xss', 'name': 'Cross-Site Scripting', 'type': 'security_vulnerability', 'severity': 'high'},
                {'id': 'hardcoded_secrets', 'name': 'Hardcoded Secrets', 'type': 'security_vulnerability', 'severity': 'critical'},
                # ... more patterns
            ],
            'performance': [
                {'id': 'n_plus_one', 'name': 'N+1 Query Problem', 'type': 'performance_issue', 'severity': 'high'},
                {'id': 'memory_leak', 'name': 'Memory Leak', 'type': 'performance_issue', 'severity': 'high'},
                {'id': 'inefficient_loop', 'name': 'Inefficient Loop', 'type': 'performance_issue', 'severity': 'medium'},
                # ... more patterns
            ]
        }
        
        return catalogs.get(model_type, [])
```

### Active Learning System

```python
# packages/ccl-core/src/ml/active_learning.py
from typing import List, Dict, Tuple
import numpy as np
from dataclasses import dataclass
from datetime import datetime
import asyncio

@dataclass
class LearningExample:
    code: str
    detected_patterns: List[PatternDetection]
    user_feedback: Dict[str, any]
    timestamp: datetime
    confidence_delta: float

class ActiveLearningSystem:
    def __init__(self, pattern_model: PatternRecognitionModel):
        self.pattern_model = pattern_model
        self.uncertain_examples = []
        self.feedback_buffer = []
        
    async def process_user_feedback(
        self,
        code: str,
        detection: PatternDetection,
        feedback_type: str,  # 'correct', 'incorrect', 'partial'
        corrected_pattern: Optional[str] = None
    ):
        """Process user feedback on pattern detection"""
        
        # Calculate confidence delta
        expected_confidence = detection.confidence
        actual_confidence = {
            'correct': 1.0,
            'incorrect': 0.0,
            'partial': 0.5
        }.get(feedback_type, 0.5)
        
        confidence_delta = abs(expected_confidence - actual_confidence)
        
        # Create learning example
        example = LearningExample(
            code=code,
            detected_patterns=[detection],
            user_feedback={
                'type': feedback_type,
                'corrected_pattern': corrected_pattern,
                'original_confidence': expected_confidence
            },
            timestamp=datetime.utcnow(),
            confidence_delta=confidence_delta
        )
        
        # Add to feedback buffer
        self.feedback_buffer.append(example)
        
        # Trigger retraining if buffer is large enough
        if len(self.feedback_buffer) >= 100:
            await self._trigger_retraining()
    
    async def identify_uncertain_examples(
        self,
        predictions: List[Tuple[str, List[PatternDetection]]]
    ) -> List[str]:
        """Identify examples where model is uncertain"""
        
        uncertain = []
        
        for code, detections in predictions:
            # Check for low confidence
            avg_confidence = np.mean([d.confidence for d in detections]) if detections else 0
            
            # Check for conflicting patterns
            pattern_types = [d.pattern_type for d in detections]
            has_conflicts = len(set(pattern_types)) > 1
            
            # Uncertainty score
            uncertainty = (1 - avg_confidence) * (1.5 if has_conflicts else 1.0)
            
            if uncertainty > 0.5:
                uncertain.append({
                    'code': code,
                    'detections': detections,
                    'uncertainty': uncertainty
                })
        
        # Sort by uncertainty
        uncertain.sort(key=lambda x: x['uncertainty'], reverse=True)
        
        # Store for active learning
        self.uncertain_examples.extend(uncertain[:10])
        
        return [u['code'] for u in uncertain[:5]]
    
    async def _trigger_retraining(self):
        """Trigger model retraining with feedback data"""
        
        # Prepare training data from feedback
        training_examples = []
        
        for example in self.feedback_buffer:
            # Create training instance
            features = await self.pattern_model._extract_features(
                example.code,
                'python',  # Would be detected
                None,
                None
            )
            
            # Adjust labels based on feedback
            if example.user_feedback['type'] == 'correct':
                label = example.detected_patterns[0].pattern_id
            elif example.user_feedback['type'] == 'incorrect':
                label = example.user_feedback.get('corrected_pattern', 'none')
            else:
                continue  # Skip partial feedback for now
            
            training_examples.append({
                'features': features,
                'label': label,
                'weight': example.confidence_delta  # Weight by confidence delta
            })
        
        # Upload to training dataset
        dataset_path = await self._upload_training_data(training_examples)
        
        # Trigger incremental training
        await self._incremental_train(dataset_path)
        
        # Clear buffer
        self.feedback_buffer.clear()
    
    async def _incremental_train(self, dataset_path: str):
        """Perform incremental training on existing model"""
        
        # In production, would trigger Vertex AI pipeline
        logger.info(f"Triggering incremental training with {dataset_path}")
```

### Pattern Explanation Generator

```python
# packages/ccl-core/src/ml/pattern_explainer.py
import numpy as np
from typing import Dict, List, Tuple
import shap
import lime
from lime.lime_text import LimeTextExplainer

class PatternExplainer:
    def __init__(self):
        self.text_explainer = LimeTextExplainer()
        
    async def explain_detection(
        self,
        code: str,
        detection: PatternDetection,
        model: tf.keras.Model,
        features: np.ndarray
    ) -> Dict:
        """Generate detailed explanation for pattern detection"""
        
        explanation = {
            'pattern': detection.pattern_name,
            'confidence': detection.confidence,
            'contributing_factors': [],
            'code_highlights': [],
            'visual_explanation': None
        }
        
        # SHAP explanation for feature importance
        if features.ndim == 2:  # Dense features
            shap_values = await self._compute_shap_values(model, features)
            top_features = self._get_top_features(shap_values)
            explanation['contributing_factors'] = top_features
        
        # LIME explanation for code regions
        if detection.pattern_type in [PatternType.SECURITY_VULNERABILITY, PatternType.QUALITY_ISSUE]:
            lime_exp = await self._compute_lime_explanation(
                code,
                lambda x: self._predict_text_batch(model, x),
                detection
            )
            explanation['code_highlights'] = lime_exp
        
        # Generate visual explanation
        explanation['visual_explanation'] = await self._generate_visual(
            code,
            detection,
            explanation
        )
        
        return explanation
    
    async def _compute_shap_values(
        self,
        model: tf.keras.Model,
        features: np.ndarray
    ) -> np.ndarray:
        """Compute SHAP values for model explanation"""
        
        # Create SHAP explainer
        explainer = shap.DeepExplainer(
            model,
            features[:100]  # Background samples
        )
        
        # Compute SHAP values
        shap_values = explainer.shap_values(features)
        
        return shap_values
    
    def _get_top_features(
        self,
        shap_values: np.ndarray,
        top_k: int = 5
    ) -> List[Dict]:
        """Extract top contributing features"""
        
        # Average absolute SHAP values
        feature_importance = np.abs(shap_values).mean(axis=0)
        
        # Get top features
        top_indices = np.argsort(feature_importance)[-top_k:][::-1]
        
        features = []
        feature_names = self._get_feature_names()
        
        for idx in top_indices:
            features.append({
                'name': feature_names[idx] if idx < len(feature_names) else f'Feature {idx}',
                'importance': float(feature_importance[idx]),
                'contribution': 'positive' if shap_values[0][idx] > 0 else 'negative'
            })
        
        return features
```

## Validation Loop

### Level 1: Model Performance Testing
```python
# Test pattern detection accuracy
async def test_pattern_detection():
    model = PatternRecognitionModel("ccl-platform-prod")
    
    # Test known patterns
    test_cases = [
        {
            'code': '''
            class DatabaseConnection:
                _instance = None
                
                def __new__(cls):
                    if cls._instance is None:
                        cls._instance = super().__new__(cls)
                    return cls._instance
            ''',
            'expected_pattern': 'singleton',
            'min_confidence': 0.8
        },
        {
            'code': '''
            results = []
            for user in users:
                profile = db.query(f"SELECT * FROM profiles WHERE user_id = {user.id}")
                results.append(profile)
            ''',
            'expected_pattern': 'n_plus_one',
            'min_confidence': 0.7
        }
    ]
    
    for test in test_cases:
        detections = await model.detect_patterns(
            test['code'],
            'test.py',
            'python'
        )
        
        found = any(
            d.pattern_id == test['expected_pattern'] and 
            d.confidence >= test['min_confidence']
            for d in detections
        )
        
        assert found, f"Failed to detect {test['expected_pattern']}"
```

### Level 2: Explanation Quality Testing
```python
# Test explanation generation
async def test_explanations():
    explainer = PatternExplainer()
    
    detection = PatternDetection(
        pattern_id='sql_injection',
        pattern_name='SQL Injection Vulnerability',
        pattern_type=PatternType.SECURITY_VULNERABILITY,
        confidence=0.95,
        location={'file': 'test.py', 'start_line': 1, 'end_line': 5},
        explanation='',
        suggestions=[],
        examples=[]
    )
    
    explanation = await explainer.explain_detection(
        code='query = f"SELECT * FROM users WHERE id = {user_input}"',
        detection=detection,
        model=None,  # Mock model
        features=np.random.randn(1, 768)
    )
    
    assert 'contributing_factors' in explanation
    assert 'code_highlights' in explanation
    assert len(explanation['contributing_factors']) > 0
```

### Level 3: End-to-End Integration Testing
```python
# Test complete pattern recognition pipeline
async def test_integration():
    # Initialize system
    model = PatternRecognitionModel("ccl-platform-prod")
    active_learning = ActiveLearningSystem(model)
    
    # Analyze repository
    repo_path = "/test/repo"
    all_detections = []
    
    for file_path in glob.glob(f"{repo_path}/**/*.py", recursive=True):
        with open(file_path, 'r') as f:
            code = f.read()
        
        detections = await model.detect_patterns(
            code=code,
            file_path=file_path,
            language='python'
        )
        
        all_detections.extend(detections)
    
    # Verify detection quality
    assert len(all_detections) > 0
    assert all(0 <= d.confidence <= 1 for d in all_detections)
    assert all(d.explanation for d in all_detections)
    
    # Test active learning
    uncertain = await active_learning.identify_uncertain_examples(
        [(d.location['file'], [d]) for d in all_detections]
    )
    
    assert len(uncertain) <= 5
```

## Final Validation Checklist

- [ ] All model architectures train successfully
- [ ] Inference latency <5s per file
- [ ] Pattern detection precision >90%
- [ ] Explanations generated for all detections
- [ ] Active learning pipeline functional
- [ ] Model monitoring alerts configured
- [ ] A/B testing framework operational
- [ ] Privacy controls enforced
- [ ] Model versioning implemented
- [ ] Performance metrics tracked

## Anti-Patterns to Avoid

1. **DON'T train on private code** - Privacy violations
2. **DON'T ignore false positives** - User trust erosion
3. **DON'T use single model** - Poor coverage
4. **DON'T skip explanations** - Black box problem
5. **DON'T ignore model drift** - Degrading quality
6. **DON'T over-fit to languages** - Limited applicability
7. **DON'T ignore edge cases** - Poor reliability
8. **DON'T skip validation** - Quality issues
9. **DON'T ignore feedback** - Missed improvements
10. **DON'T neglect monitoring** - Silent failures