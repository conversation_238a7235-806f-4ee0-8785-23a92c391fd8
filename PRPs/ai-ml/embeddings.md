# Code Embeddings System

name: "Code Embeddings System"
description: |
  High-performance code embedding generation and similarity search system using state-of-the-art models and Vertex AI Matching Engine.
  
  Core Principles:
  - **Semantic Understanding**: Capture code meaning not just syntax
  - **Language Agnostic**: Universal embeddings across languages
  - **Scalable Search**: Sub-second similarity search at scale
  - **Context Aware**: Include surrounding code context
  - **Privacy Preserving**: No code storage in embeddings

## Goal

Build a comprehensive code embedding system that generates high-quality vector representations of code for similarity search, pattern matching, and semantic understanding across the CCL platform.

## Why

Code embeddings power critical CCL features:
- Semantic code search across repositories
- Similar code detection and deduplication
- Pattern recommendation engine
- Code completion and suggestions
- Cross-language code understanding

This system enables:
- Finding semantically similar code
- Language-agnostic code search
- Efficient pattern matching
- Code quality insights
- Knowledge transfer between projects

## What

### User-Visible Behavior
- Instant semantic code search
- "More like this" code suggestions
- Cross-language similar code finding
- Smart code completion
- Pattern similarity matching

### Technical Requirements
- [ ] Multiple embedding models support
- [ ] Efficient batch processing
- [ ] Real-time embedding generation
- [ ] Vertex AI Matching Engine integration
- [ ] Hybrid search (semantic + keyword)
- [ ] Multi-level embeddings (file, function, snippet)
- [ ] Incremental index updates
- [ ] Cross-language alignment

### Success Criteria
- [ ] <100ms embedding generation per file
- [ ] <50ms similarity search latency
- [ ] >95% relevant results in top-10
- [ ] Support for 1B+ embeddings
- [ ] Handle all major languages

## All Needed Context

### Documentation & References
- url: https://cloud.google.com/vertex-ai/docs/matching-engine/overview
  why: Vertex AI Matching Engine for similarity search
- url: https://huggingface.co/microsoft/codebert-base
  why: CodeBERT model documentation
- url: https://github.com/github/CodeSearchNet
  why: Code search dataset and benchmarks
- file: packages/ccl-core/src/ml/embeddings.py
  why: Current implementation reference

### Embedding Model Options

```yaml
Models:
  CodeBERT:
    dimension: 768
    languages: [python, java, javascript, go, ruby, php]
    context_window: 512
    strengths: [semantic_understanding, cross_language]
    
  GraphCodeBERT:
    dimension: 768
    languages: [python, java, javascript, go, ruby, php]
    context_window: 512
    strengths: [data_flow, structure_aware]
    
  UnixCoder:
    dimension: 768
    languages: [python, java, javascript, go, ruby, php, c++]
    context_window: 1024
    strengths: [generation, understanding]
    
  CodeT5:
    dimension: 768
    languages: [all]
    context_window: 512
    strengths: [summarization, translation]
    
  Custom-CCL:
    dimension: 512
    languages: [all]
    context_window: 2048
    strengths: [domain_specific, fast]
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Normalize embeddings for cosine similarity
- **CRITICAL**: Handle Unicode and special characters properly
- **GOTCHA**: Token limits vary by model
- **GOTCHA**: Batch size affects embedding quality
- **WARNING**: Memory usage scales with batch size
- **TIP**: Use sentence-transformers for easy implementation
- **TIP**: Cache embeddings aggressively

## Implementation Blueprint

### Core Embedding Generator

```python
# packages/ccl-core/src/ml/embeddings.py
from sentence_transformers import SentenceTransformer
import torch
import numpy as np
from typing import List, Dict, Optional, Tuple, Union
import asyncio
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass
import hashlib
from transformers import AutoTokenizer, AutoModel
import logging
from functools import lru_cache

logger = logging.getLogger(__name__)

@dataclass
class EmbeddingConfig:
    model_name: str = "microsoft/codebert-base"
    dimension: int = 768
    max_length: int = 512
    batch_size: int = 32
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    normalize: bool = True
    pooling_strategy: str = "mean"  # mean, max, cls

class CodeEmbeddingGenerator:
    def __init__(self, config: EmbeddingConfig = EmbeddingConfig()):
        self.config = config
        self.device = torch.device(config.device)
        
        # Initialize models
        self._init_models()
        
        # Thread pool for CPU-bound operations
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Embedding cache
        self._cache = {}
        
    def _init_models(self):
        """Initialize embedding models"""
        
        if "codebert" in self.config.model_name.lower():
            # Use transformers for CodeBERT variants
            self.tokenizer = AutoTokenizer.from_pretrained(self.config.model_name)
            self.model = AutoModel.from_pretrained(self.config.model_name)
            self.model.to(self.device)
            self.model.eval()
        else:
            # Use sentence-transformers for other models
            self.model = SentenceTransformer(self.config.model_name)
            self.model.to(self.device)
            self.tokenizer = self.model.tokenizer
    
    async def generate_embeddings(
        self,
        code_snippets: List[str],
        contexts: Optional[List[Dict]] = None,
        cache_key_prefix: Optional[str] = None
    ) -> np.ndarray:
        """Generate embeddings for code snippets with optional context"""
        
        # Check cache
        if cache_key_prefix:
            cached_embeddings = self._get_cached_embeddings(
                code_snippets, 
                cache_key_prefix
            )
            if cached_embeddings is not None:
                return cached_embeddings
        
        # Preprocess code
        processed_snippets = await self._preprocess_code_batch(
            code_snippets,
            contexts
        )
        
        # Generate embeddings in batches
        all_embeddings = []
        
        for i in range(0, len(processed_snippets), self.config.batch_size):
            batch = processed_snippets[i:i + self.config.batch_size]
            
            # Run embedding generation in thread pool
            loop = asyncio.get_event_loop()
            batch_embeddings = await loop.run_in_executor(
                self.executor,
                self._generate_batch_embeddings,
                batch
            )
            
            all_embeddings.append(batch_embeddings)
        
        # Concatenate results
        embeddings = np.vstack(all_embeddings)
        
        # Cache if requested
        if cache_key_prefix:
            self._cache_embeddings(
                code_snippets,
                embeddings,
                cache_key_prefix
            )
        
        return embeddings
    
    async def _preprocess_code_batch(
        self,
        code_snippets: List[str],
        contexts: Optional[List[Dict]]
    ) -> List[str]:
        """Preprocess code for embedding generation"""
        
        processed = []
        
        for i, code in enumerate(code_snippets):
            # Clean code
            cleaned = self._clean_code(code)
            
            # Add context if available
            if contexts and i < len(contexts):
                context = contexts[i]
                
                # Add function/class context
                if 'function_name' in context:
                    cleaned = f"Function {context['function_name']}: {cleaned}"
                elif 'class_name' in context:
                    cleaned = f"Class {context['class_name']}: {cleaned}"
                
                # Add docstring if available
                if 'docstring' in context and context['docstring']:
                    cleaned = f"{context['docstring']}\n{cleaned}"
                
                # Add imports for context
                if 'imports' in context:
                    imports_str = ' '.join(context['imports'][:5])  # Top 5 imports
                    cleaned = f"Uses: {imports_str}\n{cleaned}"
            
            processed.append(cleaned)
        
        return processed
    
    def _clean_code(self, code: str) -> str:
        """Clean code for better embedding quality"""
        
        # Remove excessive whitespace
        lines = code.split('\n')
        
        # Remove empty lines and normalize indentation
        cleaned_lines = []
        min_indent = float('inf')
        
        # Find minimum indentation
        for line in lines:
            if line.strip():
                indent = len(line) - len(line.lstrip())
                min_indent = min(min_indent, indent)
        
        # Remove common indentation
        for line in lines:
            if line.strip():
                cleaned_lines.append(line[min_indent:])
        
        # Truncate if too long
        cleaned = '\n'.join(cleaned_lines)
        if len(cleaned) > self.config.max_length * 4:  # Rough char to token ratio
            cleaned = cleaned[:self.config.max_length * 4] + "..."
        
        return cleaned
    
    def _generate_batch_embeddings(self, batch: List[str]) -> np.ndarray:
        """Generate embeddings for a batch of code"""
        
        with torch.no_grad():
            if hasattr(self.model, 'encode'):
                # Sentence-transformers style
                embeddings = self.model.encode(
                    batch,
                    convert_to_numpy=True,
                    normalize_embeddings=self.config.normalize,
                    show_progress_bar=False
                )
            else:
                # Transformers style (CodeBERT)
                inputs = self.tokenizer(
                    batch,
                    padding=True,
                    truncation=True,
                    max_length=self.config.max_length,
                    return_tensors='pt'
                ).to(self.device)
                
                outputs = self.model(**inputs)
                
                # Apply pooling strategy
                if self.config.pooling_strategy == "cls":
                    embeddings = outputs.last_hidden_state[:, 0, :].cpu().numpy()
                elif self.config.pooling_strategy == "mean":
                    mask = inputs['attention_mask'].unsqueeze(-1).expand(
                        outputs.last_hidden_state.size()
                    ).float()
                    masked_embeddings = outputs.last_hidden_state * mask
                    summed = torch.sum(masked_embeddings, dim=1)
                    counted = torch.clamp(mask.sum(dim=1), min=1e-9)
                    embeddings = (summed / counted).cpu().numpy()
                else:  # max pooling
                    embeddings = torch.max(
                        outputs.last_hidden_state, 
                        dim=1
                    )[0].cpu().numpy()
                
                # Normalize if requested
                if self.config.normalize:
                    embeddings = embeddings / np.linalg.norm(
                        embeddings, axis=1, keepdims=True
                    )
        
        return embeddings
    
    async def generate_hierarchical_embeddings(
        self,
        code: str,
        language: str,
        ast_data: Optional[Dict] = None
    ) -> Dict[str, np.ndarray]:
        """Generate embeddings at multiple granularities"""
        
        embeddings = {}
        
        # File-level embedding
        embeddings['file'] = await self.generate_embeddings(
            [code],
            contexts=[{'language': language}]
        )
        
        # Function-level embeddings
        functions = self._extract_functions(code, language, ast_data)
        if functions:
            function_embeddings = await self.generate_embeddings(
                [f['code'] for f in functions],
                contexts=[{
                    'function_name': f['name'],
                    'docstring': f.get('docstring'),
                    'language': language
                } for f in functions]
            )
            
            embeddings['functions'] = {
                f['name']: function_embeddings[i]
                for i, f in enumerate(functions)
            }
        
        # Class-level embeddings
        classes = self._extract_classes(code, language, ast_data)
        if classes:
            class_embeddings = await self.generate_embeddings(
                [c['code'] for c in classes],
                contexts=[{
                    'class_name': c['name'],
                    'docstring': c.get('docstring'),
                    'language': language
                } for c in classes]
            )
            
            embeddings['classes'] = {
                c['name']: class_embeddings[i]
                for i, c in enumerate(classes)
            }
        
        # Block-level embeddings (chunks)
        blocks = self._create_overlapping_blocks(code)
        if blocks:
            block_embeddings = await self.generate_embeddings(
                blocks,
                contexts=[{'language': language} for _ in blocks]
            )
            embeddings['blocks'] = block_embeddings
        
        return embeddings
    
    def _extract_functions(
        self,
        code: str,
        language: str,
        ast_data: Optional[Dict]
    ) -> List[Dict]:
        """Extract function definitions from code"""
        
        functions = []
        
        if language == 'python':
            # Simple regex-based extraction (in production use AST)
            import re
            
            # Match function definitions
            pattern = r'def\s+(\w+)\s*\([^)]*\):\s*\n((?:\s{4,}.*\n)*)'
            matches = re.finditer(pattern, code, re.MULTILINE)
            
            for match in matches:
                name = match.group(1)
                body = match.group(0)
                
                # Extract docstring if present
                docstring_match = re.search(
                    r'^\s*"""(.*?)"""',
                    match.group(2),
                    re.DOTALL | re.MULTILINE
                )
                
                functions.append({
                    'name': name,
                    'code': body,
                    'docstring': docstring_match.group(1).strip() if docstring_match else None
                })
        
        # Similar patterns for other languages...
        
        return functions
    
    def _extract_classes(
        self,
        code: str,
        language: str,
        ast_data: Optional[Dict]
    ) -> List[Dict]:
        """Extract class definitions from code"""
        
        classes = []
        
        if language == 'python':
            import re
            
            # Match class definitions
            pattern = r'class\s+(\w+)(?:\([^)]*\))?:\s*\n((?:\s{4,}.*\n)*)'
            matches = re.finditer(pattern, code, re.MULTILINE)
            
            for match in matches:
                name = match.group(1)
                body = match.group(0)
                
                # Extract docstring
                docstring_match = re.search(
                    r'^\s*"""(.*?)"""',
                    match.group(2),
                    re.DOTALL | re.MULTILINE
                )
                
                classes.append({
                    'name': name,
                    'code': body,
                    'docstring': docstring_match.group(1).strip() if docstring_match else None
                })
        
        return classes
    
    def _create_overlapping_blocks(
        self,
        code: str,
        block_size: int = 10,
        overlap: int = 5
    ) -> List[str]:
        """Create overlapping code blocks for fine-grained search"""
        
        lines = code.split('\n')
        blocks = []
        
        for i in range(0, len(lines) - block_size + 1, block_size - overlap):
            block = '\n'.join(lines[i:i + block_size])
            if block.strip():  # Skip empty blocks
                blocks.append(block)
        
        return blocks
    
    def _get_cached_embeddings(
        self,
        code_snippets: List[str],
        cache_key_prefix: str
    ) -> Optional[np.ndarray]:
        """Retrieve cached embeddings if available"""
        
        cache_key = self._generate_cache_key(code_snippets, cache_key_prefix)
        return self._cache.get(cache_key)
    
    def _cache_embeddings(
        self,
        code_snippets: List[str],
        embeddings: np.ndarray,
        cache_key_prefix: str
    ):
        """Cache embeddings for reuse"""
        
        cache_key = self._generate_cache_key(code_snippets, cache_key_prefix)
        self._cache[cache_key] = embeddings
        
        # Limit cache size
        if len(self._cache) > 10000:
            # Remove oldest entries (simple FIFO)
            oldest_keys = list(self._cache.keys())[:1000]
            for key in oldest_keys:
                del self._cache[key]
    
    def _generate_cache_key(
        self,
        code_snippets: List[str],
        prefix: str
    ) -> str:
        """Generate cache key for embeddings"""
        
        content = f"{prefix}:{':'.join(code_snippets)}"
        return hashlib.sha256(content.encode()).hexdigest()
```

### Similarity Search Engine

```python
# packages/ccl-core/src/ml/similarity_search.py
from google.cloud import aiplatform
from typing import List, Dict, Optional, Tuple
import numpy as np
import faiss
import asyncio
from dataclasses import dataclass
import json

@dataclass
class SearchResult:
    id: str
    score: float
    metadata: Dict
    embedding: Optional[np.ndarray] = None

class SimilaritySearchEngine:
    def __init__(
        self,
        project_id: str,
        location: str = "us-central1",
        index_name: Optional[str] = None
    ):
        self.project_id = project_id
        self.location = location
        
        # Initialize Vertex AI
        aiplatform.init(project=project_id, location=location)
        
        # Local FAISS index for development/testing
        self.local_index = None
        self.local_metadata = {}
        
        # Vertex AI Matching Engine endpoint
        self.index_endpoint = None
        if index_name:
            self._init_matching_engine(index_name)
    
    def _init_matching_engine(self, index_name: str):
        """Initialize Vertex AI Matching Engine"""
        
        try:
            # Get existing index endpoint
            endpoints = aiplatform.MatchingEngineIndexEndpoint.list(
                filter=f'display_name="{index_name}"'
            )
            
            if endpoints:
                self.index_endpoint = endpoints[0]
                logger.info(f"Using existing index endpoint: {index_name}")
            else:
                # Create new index endpoint
                self.index_endpoint = self._create_index_endpoint(index_name)
        except Exception as e:
            logger.error(f"Failed to initialize Matching Engine: {e}")
            # Fall back to local index
            self._init_local_index()
    
    def _create_index_endpoint(self, index_name: str):
        """Create new Vertex AI Matching Engine index"""
        
        # Create index
        index = aiplatform.MatchingEngineIndex.create(
            display_name=f"{index_name}-index",
            dimensions=768,  # Embedding dimension
            approximate_neighbors_count=100,
            distance_measure_type="DOT_PRODUCT_DISTANCE",
            algorithm_config={
                "tree_ah_config": {
                    "leaf_node_embedding_count": 1000,
                    "leaf_nodes_to_search_percent": 10
                }
            },
            description="Code embedding similarity index"
        )
        
        # Create index endpoint
        index_endpoint = aiplatform.MatchingEngineIndexEndpoint.create(
            display_name=index_name,
            description="Code similarity search endpoint",
            public_endpoint_enabled=True
        )
        
        # Deploy index to endpoint
        index_endpoint.deploy_index(
            index=index,
            deployed_index_id=f"{index_name}-deployed",
            machine_type="n1-standard-16",
            min_replica_count=2,
            max_replica_count=10
        )
        
        return index_endpoint
    
    def _init_local_index(self, dimension: int = 768):
        """Initialize local FAISS index for development"""
        
        # Create FAISS index with inner product (for normalized vectors)
        self.local_index = faiss.IndexFlatIP(dimension)
        
        # Add ID mapping
        self.local_index = faiss.IndexIDMap(self.local_index)
        
        logger.info("Initialized local FAISS index")
    
    async def add_embeddings(
        self,
        embeddings: np.ndarray,
        ids: List[str],
        metadata: List[Dict]
    ):
        """Add embeddings to the index"""
        
        if self.index_endpoint:
            # Add to Vertex AI Matching Engine
            await self._add_to_matching_engine(embeddings, ids, metadata)
        else:
            # Add to local index
            self._add_to_local_index(embeddings, ids, metadata)
    
    async def _add_to_matching_engine(
        self,
        embeddings: np.ndarray,
        ids: List[str],
        metadata: List[Dict]
    ):
        """Add embeddings to Vertex AI Matching Engine"""
        
        # Prepare data in required format
        data_points = []
        
        for i, (embedding, id_, meta) in enumerate(zip(embeddings, ids, metadata)):
            data_point = {
                "datapoint_id": id_,
                "feature_vector": embedding.tolist(),
                "restricts": [
                    {"namespace": "language", "allow": [meta.get("language", "unknown")]},
                    {"namespace": "repository", "allow": [meta.get("repository_id", "")]}
                ],
                "numeric_restricts": [
                    {"namespace": "complexity", "value_float": meta.get("complexity", 0.0)}
                ]
            }
            data_points.append(data_point)
        
        # Batch upload to GCS
        import tempfile
        import jsonlines
        from google.cloud import storage
        
        storage_client = storage.Client()
        bucket = storage_client.bucket(f"{self.project_id}-embeddings")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl') as f:
            with jsonlines.Writer(f) as writer:
                writer.write_all(data_points)
            
            # Upload to GCS
            blob = bucket.blob(f"updates/{int(time.time())}.jsonl")
            blob.upload_from_filename(f.name)
        
        # Trigger index update
        # In production, this would be handled by a Cloud Function
        logger.info(f"Uploaded {len(data_points)} embeddings for indexing")
    
    def _add_to_local_index(
        self,
        embeddings: np.ndarray,
        ids: List[str],
        metadata: List[Dict]
    ):
        """Add embeddings to local FAISS index"""
        
        if self.local_index is None:
            self._init_local_index(embeddings.shape[1])
        
        # Convert string IDs to numeric IDs
        numeric_ids = []
        for id_ in ids:
            numeric_id = hash(id_) % (2**63)  # Ensure positive
            numeric_ids.append(numeric_id)
            self.local_metadata[numeric_id] = {
                'id': id_,
                'metadata': metadata[len(numeric_ids) - 1]
            }
        
        # Add to index
        self.local_index.add_with_ids(embeddings, np.array(numeric_ids))
        
        logger.info(f"Added {len(embeddings)} embeddings to local index")
    
    async def search(
        self,
        query_embedding: np.ndarray,
        k: int = 10,
        filters: Optional[Dict] = None
    ) -> List[SearchResult]:
        """Search for similar embeddings"""
        
        if self.index_endpoint:
            return await self._search_matching_engine(query_embedding, k, filters)
        else:
            return self._search_local_index(query_embedding, k, filters)
    
    async def _search_matching_engine(
        self,
        query_embedding: np.ndarray,
        k: int,
        filters: Optional[Dict]
    ) -> List[SearchResult]:
        """Search using Vertex AI Matching Engine"""
        
        # Prepare query
        query = {
            "deployed_index_id": self.index_endpoint.deployed_indexes[0].id,
            "feature_vector": query_embedding.tolist(),
            "neighbor_count": k
        }
        
        # Add filters if provided
        if filters:
            restricts = []
            
            if 'language' in filters:
                restricts.append({
                    "namespace": "language",
                    "allow": [filters['language']]
                })
            
            if 'repository_id' in filters:
                restricts.append({
                    "namespace": "repository",
                    "allow": [filters['repository_id']]
                })
            
            if restricts:
                query["restricts"] = restricts
            
            if 'min_complexity' in filters:
                query["numeric_restricts"] = [{
                    "namespace": "complexity",
                    "value_float": filters['min_complexity'],
                    "op": "GREATER_EQUAL"
                }]
        
        # Execute search
        response = self.index_endpoint.find_neighbors([query])
        
        # Parse results
        results = []
        for neighbor in response[0]:
            results.append(SearchResult(
                id=neighbor.id,
                score=neighbor.distance,
                metadata={}  # Would fetch from metadata store
            ))
        
        return results
    
    def _search_local_index(
        self,
        query_embedding: np.ndarray,
        k: int,
        filters: Optional[Dict]
    ) -> List[SearchResult]:
        """Search using local FAISS index"""
        
        if self.local_index is None:
            return []
        
        # Ensure query embedding is 2D
        if query_embedding.ndim == 1:
            query_embedding = query_embedding.reshape(1, -1)
        
        # Search
        distances, indices = self.local_index.search(query_embedding, k)
        
        # Convert results
        results = []
        for dist, idx in zip(distances[0], indices[0]):
            if idx != -1 and idx in self.local_metadata:
                meta_info = self.local_metadata[idx]
                
                # Apply filters
                if filters:
                    metadata = meta_info['metadata']
                    if 'language' in filters and metadata.get('language') != filters['language']:
                        continue
                    if 'repository_id' in filters and metadata.get('repository_id') != filters['repository_id']:
                        continue
                
                results.append(SearchResult(
                    id=meta_info['id'],
                    score=float(dist),
                    metadata=meta_info['metadata']
                ))
        
        return results[:k]
    
    async def hybrid_search(
        self,
        query_embedding: np.ndarray,
        query_text: str,
        k: int = 10,
        embedding_weight: float = 0.7
    ) -> List[SearchResult]:
        """Hybrid semantic + keyword search"""
        
        # Semantic search
        semantic_results = await self.search(query_embedding, k * 2)
        
        # Keyword search (would integrate with existing search infrastructure)
        keyword_results = await self._keyword_search(query_text, k * 2)
        
        # Merge results
        merged = {}
        
        for result in semantic_results:
            merged[result.id] = {
                'semantic_score': result.score * embedding_weight,
                'keyword_score': 0,
                'metadata': result.metadata
            }
        
        for result in keyword_results:
            if result.id in merged:
                merged[result.id]['keyword_score'] = result.score * (1 - embedding_weight)
            else:
                merged[result.id] = {
                    'semantic_score': 0,
                    'keyword_score': result.score * (1 - embedding_weight),
                    'metadata': result.metadata
                }
        
        # Calculate combined scores and sort
        final_results = []
        for id_, scores in merged.items():
            combined_score = scores['semantic_score'] + scores['keyword_score']
            final_results.append(SearchResult(
                id=id_,
                score=combined_score,
                metadata=scores['metadata']
            ))
        
        final_results.sort(key=lambda x: x.score, reverse=True)
        
        return final_results[:k]
    
    async def _keyword_search(self, query: str, k: int) -> List[SearchResult]:
        """Placeholder for keyword search integration"""
        # Would integrate with existing search infrastructure
        return []
```

### Cross-Language Alignment

```python
# packages/ccl-core/src/ml/cross_language.py
import torch
import numpy as np
from typing import List, Dict, Tuple
from transformers import AutoTokenizer, AutoModel

class CrossLanguageAlignment:
    def __init__(self, base_model: str = "microsoft/codebert-base"):
        self.tokenizer = AutoTokenizer.from_pretrained(base_model)
        self.model = AutoModel.from_pretrained(base_model)
        
        # Language-specific adapters
        self.language_adapters = {}
        
    def align_embeddings(
        self,
        source_embedding: np.ndarray,
        source_language: str,
        target_language: str
    ) -> np.ndarray:
        """Align embedding from source to target language space"""
        
        if source_language == target_language:
            return source_embedding
        
        # Get alignment matrix
        alignment_key = f"{source_language}->{target_language}"
        
        if alignment_key not in self.language_adapters:
            self.language_adapters[alignment_key] = self._learn_alignment(
                source_language,
                target_language
            )
        
        # Apply alignment
        alignment_matrix = self.language_adapters[alignment_key]
        aligned = source_embedding @ alignment_matrix
        
        # Normalize
        aligned = aligned / np.linalg.norm(aligned)
        
        return aligned
    
    def _learn_alignment(
        self,
        source_lang: str,
        target_lang: str
    ) -> np.ndarray:
        """Learn alignment matrix between language spaces"""
        
        # In production, this would load pre-trained alignment
        # For now, return identity matrix
        return np.eye(768, dtype=np.float32)
    
    async def find_cross_language_similar(
        self,
        code: str,
        source_language: str,
        target_languages: List[str],
        search_engine: SimilaritySearchEngine,
        k: int = 10
    ) -> Dict[str, List[SearchResult]]:
        """Find similar code across languages"""
        
        # Generate embedding for source code
        embedding_gen = CodeEmbeddingGenerator()
        source_embedding = await embedding_gen.generate_embeddings(
            [code],
            contexts=[{'language': source_language}]
        )
        source_embedding = source_embedding[0]
        
        results = {}
        
        for target_lang in target_languages:
            # Align embedding to target language space
            aligned_embedding = self.align_embeddings(
                source_embedding,
                source_language,
                target_lang
            )
            
            # Search in target language
            search_results = await search_engine.search(
                aligned_embedding,
                k=k,
                filters={'language': target_lang}
            )
            
            results[target_lang] = search_results
        
        return results
```

## Validation Loop

### Level 1: Embedding Quality Testing
```python
# Test embedding quality and consistency
async def test_embedding_quality():
    generator = CodeEmbeddingGenerator()
    
    # Test semantic similarity
    similar_codes = [
        "def factorial(n): return 1 if n <= 1 else n * factorial(n-1)",
        "def fact(num): return 1 if num <= 1 else num * fact(num-1)",
    ]
    
    dissimilar_codes = [
        "def factorial(n): return 1 if n <= 1 else n * factorial(n-1)",
        "class DatabaseConnection: def __init__(self): pass",
    ]
    
    # Generate embeddings
    similar_embeddings = await generator.generate_embeddings(similar_codes)
    dissimilar_embeddings = await generator.generate_embeddings(dissimilar_codes)
    
    # Calculate similarities
    similar_score = np.dot(similar_embeddings[0], similar_embeddings[1])
    dissimilar_score = np.dot(dissimilar_embeddings[0], dissimilar_embeddings[1])
    
    # Similar code should have higher similarity
    assert similar_score > dissimilar_score + 0.2
    assert similar_score > 0.8  # High similarity expected
    assert dissimilar_score < 0.5  # Low similarity expected
```

### Level 2: Search Performance Testing
```python
# Test search performance and accuracy
async def test_search_performance():
    search_engine = SimilaritySearchEngine("test-project")
    
    # Add test embeddings
    test_embeddings = np.random.randn(1000, 768)
    test_embeddings = test_embeddings / np.linalg.norm(
        test_embeddings, axis=1, keepdims=True
    )
    
    ids = [f"code_{i}" for i in range(1000)]
    metadata = [{"language": "python", "complexity": i/1000} for i in range(1000)]
    
    await search_engine.add_embeddings(test_embeddings, ids, metadata)
    
    # Test search speed
    query = test_embeddings[0]
    
    start_time = time.time()
    results = await search_engine.search(query, k=10)
    search_time = time.time() - start_time
    
    assert search_time < 0.05  # 50ms latency
    assert len(results) == 10
    assert results[0].id == "code_0"  # Should find itself
```

### Level 3: Cross-Language Testing
```python
# Test cross-language similarity
async def test_cross_language():
    aligner = CrossLanguageAlignment()
    search_engine = SimilaritySearchEngine("test-project")
    
    # Python function
    python_code = """
    def bubble_sort(arr):
        n = len(arr)
        for i in range(n):
            for j in range(0, n-i-1):
                if arr[j] > arr[j+1]:
                    arr[j], arr[j+1] = arr[j+1], arr[j]
        return arr
    """
    
    # Find similar in other languages
    results = await aligner.find_cross_language_similar(
        code=python_code,
        source_language="python",
        target_languages=["java", "javascript"],
        search_engine=search_engine
    )
    
    # Should find sorting algorithms in other languages
    for lang, lang_results in results.items():
        assert len(lang_results) > 0
        # Top results should be sorting-related
```

## Final Validation Checklist

- [ ] All embedding models load successfully
- [ ] Batch processing handles 1000+ snippets
- [ ] Embeddings are normalized correctly
- [ ] Cache improves performance >50%
- [ ] Similarity scores are meaningful
- [ ] Search latency <50ms at scale
- [ ] Cross-language alignment works
- [ ] Hierarchical embeddings generated
- [ ] Memory usage stays bounded
- [ ] Index updates work incrementally

## Anti-Patterns to Avoid

1. **DON'T skip normalization** - Breaks similarity metrics
2. **DON'T embed huge files** - Memory overflow
3. **DON'T ignore token limits** - Truncation issues
4. **DON'T mix embedding spaces** - Incomparable vectors
5. **DON'T forget batching** - Poor performance
6. **DON'T skip caching** - Wasteful recomputation
7. **DON'T ignore language context** - Lower quality
8. **DON'T use one model for all** - Suboptimal results
9. **DON'T forget versioning** - Incompatible embeddings
10. **DON'T neglect monitoring** - Degraded search quality