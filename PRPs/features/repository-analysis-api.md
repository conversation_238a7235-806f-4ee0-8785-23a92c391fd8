# Repository Analysis API PRP

## Purpose
Enable programmatic analysis of code repositories through a high-performance REST API that parses source code into Abstract Syntax Trees (AST), extracts metrics, detects patterns, and generates semantic embeddings for the CCL platform's core intelligence features.

## Goal
Provide a production-ready REST API that can analyze repositories up to 10GB in size, supporting 25+ programming languages, completing analysis of 1M LOC in under 5 minutes, and serving as the foundational data layer for all CCL intelligence features including query processing, pattern detection, and marketplace functionality.

## Business Value
- **Foundational Capability**: Enables all other CCL features (query intelligence, pattern detection, marketplace)
- **Developer Productivity**: Reduces manual code analysis from hours to minutes
- **Scalability**: Supports enterprise-scale repositories with concurrent analysis
- **Revenue Enablement**: Powers premium features and marketplace pattern validation
- **Competitive Advantage**: Sub-5-minute analysis time vs industry standard 30+ minutes
- **Customer Onboarding**: Reduces average onboarding time by 80% via automated repository introspection
- **Cost Reduction**: Cuts manual code-review hours by 60% through instant architectural insights

## Success Criteria

- [ ] **Performance Requirements**
  - [ ] Analyze 1M LOC in <5 minutes (SLO requirement)
  - [ ] API response time <100ms for status queries (p95)
  - [ ] Memory usage <4GB per analysis instance
  - [ ] Concurrent analysis support: 50+ repositories
  - [ ] Handle repositories up to 10GB with streaming processing

- [ ] **Language & Feature Support**
  - [ ] Support 25+ programming languages with Tree-sitter parsers
  - [ ] Pattern detection accuracy >95% for common patterns
  - [ ] WebSocket progress updates every 5 seconds during analysis
  - [ ] Comprehensive error handling with detailed error messages

- [ ] **Reliability & Quality**
  - [ ] Achieve 99.9% uptime with auto-scaling Cloud Run deployment
  - [ ] Test coverage >90% with comprehensive integration tests
  - [ ] Rate limiting and authentication working end-to-end
  - [ ] Webhook delivery system functional with retry logic

- [ ] **API Completeness**
  - [ ] All core endpoints implemented (analyze, status, list, download)
  - [ ] OpenAPI specification complete with examples
  - [ ] Authentication via API keys and OAuth2 flows
  - [ ] Input validation comprehensive with detailed error messages

## All Needed Context

```yaml
# MUST READ – included automatically by PRP
- file: PRPs/services/analysis-engine.md
  why: Core service specification (architecture, performance, tech stack)
- file: examples/analysis-engine/ast_parser.rs
  why: Reference code patterns for async Tree-sitter parsing
- file: PRPs/implementation-guide.md
  why: Rust coding standards, validation loops, testing requirements
- file: PRPs/api/rest-api.md
  why: REST API design patterns, error responses, authentication
- file: TASK.md
  why: Tracks 60 % progress, remaining work, validation commands
- doc: https://tree-sitter.github.io/tree-sitter/
  section: Using Parsers
  critical: Language grammar compilation & safety notes
- doc: https://tokio.rs
  section: Async I/O patterns
  critical: Preventing blocking in async context
- doc: https://docs.rs/git2/latest/git2/
  section: Repo cloning & authentication
  critical: Handling large repos & network timeouts
```

## Documentation

### Internal References
- Service specification: `PRPs/services/analysis-engine.md`
- API patterns: `PRPs/api/rest-api.md`
- Example code: `examples/analysis-engine/ast_parser.rs`
- Implementation guide: `PRPs/implementation-guide.md`

### External Documentation
- Tree-sitter Rust bindings: https://tree-sitter.github.io/tree-sitter/using-parsers
- Tokio async guide: https://tokio.rs/tokio/tutorial
- Git2-rs documentation: https://docs.rs/git2/latest/git2/
- Rayon data-parallelism: https://docs.rs/rayon/latest/rayon/
- Tokei language detection: https://github.com/XAMPPRocky/tokei
- RustLS TLS: https://docs.rs/rustls/latest/rustls/
- Axum framework: https://docs.rs/axum/latest/axum/
- Prometheus client: https://docs.rs/prometheus/latest/prometheus/
- Criterion benchmarking: https://bheisler.github.io/criterion.rs/book/index.html

### Code Examples from Research
```rust
// File-level parsing snippet (from examples/analysis-engine/ast_parser.rs)
let tree = parser.parse(&content, None)
    .ok_or_else(|| anyhow!("Failed to parse file"))?;
```
```rust
// Concurrent directory parsing using Rayon
let results: Vec<_> = files.par_iter()
    .map(|p| rt.block_on(self.parse_file(p)))
    .collect();
```

## Implementation Blueprint

### Phase 1: Core Analysis Engine

```rust
// Main analysis flow with comprehensive error handling and progress reporting
async fn analyze_repository(
    repo_url: &str,
    opts: AnalysisRequest,
    progress_tx: mpsc::Sender<ProgressUpdate>
) -> Result<AnalysisResponse> {
    let analysis_id = generate_analysis_id();
    let start_time = Utc::now();

    // Step 1: Validate and clone repository
    progress_tx.send(ProgressUpdate::new(analysis_id.clone(), 5.0, "Cloning repository")).await?;
    let repo_path = clone_repository_with_auth(repo_url, &opts.branch, &analysis_id).await
        .context("Failed to clone repository")?;

    // Step 2: Detect languages using tokei
    progress_tx.send(ProgressUpdate::new(analysis_id.clone(), 15.0, "Detecting languages")).await?;
    let languages = detect_languages_with_stats(&repo_path)?;
    validate_supported_languages(&languages, &opts.languages)?;

    // Step 3: Collect source files with filtering
    progress_tx.send(ProgressUpdate::new(analysis_id.clone(), 25.0, "Collecting source files")).await?;
    let files = collect_source_files(&repo_path, &opts.include_paths, &opts.exclude_paths)?;

    // Step 4: Parse files in parallel using rayon with progress tracking
    progress_tx.send(ProgressUpdate::new(analysis_id.clone(), 35.0, "Parsing source files")).await?;
    let ast_results = parse_files_parallel(&files, progress_tx.clone(), analysis_id.clone()).await?;
    let (successful_asts, failed_files) = partition_results(ast_results);

    // Step 5: Extract metrics and patterns
    progress_tx.send(ProgressUpdate::new(analysis_id.clone(), 70.0, "Extracting metrics")).await?;
    let metrics = calculate_repository_metrics(&successful_asts);

    progress_tx.send(ProgressUpdate::new(analysis_id.clone(), 85.0, "Detecting patterns")).await?;
    let patterns = detect_patterns_with_confidence(&successful_asts).await?;

    // Step 6: Generate embeddings for semantic search
    progress_tx.send(ProgressUpdate::new(analysis_id.clone(), 95.0, "Generating embeddings")).await?;
    let embeddings = generate_code_embeddings(&successful_asts).await?;

    // Step 7: Store results in Spanner
    let analysis = AnalysisResult {
        id: analysis_id.clone(),
        repository_url: repo_url.to_string(),
        status: AnalysisStatus::Completed,
        started_at: start_time,
        completed_at: Some(Utc::now()),
        metrics,
        patterns,
        embeddings,
        languages,
        failed_files,
        file_count: files.len(),
        success_rate: (successful_asts.len() as f64 / files.len() as f64) * 100.0,
    };

    store_analysis_result(&analysis).await
        .context("Failed to store analysis result")?;

    // Step 8: Send webhook notification if configured
    if let Some(webhook_url) = opts.webhook_url {
        send_webhook_notification(&webhook_url, &analysis).await
            .unwrap_or_else(|e| tracing::warn!("Failed to send webhook: {}", e));
    }

    progress_tx.send(ProgressUpdate::new(analysis_id.clone(), 100.0, "Analysis complete")).await?;

    // Step 9: Cleanup temporary files
    cleanup_repository(&repo_path).await?;

    Ok(AnalysisResponse::from(analysis))
}

// Repository cloning with comprehensive authentication support
async fn clone_repository_with_auth(
    url: &str,
    branch: &Option<String>,
    analysis_id: &str
) -> Result<PathBuf> {
    let clone_path = PathBuf::from(format!("/tmp/ccl-analysis/{}", analysis_id));
    tokio::fs::create_dir_all(&clone_path).await?;

    let mut builder = git2::build::RepoBuilder::new();

    // Set branch if specified
    if let Some(branch) = branch {
        builder.branch(branch);
    }

    // Configure authentication callbacks
    let mut callbacks = git2::RemoteCallbacks::new();
    callbacks.credentials(|url, username_from_url, allowed_types| {
        if allowed_types.contains(git2::CredentialType::SSH_KEY) {
            // Try SSH key authentication
            git2::Cred::ssh_key(
                username_from_url.unwrap_or("git"),
                None,
                Path::new(&format!("{}/.ssh/id_rsa", env::var("HOME").unwrap())),
                None,
            )
        } else if allowed_types.contains(git2::CredentialType::USER_PASS_PLAINTEXT) {
            // Try token authentication for HTTPS
            if let Ok(token) = env::var("GITHUB_TOKEN") {
                git2::Cred::userpass_plaintext("token", &token)
            } else {
                Err(git2::Error::from_str("No authentication method available"))
            }
        } else {
            Err(git2::Error::from_str("Unsupported authentication type"))
        }
    });

    // Set progress callback
    callbacks.progress(|stats| {
        if stats.total_objects() > 0 {
            let progress = (stats.received_objects() as f64 / stats.total_objects() as f64) * 100.0;
            tracing::info!("Clone progress: {:.1}%", progress);
        }
        true
    });

    let mut fetch_options = git2::FetchOptions::new();
    fetch_options.remote_callbacks(callbacks);
    builder.fetch_options(fetch_options);

    // Set timeout for large repositories
    let mut proxy_options = git2::ProxyOptions::new();
    proxy_options.auto();
    fetch_options.proxy_options(proxy_options);

    let repo = builder.clone(url, &clone_path)
        .context("Failed to clone repository")?;

    Ok(clone_path)
}

// Language detection with detailed statistics
fn detect_languages_with_stats(repo_path: &Path) -> Result<HashMap<String, LanguageStats>> {
    let mut languages = tokei::Languages::new();
    let config = tokei::Config {
        treat_doc_strings_as_comments: Some(true),
        ..Default::default()
    };

    languages.get_statistics(&[repo_path], &[], &config);

    let total_lines: usize = languages.iter().map(|(_, lang)| lang.lines()).sum();
    let total_files: usize = languages.iter().map(|(_, lang)| lang.reports.len()).sum();

    let mut result = HashMap::new();
    for (lang_type, language) in languages.iter() {
        let percentage = if total_lines > 0 {
            (language.lines() as f64 / total_lines as f64) * 100.0
        } else {
            0.0
        };

        if percentage > 0.1 { // Only include languages with >0.1% of code
            result.insert(
                lang_type.to_string().to_lowercase(),
                LanguageStats {
                    name: lang_type.to_string(),
                    lines_of_code: language.code,
                    comment_lines: language.comments,
                    blank_lines: language.blanks,
                    total_lines: language.lines(),
                    file_count: language.reports.len(),
                    percentage,
                    files: language.reports.iter()
                        .map(|report| report.name.to_string_lossy().to_string())
                        .collect(),
                }
            );
        }
    }

    Ok(result)
}

// Parallel file parsing with progress tracking
async fn parse_files_parallel(
    files: &[PathBuf],
    progress_tx: mpsc::Sender<ProgressUpdate>,
    analysis_id: String,
) -> Result<Vec<Result<ParsedAst, ParseError>>> {
    let total_files = files.len();
    let progress_counter = Arc::new(AtomicUsize::new(0));
    let parser_pool = Arc::new(AstParserPool::new()?);

    let results: Vec<_> = files
        .par_iter()
        .map(|file_path| {
            let rt = tokio::runtime::Handle::current();
            let progress_tx = progress_tx.clone();
            let analysis_id = analysis_id.clone();
            let progress_counter = progress_counter.clone();
            let parser_pool = parser_pool.clone();

            rt.block_on(async move {
                let result = parse_file_with_timeout(
                    file_path,
                    Duration::from_secs(30),
                    parser_pool.clone()
                ).await;

                // Update progress every 10 files
                let completed = progress_counter.fetch_add(1, Ordering::Relaxed) + 1;
                if completed % 10 == 0 || completed == total_files {
                    let progress = 35.0 + (completed as f64 / total_files as f64) * 35.0; // 35-70%
                    let message = format!("Parsed {}/{} files", completed, total_files);

                    if let Err(e) = progress_tx.send(
                        ProgressUpdate::new(analysis_id.clone(), progress, &message)
                    ).await {
                        tracing::warn!("Failed to send progress update: {}", e);
                    }
                }

                result
            })
        })
        .collect();

    Ok(results)
}
```

### Phase 2: REST API Layer (Axum)

```rust
// Complete Axum router setup with middleware
use axum::{
    extract::{Path, Query, State, WebSocketUpgrade},
    http::{StatusCode, HeaderMap},
    response::{Json, IntoResponse},
    routing::{get, post, delete},
    Router, middleware,
};
use tower_http::{cors::CorsLayer, limit::RequestBodyLimitLayer, trace::TraceLayer};

pub fn create_router() -> Router<AppState> {
    Router::new()
        // Core analysis endpoints
        .route("/api/v1/analysis", post(start_analysis))
        .route("/api/v1/analysis", get(list_analyses))
        .route("/api/v1/analysis/:id", get(get_analysis))
        .route("/api/v1/analysis/:id", delete(cancel_analysis))
        .route("/api/v1/analysis/:id/status", get(get_analysis_status))
        .route("/api/v1/analysis/:id/download", get(download_analysis))
        .route("/api/v1/analysis/:id/metrics", get(get_analysis_metrics))
        .route("/api/v1/analysis/:id/patterns", get(get_analysis_patterns))

        // WebSocket for real-time progress
        .route("/ws/analysis/:id", get(websocket_handler))

        // Health and monitoring
        .route("/health", get(health_check))
        .route("/metrics", get(prometheus_metrics))
        .route("/ready", get(readiness_check))

        // Middleware layers (order matters!)
        .layer(middleware::from_fn(request_id_middleware))
        .layer(middleware::from_fn(auth_middleware))
        .layer(middleware::from_fn(rate_limit_middleware))
        .layer(RequestBodyLimitLayer::new(10 * 1024 * 1024)) // 10MB limit
        .layer(CorsLayer::permissive())
        .layer(TraceLayer::new_for_http())
}

// Analysis endpoint handlers with comprehensive error handling
async fn start_analysis(
    State(app_state): State<AppState>,
    headers: HeaderMap,
    Json(request): Json<AnalysisRequest>,
) -> Result<(StatusCode, Json<AnalysisResponse>), ApiError> {
    // Extract user context from auth middleware
    let user_id = headers.get("x-user-id")
        .and_then(|h| h.to_str().ok())
        .ok_or(ApiError::Unauthorized)?;

    // Validate request thoroughly
    request.validate()
        .map_err(|e| ApiError::BadRequest(format!("Invalid request: {}", e)))?;

    // Check rate limits per user
    app_state.rate_limiter
        .check_limit(user_id)
        .await
        .map_err(|_| ApiError::RateLimited)?;

    // Check if repository is already being analyzed
    if let Some(existing) = app_state.database
        .find_active_analysis(&request.repository_url)
        .await?
    {
        return Ok((StatusCode::ACCEPTED, Json(AnalysisResponse::from(existing))));
    }

    // Generate unique analysis ID
    let analysis_id = generate_analysis_id();

    // Create progress channel for WebSocket updates
    let (progress_tx, progress_rx) = mpsc::channel(100);
    app_state.progress_channels.insert(analysis_id.clone(), progress_rx).await;

    // Create initial analysis record
    let analysis = AnalysisResult {
        id: analysis_id.clone(),
        repository_url: request.repository_url.clone(),
        status: AnalysisStatus::Pending,
        started_at: Utc::now(),
        completed_at: None,
        user_id: user_id.to_string(),
        webhook_url: request.webhook_url.clone(),
        ..Default::default()
    };

    // Store initial record
    app_state.database.store_analysis(&analysis).await?;

    // Spawn background analysis task
    let analysis_future = analyze_repository(
        &request.repository_url,
        request.clone(),
        progress_tx
    );

    let database = app_state.database.clone();
    tokio::spawn(async move {
        match analysis_future.await {
            Ok(result) => {
                if let Err(e) = database.update_analysis(&result).await {
                    tracing::error!("Failed to update analysis result: {}", e);
                }
            }
            Err(e) => {
                tracing::error!("Analysis failed for {}: {}", analysis_id, e);
                let failed_analysis = AnalysisResult {
                    id: analysis_id.clone(),
                    status: AnalysisStatus::Failed,
                    error_message: Some(e.to_string()),
                    completed_at: Some(Utc::now()),
                    ..analysis
                };
                if let Err(e) = database.update_analysis(&failed_analysis).await {
                    tracing::error!("Failed to store failure result: {}", e);
                }
            }
        }
    });

    let response = AnalysisResponse {
        analysis_id: analysis_id.clone(),
        status: AnalysisStatus::Pending,
        created_at: Utc::now(),
        estimated_completion: Utc::now() + Duration::minutes(5),
        repository: RepositoryInfo {
            url: request.repository_url,
            branch: request.branch.unwrap_or_else(|| "main".to_string()),
        },
        webhook_url: request.webhook_url,
        progress_url: Some(format!("/ws/analysis/{}", analysis_id)),
    };

    Ok((StatusCode::CREATED, Json(response)))
}

async fn get_analysis(
    Path(analysis_id): Path<String>,
    State(app_state): State<AppState>,
) -> Result<Json<AnalysisResult>, ApiError> {
    let analysis = app_state
        .database
        .get_analysis(&analysis_id)
        .await?
        .ok_or(ApiError::NotFound)?;

    Ok(Json(analysis))
}

async fn get_analysis_status(
    Path(analysis_id): Path<String>,
    State(app_state): State<AppState>,
) -> Result<Json<AnalysisStatus>, ApiError> {
    let analysis = app_state
        .database
        .get_analysis(&analysis_id)
        .await?
        .ok_or(ApiError::NotFound)?;

    let status_response = AnalysisStatusResponse {
        analysis_id: analysis.id,
        status: analysis.status,
        progress: analysis.progress.unwrap_or(0.0),
        current_stage: analysis.current_stage,
        started_at: analysis.started_at,
        estimated_completion: analysis.estimated_completion,
        error_message: analysis.error_message,
    };

    Ok(Json(status_response))
}

async fn list_analyses(
    Query(params): Query<ListAnalysesParams>,
    State(app_state): State<AppState>,
    headers: HeaderMap,
) -> Result<Json<ListAnalysesResponse>, ApiError> {
    let user_id = headers.get("x-user-id")
        .and_then(|h| h.to_str().ok())
        .ok_or(ApiError::Unauthorized)?;

    let analyses = app_state
        .database
        .list_user_analyses(user_id, &params)
        .await?;

    let response = ListAnalysesResponse {
        analyses,
        total_count: analyses.len(),
        page: params.page.unwrap_or(1),
        per_page: params.per_page.unwrap_or(20),
    };

    Ok(Json(response))
}

async fn cancel_analysis(
    Path(analysis_id): Path<String>,
    State(app_state): State<AppState>,
) -> Result<StatusCode, ApiError> {
    let mut analysis = app_state
        .database
        .get_analysis(&analysis_id)
        .await?
        .ok_or(ApiError::NotFound)?;

    if matches!(analysis.status, AnalysisStatus::Completed | AnalysisStatus::Failed) {
        return Err(ApiError::BadRequest("Analysis already completed".to_string()));
    }

    analysis.status = AnalysisStatus::Cancelled;
    analysis.completed_at = Some(Utc::now());

    app_state.database.update_analysis(&analysis).await?;

    Ok(StatusCode::NO_CONTENT)
}

// WebSocket handler for real-time progress updates
async fn websocket_handler(
    ws: WebSocketUpgrade,
    Path(analysis_id): Path<String>,
    State(app_state): State<AppState>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_websocket(socket, analysis_id, app_state))
}

async fn handle_websocket(
    mut socket: WebSocket,
    analysis_id: String,
    app_state: AppState,
) {
    // Get progress receiver for this analysis
    let mut progress_rx = match app_state.progress_channels.get(&analysis_id).await {
        Some(rx) => rx,
        None => {
            let _ = socket.send(Message::Text(
                serde_json::to_string(&WebSocketError {
                    error: "Analysis not found".to_string(),
                }).unwrap()
            )).await;
            return;
        }
    };

    // Send initial status
    if let Ok(Some(analysis)) = app_state.database.get_analysis(&analysis_id).await {
        let status_msg = serde_json::to_string(&ProgressUpdate {
            analysis_id: analysis.id,
            progress: analysis.progress.unwrap_or(0.0),
            stage: analysis.current_stage.unwrap_or_else(|| "Starting".to_string()),
            timestamp: Utc::now(),
        }).unwrap();

        if socket.send(Message::Text(status_msg)).await.is_err() {
            return;
        }
    }

    // Stream progress updates
    while let Some(update) = progress_rx.recv().await {
        let message = serde_json::to_string(&update).unwrap();
        if socket.send(Message::Text(message)).await.is_err() {
            break;
        }

        // Close connection when analysis is complete
        if update.progress >= 100.0 {
            break;
        }
    }

    // Cleanup
    app_state.progress_channels.remove(&analysis_id).await;
}
```

### Phase 3: Performance Optimisation
- Stream large (>10 MB) files line-by-line to parser to avoid RAM spikes.
- Use Rayon thread-pool equal to `num_cpus::get()` for AST generation.
- Cache repeated analyses for same commit SHA in Spanner (TTL 24 h).
- Gzip compress AST blobs before GCS upload.
- Profile with `tokio-console`, `perf`, and optimise hot loops.

## Data Models

### Input Models

```rust
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisRequest {
    /// Repository URL (HTTPS or SSH)
    pub repository_url: String,

    /// Git branch to analyze (defaults to main/master)
    pub branch: Option<String>,

    /// File patterns to include (glob patterns)
    pub include_patterns: Vec<String>,

    /// File patterns to exclude (glob patterns)
    pub exclude_patterns: Vec<String>,

    /// Analysis depth configuration
    pub analysis_depth: AnalysisDepth,

    /// Languages to analyze (empty = all supported)
    pub languages: Vec<String>,

    /// Webhook URL for completion notification
    pub webhook_url: Option<String>,

    /// Maximum analysis time in seconds (default: 300)
    pub timeout_seconds: Option<u64>,

    /// Enable pattern detection
    pub enable_patterns: bool,

    /// Enable semantic embeddings generation
    pub enable_embeddings: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum AnalysisDepth {
    /// Basic metrics only (fast)
    Shallow,
    /// Full analysis with patterns and embeddings
    Full,
    /// Custom configuration
    Custom {
        include_metrics: bool,
        include_patterns: bool,
        include_embeddings: bool,
        include_dependencies: bool,
    },
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ListAnalysesParams {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub status: Option<AnalysisStatus>,
    pub repository_url: Option<String>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
}

impl AnalysisRequest {
    pub fn validate(&self) -> Result<(), String> {
        if self.repository_url.is_empty() {
            return Err("Repository URL is required".to_string());
        }

        if !self.repository_url.starts_with("https://") && !self.repository_url.starts_with("git@") {
            return Err("Repository URL must be HTTPS or SSH format".to_string());
        }

        if let Some(timeout) = self.timeout_seconds {
            if timeout > 3600 {
                return Err("Timeout cannot exceed 1 hour".to_string());
            }
        }

        Ok(())
    }
}
```

### Output Models

```rust
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisResult {
    /// Unique analysis identifier
    pub id: String,

    /// Repository information
    pub repository_url: String,
    pub branch: String,

    /// Analysis status and timing
    pub status: AnalysisStatus,
    pub started_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub duration_seconds: Option<u64>,

    /// Progress tracking
    pub progress: Option<f64>,
    pub current_stage: Option<String>,
    pub estimated_completion: Option<DateTime<Utc>>,

    /// Results
    pub metrics: Option<RepositoryMetrics>,
    pub patterns: Vec<DetectedPattern>,
    pub languages: HashMap<String, LanguageStats>,
    pub embeddings: Option<CodeEmbeddings>,

    /// Error handling
    pub error_message: Option<String>,
    pub failed_files: Vec<FailedFile>,

    /// Metadata
    pub user_id: String,
    pub webhook_url: Option<String>,
    pub file_count: usize,
    pub success_rate: f64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum AnalysisStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
    Cancelled,
    Timeout,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RepositoryMetrics {
    /// Basic counts
    pub total_files: usize,
    pub total_lines: usize,
    pub lines_of_code: usize,
    pub comment_lines: usize,
    pub blank_lines: usize,

    /// Complexity metrics
    pub cyclomatic_complexity: f64,
    pub cognitive_complexity: f64,
    pub maintainability_index: f64,

    /// Quality metrics
    pub code_duplication_percentage: f64,
    pub test_coverage_estimate: Option<f64>,
    pub documentation_coverage: f64,

    /// Architecture metrics
    pub module_count: usize,
    pub function_count: usize,
    pub class_count: usize,
    pub interface_count: usize,

    /// Dependency metrics
    pub external_dependencies: Vec<Dependency>,
    pub internal_dependencies: Vec<InternalDependency>,
    pub dependency_depth: usize,
    pub circular_dependencies: Vec<CircularDependency>,

    /// Security metrics
    pub potential_vulnerabilities: Vec<SecurityIssue>,
    pub security_score: f64,

    /// Performance indicators
    pub large_files: Vec<LargeFile>,
    pub complex_functions: Vec<ComplexFunction>,
    pub code_smells: Vec<CodeSmell>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct LanguageStats {
    pub name: String,
    pub lines_of_code: usize,
    pub comment_lines: usize,
    pub blank_lines: usize,
    pub total_lines: usize,
    pub file_count: usize,
    pub percentage: f64,
    pub files: Vec<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DetectedPattern {
    pub id: String,
    pub name: String,
    pub description: String,
    pub pattern_type: PatternType,
    pub confidence: f64,
    pub occurrences: Vec<PatternOccurrence>,
    pub severity: PatternSeverity,
    pub recommendation: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum PatternType {
    DesignPattern,
    AntiPattern,
    SecurityPattern,
    PerformancePattern,
    ArchitecturalPattern,
    CodeSmell,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum PatternSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct PatternOccurrence {
    pub file_path: String,
    pub line_start: usize,
    pub line_end: usize,
    pub code_snippet: String,
    pub context: HashMap<String, String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct FailedFile {
    pub file_path: String,
    pub error_message: String,
    pub error_type: FileErrorType,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum FileErrorType {
    ParseError,
    UnsupportedLanguage,
    FileTooLarge,
    PermissionDenied,
    BinaryFile,
    Timeout,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ProgressUpdate {
    pub analysis_id: String,
    pub progress: f64,
    pub stage: String,
    pub message: Option<String>,
    pub timestamp: DateTime<Utc>,
    pub files_processed: Option<usize>,
    pub total_files: Option<usize>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AnalysisResponse {
    pub analysis_id: String,
    pub status: AnalysisStatus,
    pub created_at: DateTime<Utc>,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub repository: RepositoryInfo,
    pub webhook_url: Option<String>,
    pub progress_url: Option<String>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RepositoryInfo {
    pub url: String,
    pub branch: String,
    pub commit_sha: Option<String>,
    pub size_bytes: Option<u64>,
}
```

## API Endpoints

### POST /api/v1/analysis

**Purpose**: Initiates repository analysis

**Request**:
```json
{
  "repository_url": "https://github.com/user/repo.git",
  "branch": "main",
  "include_patterns": ["src/**/*.rs", "lib/**/*.rs"],
  "exclude_patterns": ["target/**", "**/*.test.rs"],
  "analysis_depth": "Full",
  "languages": ["rust", "javascript"],
  "webhook_url": "https://api.example.com/webhooks/analysis",
  "timeout_seconds": 300,
  "enable_patterns": true,
  "enable_embeddings": true
}
```

**Responses**:
- `201 Created` - Analysis started successfully
  ```json
  {
    "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "Pending",
    "created_at": "2024-01-15T10:30:00Z",
    "estimated_completion": "2024-01-15T10:35:00Z",
    "repository": {
      "url": "https://github.com/user/repo.git",
      "branch": "main"
    },
    "progress_url": "/ws/analysis/550e8400-e29b-41d4-a716-446655440000"
  }
  ```
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Missing or invalid authentication
- `403 Forbidden` - Insufficient permissions
- `409 Conflict` - Repository already being analyzed
- `429 Too Many Requests` - Rate limit exceeded

### GET /api/v1/analysis/{id}

**Purpose**: Retrieves complete analysis result

**Query Parameters**:
- `format` (optional): Response format (`json`, `html`, `zip`)
- `include` (optional): Sections to include (`metrics`, `patterns`, `embeddings`)

**Responses**:
- `200 OK` - Analysis completed
  ```json
  {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "repository_url": "https://github.com/user/repo.git",
    "status": "Completed",
    "started_at": "2024-01-15T10:30:00Z",
    "completed_at": "2024-01-15T10:34:30Z",
    "duration_seconds": 270,
    "metrics": {
      "total_files": 1250,
      "total_lines": 45000,
      "lines_of_code": 32000,
      "cyclomatic_complexity": 2.3,
      "maintainability_index": 78.5
    },
    "patterns": [
      {
        "id": "singleton-pattern-001",
        "name": "Singleton Pattern",
        "confidence": 0.95,
        "occurrences": [
          {
            "file_path": "src/config.rs",
            "line_start": 15,
            "line_end": 45,
            "code_snippet": "impl Config { fn instance() -> &'static Config { ... } }"
          }
        ]
      }
    ],
    "languages": {
      "rust": {
        "name": "Rust",
        "lines_of_code": 28000,
        "percentage": 87.5,
        "file_count": 180
      }
    },
    "success_rate": 98.4
  }
  ```
- `202 Accepted` - Analysis in progress
- `404 Not Found` - Analysis not found
- `410 Gone` - Analysis expired

### GET /api/v1/analysis/{id}/status

**Purpose**: Retrieves analysis status and progress

**Responses**:
- `200 OK` - Status retrieved
  ```json
  {
    "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "InProgress",
    "progress": 65.5,
    "current_stage": "Detecting patterns",
    "started_at": "2024-01-15T10:30:00Z",
    "estimated_completion": "2024-01-15T10:35:00Z",
    "files_processed": 820,
    "total_files": 1250
  }
  ```

### GET /api/v1/analysis

**Purpose**: Lists user's analyses with filtering

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status
- `repository_url` (optional): Filter by repository
- `created_after` (optional): Filter by creation date
- `created_before` (optional): Filter by creation date

**Responses**:
- `200 OK` - List retrieved
  ```json
  {
    "analyses": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "repository_url": "https://github.com/user/repo.git",
        "status": "Completed",
        "started_at": "2024-01-15T10:30:00Z",
        "completed_at": "2024-01-15T10:34:30Z"
      }
    ],
    "total_count": 1,
    "page": 1,
    "per_page": 20
  }
  ```

### DELETE /api/v1/analysis/{id}

**Purpose**: Cancels running analysis or deletes completed analysis

**Responses**:
- `204 No Content` - Analysis cancelled/deleted
- `404 Not Found` - Analysis not found
- `409 Conflict` - Cannot cancel completed analysis

### GET /api/v1/analysis/{id}/download

**Purpose**: Downloads analysis results as archive

**Query Parameters**:
- `format` (optional): Archive format (`zip`, `tar.gz`)
- `include` (optional): What to include (`results`, `raw_data`, `reports`)

**Responses**:
- `200 OK` - File download
- `302 Found` - Redirect to signed URL (for large files)
- `404 Not Found` - Analysis not found

### WebSocket /ws/analysis/{id}

**Purpose**: Real-time progress updates

**Message Format**:
```json
{
  "analysis_id": "550e8400-e29b-41d4-a716-446655440000",
  "progress": 42.5,
  "stage": "Parsing files",
  "message": "Processing src/main.rs",
  "timestamp": "2024-01-15T10:32:15Z",
  "files_processed": 425,
  "total_files": 1000
}
```

**Connection Lifecycle**:
1. Client connects to `/ws/analysis/{id}`
2. Server sends current status immediately
3. Server streams progress updates every 5 seconds
4. Connection closes when analysis completes or fails
5. Client can reconnect at any time

### Health and Monitoring Endpoints

#### GET /health
**Purpose**: Basic health check
**Response**: `200 OK` with `{"status": "healthy"}`

#### GET /ready
**Purpose**: Readiness check (includes database connectivity)
**Response**: `200 OK` when ready, `503 Service Unavailable` when not ready

#### GET /metrics
**Purpose**: Prometheus metrics endpoint
**Response**: Prometheus format metrics

## Task List

### Phase 1: Foundation (Weeks 1-2)

1. [ ] **Project Setup**
   - [ ] Set up Cargo workspace with analysis-engine crate
   - [ ] Configure dependencies (tokio, axum, git2, tree-sitter, etc.)
   - [ ] Set up development environment with Docker
   - [ ] Configure CI/CD pipeline with GitHub Actions

2. [ ] **Core Data Models**
   - [ ] Implement all request/response structs with serde
   - [ ] Add comprehensive validation for AnalysisRequest
   - [ ] Create error types with thiserror
   - [ ] Add database schema for Spanner

3. [ ] **Repository Operations**
   - [ ] Implement git2-rs repository cloning with authentication
   - [ ] Add support for SSH keys and personal access tokens
   - [ ] Handle large repository cloning with progress tracking
   - [ ] Implement repository cleanup and temporary file management

### Phase 2: Analysis Engine (Weeks 3-4)

4. [ ] **Language Detection**
   - [ ] Integrate tokei for language detection
   - [ ] Add language statistics calculation
   - [ ] Implement file filtering by language and patterns
   - [ ] Add support for custom language configurations

5. [ ] **AST Parsing**
   - [ ] Set up Tree-sitter parser pool for thread safety
   - [ ] Implement parsers for 25+ languages
   - [ ] Add streaming parser for large files (>10MB)
   - [ ] Create parallel parsing with rayon and progress tracking

6. [ ] **Metrics Extraction**
   - [ ] Implement basic metrics (LOC, complexity, etc.)
   - [ ] Add advanced metrics (maintainability index, duplication)
   - [ ] Create dependency analysis module
   - [ ] Implement security vulnerability detection

### Phase 3: Pattern Detection (Week 5)

7. [ ] **Pattern Recognition**
   - [ ] Implement design pattern detection algorithms
   - [ ] Add anti-pattern and code smell detection
   - [ ] Create pattern confidence scoring system
   - [ ] Add pattern occurrence tracking with line numbers

8. [ ] **Embeddings Generation**
   - [ ] Integrate with Vertex AI for code embeddings
   - [ ] Implement semantic similarity calculations
   - [ ] Add embedding storage and retrieval
   - [ ] Create embedding-based search capabilities

### Phase 4: REST API (Week 6)

9. [ ] **API Framework**
   - [ ] Set up Axum router with all endpoints
   - [ ] Implement request/response serialization
   - [ ] Add comprehensive error handling and mapping
   - [ ] Create OpenAPI specification

10. [ ] **Authentication & Authorization**
    - [ ] Implement API key authentication middleware
    - [ ] Add OAuth2 bearer token support
    - [ ] Create user context extraction
    - [ ] Add role-based access control

11. [ ] **Rate Limiting & Security**
    - [ ] Implement token bucket rate limiting
    - [ ] Add request size limits and timeouts
    - [ ] Create security headers middleware
    - [ ] Add input sanitization and validation

### Phase 5: Real-time Features (Week 7)

12. [ ] **WebSocket Support**
    - [ ] Implement WebSocket handler for progress updates
    - [ ] Create progress channel management
    - [ ] Add connection lifecycle management
    - [ ] Implement reconnection handling

13. [ ] **Progress Tracking**
    - [ ] Create progress update system with stages
    - [ ] Add file-level progress tracking
    - [ ] Implement ETA calculation
    - [ ] Add progress persistence for reconnections

### Phase 6: Data Layer (Week 8)

14. [ ] **Database Integration**
    - [ ] Implement Spanner client with connection pooling
    - [ ] Create analysis result storage and retrieval
    - [ ] Add query optimization for list operations
    - [ ] Implement data retention policies

15. [ ] **Caching Layer**
    - [ ] Implement Redis caching for analysis results
    - [ ] Add cache invalidation strategies
    - [ ] Create cache warming for popular repositories
    - [ ] Add cache metrics and monitoring

### Phase 7: Performance & Optimization (Week 9)

16. [ ] **Performance Optimization**
    - [ ] Profile and optimize hot paths
    - [ ] Implement memory usage optimization
    - [ ] Add connection pooling for external services
    - [ ] Optimize parallel processing parameters

17. [ ] **Monitoring & Observability**
    - [ ] Add Prometheus metrics collection
    - [ ] Implement distributed tracing with OpenTelemetry
    - [ ] Create health check endpoints
    - [ ] Add structured logging with correlation IDs

### Phase 8: Testing & Quality (Week 10)

18. [ ] **Unit Testing**
    - [ ] Write unit tests for all core modules
    - [ ] Add property-based testing for parsers
    - [ ] Create mock implementations for external services
    - [ ] Achieve >90% test coverage

19. [ ] **Integration Testing**
    - [ ] Create end-to-end API tests
    - [ ] Add database integration tests
    - [ ] Test WebSocket functionality
    - [ ] Add performance benchmarks

20. [ ] **Load Testing**
    - [ ] Create load testing scripts
    - [ ] Test concurrent analysis scenarios
    - [ ] Validate rate limiting behavior
    - [ ] Test system under stress conditions

### Phase 9: Documentation & Deployment (Week 11)

21. [ ] **Documentation**
    - [ ] Generate OpenAPI documentation
    - [ ] Create API usage examples
    - [ ] Write deployment guides
    - [ ] Add troubleshooting documentation

22. [ ] **Deployment Preparation**
    - [ ] Create Docker images with multi-stage builds
    - [ ] Set up Cloud Run deployment configuration
    - [ ] Configure auto-scaling parameters
    - [ ] Add deployment health checks

### Phase 10: Production Readiness (Week 12)

23. [ ] **Security Hardening**
    - [ ] Run security audit with cargo-audit
    - [ ] Implement secrets management
    - [ ] Add vulnerability scanning to CI
    - [ ] Create security incident response procedures

24. [ ] **Production Deployment**
    - [ ] Deploy to staging environment
    - [ ] Run full system tests
    - [ ] Deploy to production with blue-green strategy
    - [ ] Monitor system performance and errors

## Validation Loops

### Level 1: Lint & Formatting

```bash
cargo clippy --all-targets --all-features -- -D warnings
cargo fmt -- --check
```

### Level 2: Unit Tests

```bash
cargo test --all
cargo tarpaulin --out Html  # Coverage report
```

### Level 3: Integration & Performance

```bash
# Compile & run benchmarks
cargo bench --bench analysis_bench

# End-to-end performance on a large repo
./scripts/load-test-analysis.sh https://github.com/torvalds/linux

# API smoke test
curl -f http://localhost:8001/health
```


### Syntax & Compilation
```bash
cd services/analysis-engine
cargo check
cargo clippy -- -D warnings
cargo fmt -- --check
```

### Testing
```bash
cargo test --all
cargo tarpaulin --out Html
```

### Performance
```bash
cargo bench --bench analysis_bench
./scripts/load-test-analysis.sh https://github.com/torvalds/linux
```

### Security
```bash
cargo audit
cargo-deny check
```

## Error Handling Strategy
- `thiserror`-backed domain errors (`RepoError`, `ParseError`, `AnalysisError`).
- Map to HTTP codes via Axum `IntoResponse`.
- Include `request_id` header for tracing.

## Progress Reporting Mechanism
- In-memory `DashMap<Uuid, Progress>` shared via `Arc`.
- Emit `Progress` struct updates every 5 s over WebSocket & SSE.

## Caching Strategy
- Key = `sha256(repo_url + commit)`
- Store `Analysis` JSON in Spanner; AST blobs in GCS.
- TTL 24 h, LRU in-memory for hot repos.

## Rate Limiting Approach
- Token bucket per API key using Redis or in-process `Governor`.
- Default 1 000 requests/min, configurable tiers.

## Language-Specific Parsers Configuration
```toml
[tree-sitter-languages]
rust = "*"
python = "*"
go = "*"
javascript = "*"
typescript = "*"
java = "*"
# … add others
```

## Anti-Patterns to Avoid
- ❌ Loading entire repository into RAM
- ❌ Synchronous Git network calls
- ❌ Sequential file parsing
- ❌ Ignoring binary / vendored directories
- ❌ Unbounded concurrency spawning

## Known Gotchas

```yaml
Tree-sitter:
  - Grammars must be cross-compiled for target CPU architecture
  - Parser state is not thread-safe – create per-thread clones

git2-rs:
  - Network operations may hang on large monorepos; set 30 s timeout & retries
  - SSH auth requires proper key permissions on Cloud Run instances

Tokio:
  - Avoid blocking calls (e.g., std::fs::read) inside async tasks; use tokio-fs variants

Spanner:
  - DML mutation limit 20 k – batch writes of metrics

GCS:
  - Objects >50 MB require resumable uploads; enable chunked upload
```
- Tree-sitter grammars must match target arch (cross-compile in CI)
- git2 network timeouts on large monorepos – set 30 s timeout & retries.
- Python significant whitespace can break naive AST walkers – normalise.

## Performance Considerations
- Stream >10 MB files
- Parallel parse chunks with Rayon
- Pre-allocate metric structs to reduce reallocs
- Use `tokio::fs::File::open` with `BufReader` for IO
- Cache identical file hashes across commits
- Use Cloud CDN for result downloads

## Additional Implementation Details

### Error Handling Strategy

```rust
use thiserror::Error;
use axum::{response::IntoResponse, http::StatusCode, Json};

#[derive(Error, Debug)]
pub enum AnalysisError {
    #[error("Repository not found or inaccessible: {url}")]
    RepositoryNotFound { url: String },

    #[error("Unsupported language: {language}")]
    UnsupportedLanguage { language: String },

    #[error("Parse error in {file}: {message}")]
    ParseError { file: String, message: String },

    #[error("Analysis timeout after {seconds}s")]
    Timeout { seconds: u64 },

    #[error("Repository too large: {size_gb}GB (max: 10GB)")]
    RepositoryTooLarge { size_gb: f64 },

    #[error("Rate limit exceeded: {limit} requests per {window}")]
    RateLimited { limit: u32, window: String },

    #[error("Database error: {0}")]
    Database(#[from] DatabaseError),

    #[error("Git operation failed: {0}")]
    Git(#[from] git2::Error),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
}

impl IntoResponse for AnalysisError {
    fn into_response(self) -> axum::response::Response {
        let (status, error_message) = match self {
            AnalysisError::RepositoryNotFound { .. } => (StatusCode::NOT_FOUND, self.to_string()),
            AnalysisError::UnsupportedLanguage { .. } => (StatusCode::BAD_REQUEST, self.to_string()),
            AnalysisError::ParseError { .. } => (StatusCode::UNPROCESSABLE_ENTITY, self.to_string()),
            AnalysisError::Timeout { .. } => (StatusCode::REQUEST_TIMEOUT, self.to_string()),
            AnalysisError::RepositoryTooLarge { .. } => (StatusCode::PAYLOAD_TOO_LARGE, self.to_string()),
            AnalysisError::RateLimited { .. } => (StatusCode::TOO_MANY_REQUESTS, self.to_string()),
            _ => (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error".to_string()),
        };

        let body = Json(serde_json::json!({
            "error": error_message,
            "timestamp": chrono::Utc::now(),
            "request_id": uuid::Uuid::new_v4(),
        }));

        (status, body).into_response()
    }
}
```

### Caching Strategy Implementation

```rust
use redis::{Client, Commands, Connection};
use serde_json;

pub struct AnalysisCache {
    redis: Client,
    ttl_seconds: u64,
}

impl AnalysisCache {
    pub fn new(redis_url: &str, ttl_seconds: u64) -> Result<Self> {
        let redis = Client::open(redis_url)?;
        Ok(Self { redis, ttl_seconds })
    }

    pub async fn get_analysis(&self, repo_url: &str, commit_sha: &str) -> Result<Option<AnalysisResult>> {
        let mut conn = self.redis.get_connection()?;
        let key = self.cache_key(repo_url, commit_sha);

        let cached: Option<String> = conn.get(&key)?;
        match cached {
            Some(json) => Ok(Some(serde_json::from_str(&json)?)),
            None => Ok(None),
        }
    }

    pub async fn store_analysis(&self, analysis: &AnalysisResult, commit_sha: &str) -> Result<()> {
        let mut conn = self.redis.get_connection()?;
        let key = self.cache_key(&analysis.repository_url, commit_sha);
        let json = serde_json::to_string(analysis)?;

        conn.setex(&key, self.ttl_seconds, json)?;
        Ok(())
    }

    fn cache_key(&self, repo_url: &str, commit_sha: &str) -> String {
        format!("analysis:{}:{}",
            sha256::digest(repo_url.as_bytes()),
            commit_sha
        )
    }
}
```

### Rate Limiting Implementation

```rust
use governor::{Quota, RateLimiter, state::{InMemoryState, NotKeyed}};
use std::num::NonZeroU32;

pub struct ApiRateLimiter {
    limiter: RateLimiter<NotKeyed, InMemoryState, DefaultClock>,
}

impl ApiRateLimiter {
    pub fn new(requests_per_minute: u32) -> Self {
        let quota = Quota::per_minute(NonZeroU32::new(requests_per_minute).unwrap());
        let limiter = RateLimiter::direct(quota);
        Self { limiter }
    }

    pub async fn check_limit(&self, _user_id: &str) -> Result<(), RateLimitError> {
        match self.limiter.check() {
            Ok(_) => Ok(()),
            Err(_) => Err(RateLimitError::Exceeded),
        }
    }
}
```

### Performance Benchmarks

```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_analysis_pipeline(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();

    c.bench_function("small_repo_analysis", |b| {
        b.to_async(&rt).iter(|| async {
            let request = AnalysisRequest {
                repository_url: "https://github.com/rust-lang/hello-world.git".to_string(),
                analysis_depth: AnalysisDepth::Shallow,
                ..Default::default()
            };

            black_box(analyze_repository_mock(&request).await)
        })
    });

    c.bench_function("large_repo_analysis", |b| {
        b.to_async(&rt).iter(|| async {
            let request = AnalysisRequest {
                repository_url: "https://github.com/rust-lang/rust.git".to_string(),
                analysis_depth: AnalysisDepth::Full,
                ..Default::default()
            };

            black_box(analyze_repository_mock(&request).await)
        })
    });
}

criterion_group!(benches, benchmark_analysis_pipeline);
criterion_main!(benches);
```

## Confidence Score: 8/10

### High Confidence Areas (9-10/10):
- **Rust async patterns and tokio integration**: Well-established patterns with extensive documentation
- **Axum web framework usage**: Mature framework with clear patterns for REST APIs
- **Tree-sitter AST parsing**: Proven technology with good Rust bindings
- **Git operations with git2-rs**: Stable library with comprehensive functionality
- **Database integration with Spanner**: Google Cloud SDK is well-documented
- **Authentication and rate limiting**: Standard patterns with existing libraries

### Medium Confidence Areas (7-8/10):
- **Pattern detection accuracy**: Requires domain expertise and iterative refinement
- **Performance optimization for large repositories**: Needs real-world testing and tuning
- **WebSocket connection management**: Requires careful handling of connection lifecycle
- **Caching strategy effectiveness**: Needs monitoring and adjustment based on usage patterns
- **Multi-language parser configuration**: Some languages may have edge cases

### Low Confidence Areas (5-6/10):
- **Semantic embeddings generation**: Depends on Vertex AI API stability and performance
- **Security vulnerability detection**: Requires extensive rule sets and regular updates
- **Cross-platform deployment**: May need platform-specific optimizations
- **Memory usage optimization for very large repositories**: Requires extensive profiling

### Mitigation Strategies:
1. **Incremental rollout**: Start with popular languages and repositories, expand gradually
2. **Comprehensive monitoring**: Implement detailed metrics and alerting for all components
3. **Fallback mechanisms**: Graceful degradation when external services are unavailable
4. **Regular benchmarking**: Continuous performance testing with real-world repositories
5. **Community feedback**: Engage with users to identify and fix edge cases
6. **Automated testing**: Extensive test suite covering various repository types and sizes
7. **Documentation and examples**: Clear documentation for troubleshooting and optimization

### Success Validation:
- All validation commands pass consistently
- Performance targets met in staging environment
- Security audit completed with no critical issues
- Load testing demonstrates system can handle expected traffic
- Integration tests cover all major use cases
- Documentation is complete and accurate
