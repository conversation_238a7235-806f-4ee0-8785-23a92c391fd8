name: "Development Environment Setup"
description: |
  Complete setup of local development environment for CCL platform development including
  all required tools, services, configuration, and development workflows.

---

## Goal
Establish a complete, consistent development environment that enables productive CCL platform development with all necessary tools, services, and configurations properly set up and integrated.

## Why
- **Developer Productivity**: Streamlined setup reduces onboarding time
- **Consistency**: All developers work with identical environments
- **Quality Assurance**: Proper tooling ensures code quality from the start
- **Rapid Development**: Optimized environment enables fast iteration

## What
A comprehensive development environment that:
- Installs and configures all required development tools
- Sets up local versions of all CCL services
- Configures development databases and external services
- Establishes code quality tools and pre-commit hooks
- Provides debugging and testing capabilities
- Integrates with cloud services for development

### Success Criteria
- [ ] All required tools installed and configured
- [ ] Local development environment running
- [ ] All CCL services can be built and run locally
- [ ] Database connections working
- [ ] Code quality tools operational
- [ ] Testing framework functional
- [ ] Debugging capabilities available
- [ ] Cloud service integration working

## All Needed Context

### Required Tools and Versions
```yaml
Core Development Tools:
  - Google Cloud SDK: 450.0.0+
  - Docker: 24.0.0+
  - Docker Compose: 2.20.0+
  - Node.js: 20.0.0+ (LTS)
  - Python: 3.11+
  - Rust: 1.75+
  - Go: 1.21+
  - Git: 2.40+
  
Development IDEs:
  - VS Code: Latest (recommended)
  - JetBrains IDEs: Latest
  - Vim/Neovim: With language servers
  
Additional Tools:
  - Terraform: 1.6+
  - kubectl: Latest
  - Postman: Latest (API testing)
  - Chrome DevTools: For frontend debugging
```

### Local Service Architecture
```yaml
Local Services:
  analysis-engine:
    port: 8001
    language: Rust
    dependencies: [spanner-emulator, storage-emulator]
    
  query-intelligence:
    port: 8002
    language: Python
    dependencies: [spanner-emulator, firestore-emulator, redis]
    
  pattern-mining:
    port: 8003
    language: Python
    dependencies: [bigquery-emulator, spanner-emulator, storage-emulator]
    
  marketplace:
    port: 8004
    language: Go
    dependencies: [spanner-emulator, firestore-emulator]
    
  collaboration:
    port: 8005
    language: TypeScript
    dependencies: [firestore-emulator, redis]
    
  web:
    port: 3000
    language: TypeScript/React
    dependencies: [all-services]
    
Supporting Services:
  spanner-emulator:
    port: 9010
    purpose: Local Spanner database
    
  firestore-emulator:
    port: 8080
    purpose: Local Firestore database
    
  bigquery-emulator:
    port: 9050
    purpose: Local BigQuery
    
  redis:
    port: 6379
    purpose: Local Redis cache
    
  storage-emulator:
    port: 9023
    purpose: Local Cloud Storage
```

### Current Codebase Context
```bash
# Reference setup patterns from:
docs/guides/developer-guide.md  # Complete development guide
examples/development/
├── docker-compose.dev.yml      # Local service orchestration
├── setup-dev-env.sh           # Environment setup script
├── Makefile                   # Development commands
└── .env.example               # Environment variables template
```

### Development Workflow
```yaml
Git Workflow:
  main_branch: main
  feature_branches: feature/description
  hotfix_branches: hotfix/description
  release_branches: release/version
  
Commit Convention:
  format: "type(scope): description"
  types: [feat, fix, docs, style, refactor, test, chore]
  
Code Review Process:
  - All changes require PR
  - Minimum 2 approvals required
  - All tests must pass
  - Code quality checks must pass
  
Testing Strategy:
  unit_tests: >90% coverage required
  integration_tests: All service interactions tested
  e2e_tests: Critical user journeys covered
  performance_tests: SLO validation
```

## Implementation Blueprint

### Phase 1: Tool Installation
1. **Install Core Development Tools**
   ```bash
   # macOS installation
   brew install google-cloud-sdk docker docker-compose node python rust go git terraform kubectl
   
   # Ubuntu installation
   sudo apt update
   sudo apt install -y curl wget git
   
   # Install Google Cloud SDK
   curl https://sdk.cloud.google.com | bash
   exec -l $SHELL
   
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sh get-docker.sh
   
   # Install Node.js via nvm
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   nvm install 20
   nvm use 20
   
   # Install Python via pyenv
   curl https://pyenv.run | bash
   pyenv install 3.11
   pyenv global 3.11
   
   # Install Rust
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
   
   # Install Go
   wget https://go.dev/dl/go1.21.linux-amd64.tar.gz
   sudo tar -C /usr/local -xzf go1.21.linux-amd64.tar.gz
   ```

2. **Configure Development Tools**
   ```bash
   # Configure Git
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   git config --global init.defaultBranch main
   
   # Configure Google Cloud SDK
   gcloud auth login
   gcloud auth application-default login
   gcloud config set project ccl-platform-dev
   
   # Configure Docker
   sudo usermod -aG docker $USER
   newgrp docker
   ```

### Phase 2: Project Setup
1. **Clone and Setup Repository**
   ```bash
   # Clone repository
   git clone https://github.com/ccl-platform/ccl.git
   cd ccl
   
   # Install development dependencies
   make install-dev
   
   # Setup environment variables
   cp .env.example .env.local
   # Edit .env.local with your configuration
   
   # Install Git hooks
   make install-hooks
   ```

2. **Language-Specific Setup**
   ```bash
   # Rust setup
   cd services/analysis-engine
   cargo build
   
   # Python setup
   cd services/query-intelligence
   python -m venv venv
   source venv/bin/activate
   pip install -e ".[dev]"
   
   # Go setup
   cd services/marketplace
   go mod download
   
   # Node.js setup
   cd services/collaboration
   npm install
   
   cd services/web
   npm install
   ```

### Phase 3: Local Services Setup
1. **Start Supporting Services**
   ```bash
   # Start all emulators and supporting services
   docker-compose -f docker-compose.dev.yml up -d
   
   # Verify services are running
   docker-compose ps
   
   # Check service health
   curl http://localhost:9010/v1/projects/test-project/instances  # Spanner
   curl http://localhost:8080  # Firestore
   redis-cli ping  # Redis
   ```

2. **Initialize Databases**
   ```bash
   # Initialize Spanner database
   gcloud spanner instances create test-instance \
     --config=emulator-config \
     --description="Test instance" \
     --nodes=1
   
   gcloud spanner databases create ccl-dev \
     --instance=test-instance
   
   # Initialize Firestore
   gcloud firestore databases create --region=us-central1
   
   # Run database migrations
   make migrate-dev
   ```

### Phase 4: Development Workflow Setup
1. **Code Quality Tools**
   ```bash
   # Install pre-commit hooks
   pip install pre-commit
   pre-commit install
   
   # Configure linting tools
   # Rust
   rustup component add clippy rustfmt
   
   # Python
   pip install black ruff mypy
   
   # Go
   go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
   
   # TypeScript
   npm install -g eslint prettier typescript
   ```

2. **Testing Framework Setup**
   ```bash
   # Run all tests to verify setup
   make test
   
   # Run service-specific tests
   make test-rust
   make test-python
   make test-go
   make test-typescript
   ```

3. **Development Scripts**
   ```bash
   # Create development shortcuts
   cat >> ~/.bashrc << 'EOF'
   # CCL Development Aliases
   alias ccl-dev="cd ~/ccl && make dev"
   alias ccl-test="cd ~/ccl && make test"
   alias ccl-lint="cd ~/ccl && make lint"
   alias ccl-logs="docker-compose -f docker-compose.dev.yml logs -f"
   EOF
   ```

### Phase 5: IDE Configuration
1. **VS Code Setup**
   ```json
   // .vscode/settings.json
   {
     "rust-analyzer.cargo.target": "x86_64-unknown-linux-gnu",
     "python.defaultInterpreterPath": "./venv/bin/python",
     "go.gopath": "~/go",
     "typescript.preferences.importModuleSpecifier": "relative",
     "editor.formatOnSave": true,
     "editor.codeActionsOnSave": {
       "source.fixAll.eslint": true
     }
   }
   ```

2. **Recommended Extensions**
   ```json
   // .vscode/extensions.json
   {
     "recommendations": [
       "rust-lang.rust-analyzer",
       "ms-python.python",
       "golang.go",
       "bradlc.vscode-tailwindcss",
       "esbenp.prettier-vscode",
       "ms-vscode.vscode-typescript-next",
       "googlecloudtools.cloudcode"
     ]
   }
   ```

## Validation Gates

### Tool Installation Validation
```bash
# Verify all tools are installed
gcloud version
docker --version
docker-compose --version
node --version
python --version
rustc --version
go version
git --version
terraform --version
kubectl version --client
```

### Service Connectivity Validation
```bash
# Test local service connections
curl http://localhost:8001/health  # Analysis Engine
curl http://localhost:8002/health  # Query Intelligence
curl http://localhost:8003/health  # Pattern Mining
curl http://localhost:8004/health  # Marketplace
curl http://localhost:8005/health  # Collaboration
curl http://localhost:3000         # Web UI

# Test database connections
gcloud spanner databases list --instance=test-instance
curl http://localhost:8080/v1/projects/test-project/databases
redis-cli ping
```

### Development Workflow Validation
```bash
# Test build process
make build

# Test code quality tools
make lint
make format

# Test testing framework
make test

# Test Git hooks
git add .
git commit -m "test: validate development environment"
```

### Cloud Integration Validation
```bash
# Test Google Cloud authentication
gcloud auth list
gcloud projects list

# Test service account access
gcloud iam service-accounts list

# Test API access
gcloud services list --enabled
```

## Success Metrics

### Setup Completion Metrics
- **Tool Installation**: 100% required tools installed
- **Service Startup**: All services start successfully
- **Database Connectivity**: All databases accessible
- **Code Quality**: All quality tools operational

### Development Productivity Metrics
- **Build Time**: <5 minutes for full build
- **Test Execution**: <10 minutes for full test suite
- **Service Startup**: <2 minutes for all services
- **Hot Reload**: <5 seconds for code changes

### Quality Metrics
- **Code Coverage**: >90% across all services
- **Linting**: 100% compliance with style guides
- **Security Scan**: No high/critical vulnerabilities
- **Performance**: Local services meet SLO requirements

## Final Validation Checklist
- [ ] All required tools installed and configured
- [ ] Repository cloned and dependencies installed
- [ ] Local services running and healthy
- [ ] Database connections working
- [ ] Code quality tools operational
- [ ] Testing framework functional
- [ ] Git hooks installed and working
- [ ] IDE configured with extensions
- [ ] Cloud services accessible
- [ ] Development workflow validated
- [ ] Documentation complete
- [ ] Team onboarding guide created

---

## Implementation Notes

### Platform-Specific Considerations
- **macOS**: Use Homebrew for package management
- **Linux**: Use distribution package managers
- **Windows**: Use WSL2 for Linux compatibility
- **Docker**: Ensure sufficient memory allocation (8GB+)

### Performance Optimization
- Use SSD storage for better I/O performance
- Allocate sufficient RAM (16GB+ recommended)
- Configure Docker resource limits appropriately
- Use local caching for dependencies

### Troubleshooting Common Issues
- Port conflicts: Check for conflicting services
- Permission issues: Ensure proper user permissions
- Memory issues: Monitor Docker memory usage
- Network issues: Check firewall and proxy settings

### Security Considerations
- Use development-only credentials
- Keep development environment isolated
- Regular security updates for all tools
- Proper secret management for development
