# Language Parser Framework

name: "Language Parser Framework"
description: |
  WebAssembly-based plugin system for parsing and analyzing code across 20+ programming languages with unified AST representation.
  
  Core Principles:
  - **Language Agnostic**: Unified interface across all languages
  - **High Performance**: WebAssembly for near-native speed
  - **Extensible**: Easy to add new language support
  - **Sandboxed**: Secure execution environment
  - **Streaming**: Handle large files efficiently

## Goal

Build a comprehensive language parser framework using WebAssembly plugins that can parse, analyze, and extract insights from code in any programming language while maintaining consistent AST structure and high performance.

## Why

The parser framework is fundamental to CCL's multi-language support:
- Consistent code analysis across languages
- Fast and secure parsing with WebAssembly
- Easy addition of new language support
- Unified AST for cross-language features
- Enables all downstream analysis features

This framework provides:
- Sub-second parsing for large files
- Memory-safe execution
- Language-specific intelligence
- Incremental parsing support
- Error recovery and partial parsing

## What

### User-Visible Behavior
- Support for 20+ languages out of the box
- Fast parsing of large codebases
- Accurate syntax highlighting
- Code navigation features
- Language-specific insights

### Technical Requirements
- [ ] WebAssembly plugin architecture
- [ ] Unified AST schema
- [ ] Streaming parser interface
- [ ] Error recovery mechanisms
- [ ] Incremental parsing support
- [ ] Language detection system
- [ ] Parser performance monitoring
- [ ] Plugin hot-reloading

### Success Criteria
- [ ] Parse 1MB file in <100ms
- [ ] Support all top 20 languages
- [ ] <10MB memory per parser
- [ ] 99.9% parsing accuracy
- [ ] Graceful error handling

## All Needed Context

### Documentation & References
- url: https://webassembly.org/docs/use-cases/
  why: WebAssembly use cases and best practices
- url: https://tree-sitter.github.io/tree-sitter/
  why: Tree-sitter parsing library documentation
- url: https://microsoft.github.io/language-server-protocol/
  why: Language Server Protocol for IDE features
- file: packages/ccl-core/src/parser/plugin.rs
  why: Current Rust implementation

### Language Support Matrix

```yaml
Tier 1 (Full Support):
  - name: JavaScript/TypeScript
    parser: tree-sitter-typescript
    lsp: typescript-language-server
    features: [ast, types, symbols, diagnostics]
    
  - name: Python
    parser: tree-sitter-python
    lsp: pylsp
    features: [ast, types, symbols, diagnostics]
    
  - name: Java
    parser: tree-sitter-java
    lsp: eclipse.jdt.ls
    features: [ast, types, symbols, diagnostics]
    
  - name: Go
    parser: tree-sitter-go
    lsp: gopls
    features: [ast, types, symbols, diagnostics]
    
  - name: Rust
    parser: tree-sitter-rust
    lsp: rust-analyzer
    features: [ast, types, symbols, diagnostics]

Tier 2 (Core Support):
  - C/C++
  - C#
  - Ruby
  - PHP
  - Swift
  - Kotlin
  - Scala

Tier 3 (Basic Support):
  - Dart
  - Elixir
  - Haskell
  - Lua
  - R
  - Julia
  - Objective-C
  - SQL
```

### Known Gotchas & Library Quirks
- **CRITICAL**: WASM memory limits require streaming for large files
- **CRITICAL**: Parser state must be thread-safe
- **GOTCHA**: Different AST structures between parsers
- **GOTCHA**: Unicode handling varies by language
- **WARNING**: Memory leaks in long-running parsers
- **TIP**: Use tree-sitter for consistent parsing
- **TIP**: Cache parsed ASTs aggressively

## Implementation Blueprint

### Core Parser Plugin System

```rust
// packages/ccl-core/src/parser/plugin.rs
use wasmtime::*;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use anyhow::Result;
use tokio::sync::RwLock;
use std::sync::Arc;

#[derive(Debug, Serialize, Deserialize)]
pub struct ParseRequest {
    pub code: String,
    pub language: String,
    pub options: ParseOptions,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ParseOptions {
    pub include_comments: bool,
    pub include_positions: bool,
    pub error_recovery: bool,
    pub timeout_ms: Option<u64>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ParseResult {
    pub ast: AstNode,
    pub errors: Vec<ParseError>,
    pub metrics: ParseMetrics,
    pub language_specific: serde_json::Value,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AstNode {
    pub node_type: String,
    pub children: Vec<AstNode>,
    pub properties: HashMap<String, serde_json::Value>,
    pub position: Option<Position>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Position {
    pub start_line: usize,
    pub start_column: usize,
    pub end_line: usize,
    pub end_column: usize,
    pub start_byte: usize,
    pub end_byte: usize,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ParseError {
    pub message: String,
    pub position: Position,
    pub severity: ErrorSeverity,
    pub error_code: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Error,
    Warning,
    Info,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ParseMetrics {
    pub parse_time_ms: f64,
    pub node_count: usize,
    pub max_depth: usize,
    pub memory_used_bytes: usize,
}

pub struct ParserPlugin {
    engine: Engine,
    module: Module,
    store: Store<PluginState>,
    instance: Instance,
}

struct PluginState {
    memory: Option<Memory>,
    alloc_fn: Option<TypedFunc<i32, i32>>,
    parse_fn: Option<TypedFunc<(i32, i32), i32>>,
    free_fn: Option<TypedFunc<(i32, i32), ()>>,
}

pub struct ParserPluginManager {
    plugins: Arc<RwLock<HashMap<String, Arc<ParserPlugin>>>>,
    config: PluginConfig,
}

#[derive(Clone)]
pub struct PluginConfig {
    pub plugin_dir: String,
    pub max_memory_pages: u32,
    pub enable_caching: bool,
    pub cache_size_mb: usize,
}

impl ParserPluginManager {
    pub fn new(config: PluginConfig) -> Self {
        Self {
            plugins: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }
    
    pub async fn load_plugin(&self, language: &str) -> Result<()> {
        let wasm_path = format!("{}/parser-{}.wasm", self.config.plugin_dir, language);
        let wasm_bytes = tokio::fs::read(&wasm_path).await?;
        
        let engine = Engine::default();
        let module = Module::new(&engine, &wasm_bytes)?;
        
        let mut store = Store::new(&engine, PluginState {
            memory: None,
            alloc_fn: None,
            parse_fn: None,
            free_fn: None,
        });
        
        // Create instance with imports
        let imports = self.create_imports(&mut store);
        let instance = Instance::new(&mut store, &module, &imports)?;
        
        // Get exported functions
        let alloc_fn = instance.get_typed_func::<i32, i32>(&mut store, "alloc")?;
        let parse_fn = instance.get_typed_func::<(i32, i32), i32>(&mut store, "parse")?;
        let free_fn = instance.get_typed_func::<(i32, i32), ()>(&mut store, "free")?;
        
        // Get memory export
        let memory = instance
            .get_memory(&mut store, "memory")
            .ok_or_else(|| anyhow::anyhow!("Memory export not found"))?;
        
        // Update store state
        store.data_mut().memory = Some(memory);
        store.data_mut().alloc_fn = Some(alloc_fn);
        store.data_mut().parse_fn = Some(parse_fn);
        store.data_mut().free_fn = Some(free_fn);
        
        let plugin = Arc::new(ParserPlugin {
            engine,
            module,
            store,
            instance,
        });
        
        self.plugins.write().await.insert(language.to_string(), plugin);
        
        Ok(())
    }
    
    pub async fn parse(
        &self,
        language: &str,
        code: &str,
        options: ParseOptions,
    ) -> Result<ParseResult> {
        // Get or load plugin
        let plugin = {
            let plugins = self.plugins.read().await;
            plugins.get(language).cloned()
        };
        
        let plugin = match plugin {
            Some(p) => p,
            None => {
                self.load_plugin(language).await?;
                self.plugins.read().await.get(language).unwrap().clone()
            }
        };
        
        // Execute parsing in plugin
        plugin.parse(code, options).await
    }
    
    fn create_imports(&self, store: &mut Store<PluginState>) -> Vec<Extern> {
        vec![
            // Add any host functions needed by plugins
            Func::wrap(store, |_caller: Caller<'_, PluginState>, ptr: i32, len: i32| {
                // Log function for debugging
                println!("Plugin log: ptr={}, len={}", ptr, len);
            }).into(),
        ]
    }
}

impl ParserPlugin {
    async fn parse(&self, code: &str, options: ParseOptions) -> Result<ParseResult> {
        // Serialize request
        let request = ParseRequest {
            code: code.to_string(),
            language: String::new(), // Set by plugin
            options,
        };
        
        let request_json = serde_json::to_string(&request)?;
        let request_bytes = request_json.as_bytes();
        
        // Allocate memory in WASM
        let ptr = self.store.data().alloc_fn.unwrap().call(&mut self.store, request_bytes.len() as i32)?;
        
        // Write request to WASM memory
        let memory = self.store.data().memory.unwrap();
        memory.write(&mut self.store, ptr as usize, request_bytes)?;
        
        // Call parse function
        let result_ptr = self.store.data().parse_fn.unwrap().call(
            &mut self.store,
            (ptr, request_bytes.len() as i32),
        )?;
        
        // Read result size (first 4 bytes at result_ptr)
        let mut size_bytes = [0u8; 4];
        memory.read(&self.store, result_ptr as usize, &mut size_bytes)?;
        let result_size = u32::from_le_bytes(size_bytes) as usize;
        
        // Read result data
        let mut result_bytes = vec![0u8; result_size];
        memory.read(&self.store, (result_ptr + 4) as usize, &mut result_bytes)?;
        
        // Free allocated memory
        self.store.data().free_fn.unwrap().call(
            &mut self.store,
            (ptr, request_bytes.len() as i32),
        )?;
        
        self.store.data().free_fn.unwrap().call(
            &mut self.store,
            (result_ptr, (result_size + 4) as i32),
        )?;
        
        // Deserialize result
        let result: ParseResult = serde_json::from_slice(&result_bytes)?;
        
        Ok(result)
    }
}
```

### Language-Specific Parser Implementation

```rust
// plugins/parser-rust/src/lib.rs
use tree_sitter_rust;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;

#[no_mangle]
pub extern "C" fn alloc(size: i32) -> i32 {
    let layout = std::alloc::Layout::from_size_align(size as usize, 8).unwrap();
    let ptr = unsafe { std::alloc::alloc(layout) };
    ptr as i32
}

#[no_mangle]
pub extern "C" fn free(ptr: i32, size: i32) {
    let layout = std::alloc::Layout::from_size_align(size as usize, 8).unwrap();
    unsafe {
        std::alloc::dealloc(ptr as *mut u8, layout);
    }
}

#[no_mangle]
pub extern "C" fn parse(ptr: i32, len: i32) -> i32 {
    let input = unsafe {
        let slice = std::slice::from_raw_parts(ptr as *const u8, len as usize);
        std::str::from_utf8_unchecked(slice)
    };
    
    let request: ParseRequest = match serde_json::from_str(input) {
        Ok(r) => r,
        Err(e) => return encode_error(&format!("Failed to parse request: {}", e)),
    };
    
    let result = match parse_rust_code(&request) {
        Ok(r) => r,
        Err(e) => return encode_error(&format!("Parse error: {}", e)),
    };
    
    encode_result(&result)
}

fn parse_rust_code(request: &ParseRequest) -> Result<ParseResult, Box<dyn std::error::Error>> {
    let start_time = std::time::Instant::now();
    
    // Initialize tree-sitter parser
    let mut parser = tree_sitter::Parser::new();
    parser.set_language(tree_sitter_rust::language())?;
    
    // Parse code
    let tree = parser.parse(&request.code, None)
        .ok_or("Failed to parse code")?;
    
    let root_node = tree.root_node();
    
    // Convert tree-sitter AST to our unified AST
    let ast = convert_node_to_ast(&root_node, &request.code, request.options.include_positions);
    
    // Collect parse errors
    let mut errors = Vec::new();
    collect_errors(&root_node, &request.code, &mut errors);
    
    // Calculate metrics
    let metrics = ParseMetrics {
        parse_time_ms: start_time.elapsed().as_millis() as f64,
        node_count: count_nodes(&root_node),
        max_depth: calculate_max_depth(&root_node),
        memory_used_bytes: 0, // Would calculate actual memory usage
    };
    
    // Extract Rust-specific information
    let mut language_specific = serde_json::Map::new();
    
    // Find main function
    if let Some(main_fn) = find_main_function(&root_node, &request.code) {
        language_specific.insert("has_main".to_string(), serde_json::Value::Bool(true));
        language_specific.insert("main_position".to_string(), serde_json::to_value(main_fn)?);
    }
    
    // Extract crate dependencies
    let dependencies = extract_dependencies(&root_node, &request.code);
    language_specific.insert("dependencies".to_string(), serde_json::to_value(dependencies)?);
    
    // Find unsafe blocks
    let unsafe_blocks = find_unsafe_blocks(&root_node, &request.code);
    language_specific.insert("unsafe_count".to_string(), serde_json::Value::Number(unsafe_blocks.len().into()));
    
    Ok(ParseResult {
        ast,
        errors,
        metrics,
        language_specific: serde_json::Value::Object(language_specific),
    })
}

fn convert_node_to_ast(
    node: &tree_sitter::Node,
    source: &str,
    include_positions: bool,
) -> AstNode {
    let mut properties = HashMap::new();
    
    // Add node text if it's a leaf node
    if node.child_count() == 0 {
        let text = node.utf8_text(source.as_bytes()).unwrap_or("");
        properties.insert("value".to_string(), serde_json::Value::String(text.to_string()));
    }
    
    // Add field name if available
    if let Some(field_name) = node.field_name() {
        properties.insert("field".to_string(), serde_json::Value::String(field_name.to_string()));
    }
    
    // Add position information
    let position = if include_positions {
        Some(Position {
            start_line: node.start_position().row,
            start_column: node.start_position().column,
            end_line: node.end_position().row,
            end_column: node.end_position().column,
            start_byte: node.start_byte(),
            end_byte: node.end_byte(),
        })
    } else {
        None
    };
    
    // Recursively convert children
    let children = (0..node.child_count())
        .map(|i| node.child(i).unwrap())
        .map(|child| convert_node_to_ast(&child, source, include_positions))
        .collect();
    
    AstNode {
        node_type: node.kind().to_string(),
        children,
        properties,
        position,
    }
}

fn extract_dependencies(node: &tree_sitter::Node, source: &str) -> Vec<String> {
    let mut dependencies = Vec::new();
    let mut cursor = node.walk();
    
    visit_nodes(&mut cursor, |node| {
        if node.kind() == "use_declaration" {
            if let Some(path_node) = node.child_by_field_name("argument") {
                let dep = path_node.utf8_text(source.as_bytes()).unwrap_or("");
                dependencies.push(dep.to_string());
            }
        }
    });
    
    dependencies
}

fn find_unsafe_blocks(node: &tree_sitter::Node, source: &str) -> Vec<Position> {
    let mut unsafe_blocks = Vec::new();
    let mut cursor = node.walk();
    
    visit_nodes(&mut cursor, |node| {
        if node.kind() == "unsafe_block" {
            unsafe_blocks.push(Position {
                start_line: node.start_position().row,
                start_column: node.start_position().column,
                end_line: node.end_position().row,
                end_column: node.end_position().column,
                start_byte: node.start_byte(),
                end_byte: node.end_byte(),
            });
        }
    });
    
    unsafe_blocks
}

fn visit_nodes<F>(cursor: &mut tree_sitter::TreeCursor, mut f: F)
where
    F: FnMut(&tree_sitter::Node),
{
    loop {
        f(&cursor.node());
        
        if cursor.goto_first_child() {
            continue;
        }
        
        if cursor.goto_next_sibling() {
            continue;
        }
        
        loop {
            if !cursor.goto_parent() {
                return;
            }
            
            if cursor.goto_next_sibling() {
                break;
            }
        }
    }
}
```

### Unified AST Transformation

```typescript
// packages/ccl-core/src/parser/ast-transformer.ts
interface UnifiedAst {
  type: 'Program';
  body: Statement[];
  comments: Comment[];
  imports: Import[];
  exports: Export[];
  metadata: {
    language: string;
    version: string;
    features: string[];
  };
}

interface Statement {
  type: string;
  id?: Identifier;
  params?: Parameter[];
  body?: Statement | Statement[];
  position: Position;
}

interface Import {
  type: 'Import';
  source: string;
  specifiers: ImportSpecifier[];
  position: Position;
}

interface Export {
  type: 'Export';
  declaration?: Statement;
  specifiers?: ExportSpecifier[];
  source?: string;
  position: Position;
}

class AstTransformer {
  private transformers: Map<string, LanguageTransformer>;
  
  constructor() {
    this.transformers = new Map();
    this.registerTransformers();
  }
  
  private registerTransformers() {
    // Register language-specific transformers
    this.transformers.set('javascript', new JavaScriptTransformer());
    this.transformers.set('python', new PythonTransformer());
    this.transformers.set('java', new JavaTransformer());
    this.transformers.set('go', new GoTransformer());
    this.transformers.set('rust', new RustTransformer());
  }
  
  transform(ast: AstNode, language: string): UnifiedAst {
    const transformer = this.transformers.get(language);
    if (!transformer) {
      throw new Error(`No transformer for language: ${language}`);
    }
    
    return transformer.transform(ast);
  }
}

abstract class LanguageTransformer {
  abstract transform(ast: AstNode): UnifiedAst;
  
  protected extractImports(ast: AstNode): Import[] {
    // Override in language-specific transformers
    return [];
  }
  
  protected extractExports(ast: AstNode): Export[] {
    // Override in language-specific transformers
    return [];
  }
  
  protected normalizeStatement(node: AstNode): Statement {
    // Common statement normalization
    return {
      type: this.mapNodeType(node.node_type),
      position: node.position!,
    };
  }
  
  protected abstract mapNodeType(nodeType: string): string;
}

class JavaScriptTransformer extends LanguageTransformer {
  transform(ast: AstNode): UnifiedAst {
    const body = this.transformBody(ast);
    const imports = this.extractImports(ast);
    const exports = this.extractExports(ast);
    const comments = this.extractComments(ast);
    
    return {
      type: 'Program',
      body,
      comments,
      imports,
      exports,
      metadata: {
        language: 'javascript',
        version: 'ES2022',
        features: ['modules', 'async', 'classes'],
      },
    };
  }
  
  private transformBody(ast: AstNode): Statement[] {
    const statements: Statement[] = [];
    
    for (const child of ast.children) {
      if (this.isStatement(child)) {
        statements.push(this.transformStatement(child));
      }
    }
    
    return statements;
  }
  
  private transformStatement(node: AstNode): Statement {
    switch (node.node_type) {
      case 'function_declaration':
        return this.transformFunction(node);
      case 'class_declaration':
        return this.transformClass(node);
      case 'variable_declaration':
        return this.transformVariable(node);
      default:
        return this.normalizeStatement(node);
    }
  }
  
  private transformFunction(node: AstNode): Statement {
    const nameNode = node.children.find(c => c.node_type === 'identifier');
    const paramsNode = node.children.find(c => c.node_type === 'formal_parameters');
    const bodyNode = node.children.find(c => c.node_type === 'statement_block');
    
    return {
      type: 'FunctionDeclaration',
      id: nameNode ? { type: 'Identifier', name: nameNode.properties.value as string } : undefined,
      params: paramsNode ? this.extractParameters(paramsNode) : [],
      body: bodyNode ? this.transformStatement(bodyNode) : undefined,
      position: node.position!,
    };
  }
  
  protected mapNodeType(nodeType: string): string {
    const mapping: Record<string, string> = {
      'function_declaration': 'FunctionDeclaration',
      'class_declaration': 'ClassDeclaration',
      'variable_declaration': 'VariableDeclaration',
      'if_statement': 'IfStatement',
      'for_statement': 'ForStatement',
      'while_statement': 'WhileStatement',
      'return_statement': 'ReturnStatement',
    };
    
    return mapping[nodeType] || nodeType;
  }
}

class PythonTransformer extends LanguageTransformer {
  transform(ast: AstNode): UnifiedAst {
    // Python-specific transformation
    const body = this.transformBody(ast);
    const imports = this.extractPythonImports(ast);
    
    return {
      type: 'Program',
      body,
      comments: [],
      imports,
      exports: [], // Python doesn't have explicit exports
      metadata: {
        language: 'python',
        version: '3.10',
        features: ['type_hints', 'async', 'decorators'],
      },
    };
  }
  
  private extractPythonImports(ast: AstNode): Import[] {
    const imports: Import[] = [];
    
    this.walkAst(ast, (node) => {
      if (node.node_type === 'import_statement' || node.node_type === 'import_from_statement') {
        imports.push(this.transformImport(node));
      }
    });
    
    return imports;
  }
  
  protected mapNodeType(nodeType: string): string {
    const mapping: Record<string, string> = {
      'function_definition': 'FunctionDeclaration',
      'class_definition': 'ClassDeclaration',
      'assignment': 'VariableDeclaration',
      'if_statement': 'IfStatement',
      'for_statement': 'ForStatement',
      'while_statement': 'WhileStatement',
      'return_statement': 'ReturnStatement',
    };
    
    return mapping[nodeType] || nodeType;
  }
}
```

### Incremental Parsing

```rust
// packages/ccl-core/src/parser/incremental.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

pub struct IncrementalParser {
    trees: Arc<RwLock<HashMap<String, ParseTree>>>,
    parser_manager: Arc<ParserPluginManager>,
}

struct ParseTree {
    tree: tree_sitter::Tree,
    source: String,
    version: u64,
}

#[derive(Debug)]
pub struct Edit {
    pub start_byte: usize,
    pub old_end_byte: usize,
    pub new_end_byte: usize,
    pub start_position: Position,
    pub old_end_position: Position,
    pub new_end_position: Position,
}

impl IncrementalParser {
    pub fn new(parser_manager: Arc<ParserPluginManager>) -> Self {
        Self {
            trees: Arc::new(RwLock::new(HashMap::new())),
            parser_manager,
        }
    }
    
    pub async fn parse_incremental(
        &self,
        file_path: &str,
        new_source: &str,
        language: &str,
        edits: Vec<Edit>,
    ) -> Result<ParseResult> {
        let mut trees = self.trees.write().await;
        
        if let Some(parse_tree) = trees.get_mut(file_path) {
            // Apply edits to existing tree
            for edit in edits {
                parse_tree.tree.edit(&tree_sitter::InputEdit {
                    start_byte: edit.start_byte,
                    old_end_byte: edit.old_end_byte,
                    new_end_byte: edit.new_end_byte,
                    start_position: tree_sitter::Point {
                        row: edit.start_position.start_line,
                        column: edit.start_position.start_column,
                    },
                    old_end_position: tree_sitter::Point {
                        row: edit.old_end_position.end_line,
                        column: edit.old_end_position.end_column,
                    },
                    new_end_position: tree_sitter::Point {
                        row: edit.new_end_position.end_line,
                        column: edit.new_end_position.end_column,
                    },
                });
            }
            
            // Re-parse with old tree as base
            let result = self.parser_manager.parse_with_tree(
                language,
                new_source,
                Some(&parse_tree.tree),
            ).await?;
            
            // Update stored tree
            parse_tree.source = new_source.to_string();
            parse_tree.version += 1;
            
            Ok(result)
        } else {
            // First parse of this file
            let result = self.parser_manager.parse(
                language,
                new_source,
                ParseOptions::default(),
            ).await?;
            
            // Store tree for future incremental parsing
            trees.insert(file_path.to_string(), ParseTree {
                tree: result.tree.clone(), // Would need to expose tree
                source: new_source.to_string(),
                version: 1,
            });
            
            Ok(result)
        }
    }
    
    pub async fn get_parse_tree(&self, file_path: &str) -> Option<tree_sitter::Tree> {
        let trees = self.trees.read().await;
        trees.get(file_path).map(|pt| pt.tree.clone())
    }
}
```

### Language Detection

```rust
// packages/ccl-core/src/parser/language_detector.rs
use std::path::Path;

pub struct LanguageDetector {
    extension_map: HashMap<String, String>,
    content_patterns: Vec<ContentPattern>,
}

struct ContentPattern {
    pattern: regex::Regex,
    language: String,
    priority: i32,
}

impl LanguageDetector {
    pub fn new() -> Self {
        let mut detector = Self {
            extension_map: HashMap::new(),
            content_patterns: Vec::new(),
        };
        
        detector.init_extension_map();
        detector.init_content_patterns();
        
        detector
    }
    
    fn init_extension_map(&mut self) {
        // Primary extensions
        self.extension_map.insert("js".to_string(), "javascript".to_string());
        self.extension_map.insert("jsx".to_string(), "javascript".to_string());
        self.extension_map.insert("ts".to_string(), "typescript".to_string());
        self.extension_map.insert("tsx".to_string(), "typescript".to_string());
        self.extension_map.insert("py".to_string(), "python".to_string());
        self.extension_map.insert("java".to_string(), "java".to_string());
        self.extension_map.insert("go".to_string(), "go".to_string());
        self.extension_map.insert("rs".to_string(), "rust".to_string());
        self.extension_map.insert("c".to_string(), "c".to_string());
        self.extension_map.insert("cpp".to_string(), "cpp".to_string());
        self.extension_map.insert("cc".to_string(), "cpp".to_string());
        self.extension_map.insert("cs".to_string(), "csharp".to_string());
        self.extension_map.insert("rb".to_string(), "ruby".to_string());
        self.extension_map.insert("php".to_string(), "php".to_string());
        self.extension_map.insert("swift".to_string(), "swift".to_string());
        self.extension_map.insert("kt".to_string(), "kotlin".to_string());
        self.extension_map.insert("scala".to_string(), "scala".to_string());
        self.extension_map.insert("dart".to_string(), "dart".to_string());
        self.extension_map.insert("ex".to_string(), "elixir".to_string());
        self.extension_map.insert("hs".to_string(), "haskell".to_string());
        self.extension_map.insert("lua".to_string(), "lua".to_string());
        self.extension_map.insert("r".to_string(), "r".to_string());
        self.extension_map.insert("jl".to_string(), "julia".to_string());
        self.extension_map.insert("m".to_string(), "objc".to_string());
        self.extension_map.insert("sql".to_string(), "sql".to_string());
    }
    
    fn init_content_patterns(&mut self) {
        // Shebang patterns
        self.content_patterns.push(ContentPattern {
            pattern: regex::Regex::new(r"^#!/usr/bin/env python").unwrap(),
            language: "python".to_string(),
            priority: 100,
        });
        
        self.content_patterns.push(ContentPattern {
            pattern: regex::Regex::new(r"^#!/usr/bin/env node").unwrap(),
            language: "javascript".to_string(),
            priority: 100,
        });
        
        // Language-specific patterns
        self.content_patterns.push(ContentPattern {
            pattern: regex::Regex::new(r"^\s*package\s+\w+").unwrap(),
            language: "java".to_string(),
            priority: 90,
        });
        
        self.content_patterns.push(ContentPattern {
            pattern: regex::Regex::new(r"^\s*package\s+main").unwrap(),
            language: "go".to_string(),
            priority: 95,
        });
    }
    
    pub fn detect_language(&self, file_path: &str, content: Option<&str>) -> Option<String> {
        // First try extension
        if let Some(ext) = Path::new(file_path).extension() {
            if let Some(lang) = self.extension_map.get(ext.to_str()?) {
                return Some(lang.clone());
            }
        }
        
        // Then try content patterns
        if let Some(content) = content {
            let mut best_match: Option<(&str, i32)> = None;
            
            for pattern in &self.content_patterns {
                if pattern.pattern.is_match(content) {
                    if best_match.is_none() || pattern.priority > best_match.unwrap().1 {
                        best_match = Some((&pattern.language, pattern.priority));
                    }
                }
            }
            
            if let Some((language, _)) = best_match {
                return Some(language.to_string());
            }
        }
        
        None
    }
}
```

## Validation Loop

### Level 1: Parser Accuracy Testing
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_parse_accuracy() {
        let manager = ParserPluginManager::new(PluginConfig {
            plugin_dir: "./plugins".to_string(),
            max_memory_pages: 1000,
            enable_caching: true,
            cache_size_mb: 100,
        });
        
        // Test various code samples
        let test_cases = vec![
            ("rust", include_str!("../test_data/sample.rs")),
            ("python", include_str!("../test_data/sample.py")),
            ("javascript", include_str!("../test_data/sample.js")),
        ];
        
        for (lang, code) in test_cases {
            let result = manager.parse(lang, code, ParseOptions::default()).await.unwrap();
            
            // Should parse without errors
            assert!(result.errors.is_empty());
            
            // Should have reasonable metrics
            assert!(result.metrics.parse_time_ms < 100.0);
            assert!(result.metrics.node_count > 0);
        }
    }
}
```

### Level 2: Performance Testing
```rust
#[tokio::test]
async fn test_parse_performance() {
    let manager = ParserPluginManager::new(PluginConfig::default());
    
    // Load large file (1MB)
    let large_code = generate_large_code_file(1_000_000);
    
    let start = std::time::Instant::now();
    let result = manager.parse("javascript", &large_code, ParseOptions::default()).await.unwrap();
    let duration = start.elapsed();
    
    // Should parse within 100ms
    assert!(duration.as_millis() < 100);
    
    // Memory usage should be reasonable
    assert!(result.metrics.memory_used_bytes < 10_000_000); // <10MB
}
```

### Level 3: Incremental Parsing Testing
```rust
#[tokio::test]
async fn test_incremental_parsing() {
    let manager = Arc::new(ParserPluginManager::new(PluginConfig::default()));
    let incremental = IncrementalParser::new(manager);
    
    let original = "function hello() { return 'world'; }";
    let modified = "function hello() { return 'hello world'; }";
    
    // Initial parse
    let result1 = incremental.parse_incremental(
        "test.js",
        original,
        "javascript",
        vec![],
    ).await.unwrap();
    
    // Incremental parse with edit
    let edit = Edit {
        start_byte: 26,
        old_end_byte: 33,
        new_end_byte: 39,
        start_position: Position { start_line: 0, start_column: 26, ..Default::default() },
        old_end_position: Position { end_line: 0, end_column: 33, ..Default::default() },
        new_end_position: Position { end_line: 0, end_column: 39, ..Default::default() },
    };
    
    let start = std::time::Instant::now();
    let result2 = incremental.parse_incremental(
        "test.js",
        modified,
        "javascript",
        vec![edit],
    ).await.unwrap();
    let duration = start.elapsed();
    
    // Incremental parse should be much faster
    assert!(duration.as_micros() < 1000); // <1ms
}
```

## Final Validation Checklist

- [ ] All language plugins load successfully
- [ ] Parse 1MB file in <100ms
- [ ] Memory usage <10MB per parser
- [ ] Incremental parsing <1ms
- [ ] Error recovery handles malformed code
- [ ] Unicode handled correctly
- [ ] AST structure consistent across languages
- [ ] Plugin hot-reload works
- [ ] Language detection >95% accurate
- [ ] Parser metrics tracked correctly

## Anti-Patterns to Avoid

1. **DON'T load all parsers at once** - Memory waste
2. **DON'T parse synchronously** - Blocks thread
3. **DON'T ignore memory limits** - WASM constraints
4. **DON'T skip error recovery** - Real code has errors
5. **DON'T use regex for parsing** - Use proper parsers
6. **DON'T ignore incremental parsing** - Performance loss
7. **DON'T mix parser versions** - Incompatible ASTs
8. **DON'T forget unicode** - Global users
9. **DON'T skip caching** - Repeated parsing
10. **DON'T ignore security** - Malicious code