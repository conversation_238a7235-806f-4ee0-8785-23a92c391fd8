# Firestore Real-Time Collections

name: "Firestore Real-Time Collections"
description: |
  Firestore NoSQL schema for CCL platform's real-time collaboration and user preferences.
  
  Core Principles:
  - **Real-Time Sync**: Instant updates across all connected clients
  - **Offline Support**: Local caching with automatic sync
  - **Document-Based**: Flexible schema with nested data
  - **Security Rules**: Fine-grained access control
  - **Optimistic Updates**: Immediate UI response with eventual consistency

## Goal

Define and maintain Firestore collections for real-time features including collaborative sessions, live conversation updates, user preferences sync, and instant notifications.

## Why

Firestore enables real-time collaborative features for CCL:
- Live collaboration on code analysis
- Real-time conversation updates
- Instant notifications
- Cross-device preference sync
- Offline-first user experience

This schema provides:
- Sub-100ms update propagation
- Automatic offline sync
- Scalable real-time subscriptions
- Fine-grained security rules
- Cost-effective for read-heavy workloads

## What

### User-Visible Behavior
- Instant updates when collaborating
- Real-time typing indicators
- Live cursor positions in shared sessions
- Immediate notification delivery
- Seamless offline/online transitions

### Technical Requirements
- [ ] Collections optimized for real-time queries
- [ ] Security rules for all collections
- [ ] Composite indexes for complex queries
- [ ] Document size limits considered (<1MB)
- [ ] Subcollection structure for scalability
- [ ] Presence system implementation
- [ ] Conflict resolution strategies

### Success Criteria
- [ ] Real-time updates propagate <100ms
- [ ] Offline changes sync when reconnected
- [ ] Security rules prevent unauthorized access
- [ ] Queries perform with <50ms latency
- [ ] Document structure supports all features

## All Needed Context

### Documentation & References
- url: https://firebase.google.com/docs/firestore/data-model
  why: Firestore data modeling best practices
- url: https://firebase.google.com/docs/firestore/security/get-started
  why: Security rules implementation
- url: https://firebase.google.com/docs/firestore/manage-indexes
  why: Index configuration for performance
- file: docs/architecture/technical-specification.md
  why: Source of current collection schemas

### Collection Structure

```
Firestore Collections:
├── sessions/
│   └── {sessionId}/
│       ├── participants (map)
│       └── sharedContext (map)
├── conversations/
│   └── {conversationId}/
│       ├── messages/ (subcollection)
│       └── state (document)
├── users/
│   └── {userId}/
│       ├── preferences (document)
│       └── notifications/ (subcollection)
├── drafts/
│   └── {userId}/
│       └── patterns/ (subcollection)
└── presence/
    └── {userId}/ (online status)
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Document size limit is 1MB
- **CRITICAL**: 500 writes/second per document limit
- **GOTCHA**: Subcollections don't inherit security rules
- **GOTCHA**: Can't query across subcollections
- **WARNING**: Deep nesting impacts performance
- **TIP**: Use batch writes for atomic updates
- **TIP**: Server timestamps for consistency

## Implementation Blueprint

### TypeScript Interfaces

```typescript
// Firestore collection schemas (TypeScript interfaces)

// Real-time collaboration sessions
interface Session {
  sessionId: string;
  repositoryId: string;
  createdAt: FirebaseFirestore.Timestamp;
  participants: {
    [userId: string]: {
      name: string;
      email: string;
      avatarUrl?: string;
      cursor?: {
        file: string;
        line: number;
        column: number;
      };
      status: 'active' | 'idle' | 'offline';
      lastActivity: FirebaseFirestore.Timestamp;
    }
  };
  sharedContext: {
    currentFile?: string;
    highlightedCode?: {
      file: string;
      startLine: number;
      endLine: number;
    };
    activePattern?: string;
    notes?: string;
  };
}

// Conversation state for real-time updates
interface ConversationState {
  conversationId: string;
  userId: string;
  repositoryId?: string;
  messages: Array<{
    messageId: string;
    role: 'user' | 'assistant';
    content: string;
    timestamp: FirebaseFirestore.Timestamp;
    sources?: string[];
    codeSnippets?: Array<{
      file: string;
      startLine: number;
      endLine: number;
      code: string;
    }>;
  }>;
  context: {
    currentFocus?: string;
    relevantFiles?: string[];
    activePatterns?: string[];
  };
  isTyping?: boolean;
  lastUpdated: FirebaseFirestore.Timestamp;
}

// User preferences (synced across devices)
interface UserPreferences {
  userId: string;
  theme: 'light' | 'dark' | 'system';
  editorSettings: {
    fontSize: number;
    fontFamily: string;
    tabSize: number;
    wordWrap: boolean;
  };
  notificationSettings: {
    email: boolean;
    browser: boolean;
    analysisComplete: boolean;
    marketplaceUpdates: boolean;
  };
  favoritePatterns: string[];
  recentRepositories: Array<{
    repositoryId: string;
    name: string;
    lastAccessed: FirebaseFirestore.Timestamp;
  }>;
  shortcuts: {
    [action: string]: string; // keyboard shortcuts
  };
}

// Pattern drafts (auto-saved)
interface PatternDraft {
  draftId: string;
  userId: string;
  name: string;
  description: string;
  template: string;
  examples: string[];
  category: string;
  language: string;
  tags: string[];
  lastSaved: FirebaseFirestore.Timestamp;
  isPublished: boolean;
}

// Real-time notifications
interface Notification {
  notificationId: string;
  userId: string;
  type: 'analysis_complete' | 'pattern_purchased' | 'mention' | 'system';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: FirebaseFirestore.Timestamp;
  expiresAt?: FirebaseFirestore.Timestamp;
}
```

### Collaboration Sessions

```typescript
// Real-time collaboration sessions
// Collection: /sessions/{sessionId}
interface Session extends FirestoreMetadata {
  sessionId: string;
  repositoryId: string;
  createdBy: string; // userId who created session
  participants: {
    [userId: string]: {
      name: string;
      email: string;
      avatarUrl?: string;
      role: 'owner' | 'editor' | 'viewer';
      cursor?: {
        file: string;
        line: number;
        column: number;
        selection?: {
          startLine: number;
          startColumn: number;
          endLine: number;
          endColumn: number;
        };
      };
      status: 'active' | 'idle' | 'offline';
      lastActivity: Timestamp;
      color: string; // Unique color for cursor/selection
    }
  };
  sharedContext: {
    currentFile?: string;
    highlightedCode?: {
      file: string;
      startLine: number;
      endLine: number;
      annotation?: string;
    };
    activePattern?: {
      patternId: string;
      patternName: string;
      occurrences: Array<{
        file: string;
        line: number;
      }>;
    };
    notes?: string; // Shared notes/comments
    drawingData?: any; // For whiteboard features
  };
  settings: {
    maxParticipants: number;
    allowGuests: boolean;
    expiresAt?: Timestamp;
  };
}

// Firestore security rules for sessions
const sessionRules = `
  match /sessions/{sessionId} {
    allow read: if request.auth != null && 
      (request.auth.uid in resource.data.participants ||
       resource.data.settings.allowGuests == true);
    
    allow create: if request.auth != null &&
      request.auth.uid == request.resource.data.createdBy;
    
    allow update: if request.auth != null &&
      request.auth.uid in resource.data.participants &&
      resource.data.participants[request.auth.uid].role in ['owner', 'editor'];
    
    allow delete: if request.auth != null &&
      request.auth.uid == resource.data.createdBy;
  }
`;
```

### Conversation State

```typescript
// Real-time conversation state
// Collection: /conversations/{conversationId}
interface ConversationState extends FirestoreMetadata {
  conversationId: string;
  userId: string;
  repositoryId?: string;
  lastMessageId?: string;
  lastMessageAt?: Timestamp;
  messageCount: number;
  participants: string[]; // For shared conversations
  context: {
    currentFocus?: string; // Current area of interest
    relevantFiles?: string[]; // Files being discussed
    activePatterns?: Array<{
      patternId: string;
      patternName: string;
      confidence: number;
    }>;
    codebaseVersion?: string; // Git commit SHA
  };
  metadata: {
    model: string; // AI model being used
    temperature: number;
    maxTokens: number;
  };
  isTyping?: {
    [userId: string]: boolean;
  };
  unreadCount?: {
    [userId: string]: number;
  };
}

// Messages subcollection
// Collection: /conversations/{conversationId}/messages/{messageId}
interface Message extends FirestoreMetadata {
  messageId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  userId?: string; // For user messages
  sources?: Array<{
    type: 'file' | 'pattern' | 'documentation';
    path: string;
    startLine?: number;
    endLine?: number;
    confidence?: number;
  }>;
  codeSnippets?: Array<{
    language: string;
    code: string;
    file?: string;
    startLine?: number;
    highlighted?: boolean;
  }>;
  metadata?: {
    tokensUsed?: number;
    processingTime?: number;
    confidenceScore?: number;
    feedback?: {
      helpful: boolean;
      rating?: number;
      comment?: string;
    };
  };
  attachments?: Array<{
    type: 'image' | 'file';
    url: string;
    name: string;
    size: number;
  }>;
}

// Security rules for conversations
const conversationRules = `
  match /conversations/{conversationId} {
    allow read: if request.auth != null && 
      (request.auth.uid == resource.data.userId ||
       request.auth.uid in resource.data.participants);
    
    allow create: if request.auth != null &&
      request.auth.uid == request.resource.data.userId;
    
    allow update: if request.auth != null &&
      (request.auth.uid == resource.data.userId ||
       request.auth.uid in resource.data.participants);
    
    match /messages/{messageId} {
      allow read: if request.auth != null &&
        (request.auth.uid == parent(/conversations/{conversationId}).userId ||
         request.auth.uid in parent(/conversations/{conversationId}).participants);
      
      allow create: if request.auth != null &&
        (request.auth.uid == parent(/conversations/{conversationId}).userId ||
         request.auth.uid in parent(/conversations/{conversationId}).participants);
    }
  }
`;
```

### User Preferences

```typescript
// User preferences (synced across devices)
// Collection: /users/{userId}/preferences
interface UserPreferences extends FirestoreMetadata {
  userId: string;
  theme: 'light' | 'dark' | 'system';
  language: string; // UI language
  timezone: string;
  
  editorSettings: {
    fontSize: number;
    fontFamily: string;
    tabSize: number;
    wordWrap: boolean;
    lineNumbers: boolean;
    minimap: boolean;
    bracketMatching: boolean;
    autoSave: boolean;
    autoSaveDelay: number; // milliseconds
  };
  
  aiSettings: {
    preferredModel: string;
    temperature: number;
    autoSuggest: boolean;
    suggestDelay: number;
    contextLines: number; // Lines of context to include
  };
  
  notificationSettings: {
    email: {
      enabled: boolean;
      frequency: 'immediate' | 'hourly' | 'daily';
      types: {
        analysisComplete: boolean;
        patternDetected: boolean;
        collaborationInvite: boolean;
        marketplaceUpdates: boolean;
        securityAlerts: boolean;
      };
    };
    browser: {
      enabled: boolean;
      sound: boolean;
      types: {
        mentions: boolean;
        messages: boolean;
        analysisComplete: boolean;
      };
    };
  };
  
  dashboardLayout: {
    widgets: Array<{
      id: string;
      type: string;
      position: { x: number; y: number };
      size: { width: number; height: number };
      config: any;
    }>;
  };
  
  favoritePatterns: Array<{
    patternId: string;
    addedAt: Timestamp;
    tags?: string[];
  }>;
  
  recentItems: {
    repositories: Array<{
      repositoryId: string;
      name: string;
      lastAccessed: Timestamp;
      pinned?: boolean;
    }>;
    patterns: Array<{
      patternId: string;
      name: string;
      lastUsed: Timestamp;
    }>;
    searches: Array<{
      query: string;
      timestamp: Timestamp;
      resultCount: number;
    }>;
  };
  
  shortcuts: {
    [action: string]: {
      key: string;
      modifiers: Array<'ctrl' | 'alt' | 'shift' | 'meta'>;
    };
  };
  
  privacy: {
    shareAnalytics: boolean;
    publicProfile: boolean;
    showActivity: boolean;
  };
}

// Security rules for user preferences
const userPreferencesRules = `
  match /users/{userId} {
    allow read: if request.auth != null && request.auth.uid == userId;
    allow write: if request.auth != null && request.auth.uid == userId;
    
    match /preferences {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
`;
```

### Pattern Drafts

```typescript
// Auto-saved pattern drafts
// Collection: /drafts/{userId}/patterns/{draftId}
interface PatternDraft extends FirestoreMetadata {
  draftId: string;
  userId: string;
  name: string;
  description: string;
  category: string;
  language: string;
  
  template: {
    code: string;
    placeholders?: Array<{
      name: string;
      description: string;
      defaultValue?: string;
      validation?: string; // Regex
    }>;
  };
  
  examples: Array<{
    title: string;
    description?: string;
    code: string;
    language: string;
  }>;
  
  documentation: {
    content: string; // Markdown
    sections: Array<{
      title: string;
      anchor: string;
    }>;
  };
  
  testing: {
    unitTests?: string;
    integrationTests?: string;
    testResults?: Array<{
      timestamp: Timestamp;
      passed: boolean;
      output: string;
    }>;
  };
  
  metadata: {
    tags: string[];
    version: string;
    compatibility: string[]; // Framework versions
    dependencies?: any;
  };
  
  collaboration?: {
    sharedWith: Array<{
      userId: string;
      role: 'viewer' | 'editor';
      sharedAt: Timestamp;
    }>;
    comments?: Array<{
      userId: string;
      content: string;
      timestamp: Timestamp;
      resolved?: boolean;
    }>;
  };
  
  publishStatus: {
    isPublished: boolean;
    publishedAt?: Timestamp;
    patternId?: string; // Marketplace pattern ID
    lastSyncedAt?: Timestamp;
  };
  
  autoSave: {
    enabled: boolean;
    lastSaved: Timestamp;
    unsavedChanges: boolean;
  };
}
```

### Real-Time Notifications

```typescript
// User notifications
// Collection: /users/{userId}/notifications/{notificationId}
interface Notification extends FirestoreMetadata {
  notificationId: string;
  userId: string;
  type: 'analysis_complete' | 'pattern_match' | 'collaboration_invite' | 
        'mention' | 'security_alert' | 'system_update' | 'marketplace';
  
  title: string;
  message: string;
  
  data: {
    // Type-specific data
    repositoryId?: string;
    analysisId?: string;
    patternId?: string;
    sessionId?: string;
    conversationId?: string;
    mentionedBy?: string;
    securityLevel?: 'info' | 'warning' | 'critical';
    actionUrl?: string;
    actionLabel?: string;
  };
  
  status: {
    read: boolean;
    readAt?: Timestamp;
    acted: boolean;
    actedAt?: Timestamp;
    dismissed: boolean;
    dismissedAt?: Timestamp;
  };
  
  delivery: {
    channels: Array<'app' | 'email' | 'browser'>;
    emailSent?: boolean;
    pushSent?: boolean;
    inAppShown?: boolean;
  };
  
  priority: 'low' | 'normal' | 'high' | 'urgent';
  expiresAt?: Timestamp;
  
  actions?: Array<{
    label: string;
    action: string; // Action identifier
    style?: 'primary' | 'secondary' | 'danger';
  }>;
}
```

### Presence System

```typescript
// User presence for online status
// Collection: /presence/{userId}
interface UserPresence extends FirestoreMetadata {
  userId: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: Timestamp;
  
  currentActivity?: {
    type: 'analyzing' | 'browsing' | 'editing' | 'collaborating';
    repositoryId?: string;
    sessionId?: string;
    details?: string;
  };
  
  device: {
    type: 'web' | 'desktop' | 'mobile';
    browser?: string;
    os?: string;
  };
  
  // Automatically managed by Firebase
  connections?: {
    [connectionId: string]: {
      connectedAt: Timestamp;
      userAgent: string;
    };
  };
}

// Presence system implementation
const presenceSystem = `
// Client-side presence management
import { getDatabase, ref, onValue, onDisconnect, serverTimestamp } from 'firebase/database';

function setupPresence(userId: string) {
  const db = getDatabase();
  const userStatusRef = ref(db, \`/status/\${userId}\`);
  
  // Firestore reference
  const userPresenceRef = doc(firestore, 'presence', userId);
  
  const isOfflineForDatabase = {
    status: 'offline',
    lastSeen: serverTimestamp(),
  };
  
  const isOnlineForDatabase = {
    status: 'online',
    lastSeen: serverTimestamp(),
  };
  
  onValue(ref(db, '.info/connected'), (snapshot) => {
    if (snapshot.val() === false) {
      return;
    }
    
    onDisconnect(userStatusRef)
      .set(isOfflineForDatabase)
      .then(() => {
        set(userStatusRef, isOnlineForDatabase);
        updateDoc(userPresenceRef, isOnlineForDatabase);
      });
  });
}
`;
```

### Composite Indexes

```yaml
# firestore.indexes.json
{
  "indexes": [
    {
      "collectionGroup": "messages",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "createdAt", "order": "DESCENDING" },
        { "fieldPath": "role", "order": "ASCENDING" }
      ]
    },
    {
      "collectionGroup": "notifications",
      "queryScope": "COLLECTION", 
      "fields": [
        { "fieldPath": "userId", "order": "ASCENDING" },
        { "fieldPath": "status.read", "order": "ASCENDING" },
        { "fieldPath": "createdAt", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "patterns",
      "queryScope": "COLLECTION_GROUP",
      "fields": [
        { "fieldPath": "userId", "order": "ASCENDING" },
        { "fieldPath": "publishStatus.isPublished", "order": "ASCENDING" },
        { "fieldPath": "updatedAt", "order": "DESCENDING" }
      ]
    }
  ]
}
```

### Security Rules

```javascript
// Complete Firestore security rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return isAuthenticated() && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }
    
    // Session rules
    match /sessions/{sessionId} {
      allow read: if isAuthenticated() && 
        (request.auth.uid in resource.data.participants ||
         resource.data.settings.allowGuests == true);
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() &&
        request.auth.uid in resource.data.participants;
      allow delete: if isAuthenticated() &&
        request.auth.uid == resource.data.createdBy;
    }
    
    // User data rules
    match /users/{userId} {
      allow read: if isOwner(userId);
      allow write: if isOwner(userId);
      
      match /preferences {
        allow read, write: if isOwner(userId);
      }
      
      match /notifications/{notificationId} {
        allow read: if isOwner(userId);
        allow create: if isAuthenticated();
        allow update: if isOwner(userId) && 
          request.resource.data.diff(resource.data).affectedKeys()
            .hasOnly(['status', 'delivery']);
      }
    }
    
    // Drafts rules
    match /drafts/{userId}/patterns/{draftId} {
      allow read: if isOwner(userId) ||
        request.auth.uid in resource.data.collaboration.sharedWith.userId;
      allow write: if isOwner(userId);
      allow update: if isAuthenticated() &&
        request.auth.uid in resource.data.collaboration.sharedWith.userId &&
        resource.data.collaboration.sharedWith[request.auth.uid].role == 'editor';
    }
    
    // Presence rules
    match /presence/{userId} {
      allow read: if isAuthenticated();
      allow write: if isOwner(userId);
    }
  }
}
```

## Validation Loop

### Level 1: Schema Validation
```typescript
// Validate document structure
import { Firestore } from 'firebase-admin/firestore';

async function validateSchema(db: Firestore) {
  // Test document creation
  const testSession: Session = {
    sessionId: 'test-session',
    repositoryId: 'test-repo',
    createdBy: 'test-user',
    participants: {},
    sharedContext: {},
    settings: {
      maxParticipants: 10,
      allowGuests: false
    },
    createdAt: FieldValue.serverTimestamp(),
    updatedAt: FieldValue.serverTimestamp()
  };
  
  await db.collection('sessions').doc('test-session').set(testSession);
}
```

### Level 2: Real-time Performance Testing
```typescript
// Test real-time subscription performance
async function testRealtimePerformance() {
  const startTime = Date.now();
  const unsubscribe = db.collection('sessions')
    .where('participants.testUser', '!=', null)
    .onSnapshot((snapshot) => {
      const latency = Date.now() - startTime;
      console.log(`Real-time update latency: ${latency}ms`);
    });
    
  // Trigger update
  await db.collection('sessions').doc('test-session').update({
    'participants.testUser': { name: 'Test User', status: 'active' }
  });
}
```

### Level 3: Security Rules Testing
```bash
# Test security rules with Firebase CLI
firebase emulators:exec --only firestore "npm test"

# Run security rules tests
npm run test:security-rules
```

## Final Validation Checklist

- [ ] All TypeScript interfaces compile without errors
- [ ] Document size stays under 1MB limit
- [ ] Real-time updates propagate in <100ms
- [ ] Offline changes sync when reconnected
- [ ] Security rules block unauthorized access
- [ ] Composite indexes created for all queries
- [ ] Subcollection structure is efficient
- [ ] Presence system tracks online status
- [ ] Auto-save functionality works reliably
- [ ] Cost projections are within budget

## Anti-Patterns to Avoid

1. **DON'T store large blobs** - Use Cloud Storage for files
2. **DON'T create deep nesting** - Maximum 100 levels
3. **DON'T use sequential IDs** - Causes hotspots
4. **DON'T ignore security rules** - Data breaches
5. **DON'T poll for updates** - Use real-time listeners
6. **DON'T store sensitive data** - PII should be encrypted
7. **DON'T create wide documents** - Split into subcollections
8. **DON'T ignore offline state** - Handle connection loss
9. **DON'T batch too many writes** - 500 operations limit
10. **DON'T forget cleanup** - Delete old notifications
