# Cloud Spanner Database Schema

name: "Cloud Spanner Database Schema"
description: |
  Complete Cloud Spanner schema definition for CCL platform's transactional data storage.
  
  Core Principles:
  - **Global Distribution**: Horizontally scalable across regions
  - **Strong Consistency**: ACID transactions with external consistency
  - **Hierarchical Data**: Parent-child relationships with interleaving
  - **Efficient Indexing**: Strategic indexes for query performance
  - **Schema Evolution**: Designed for backward-compatible changes

## Goal

Define and maintain the complete Cloud Spanner schema for CCL platform, including all tables, indexes, foreign keys, and interleaved relationships needed for transactional data storage.

## Why

Cloud Spanner serves as the primary transactional database for CCL, storing:
- User accounts and authentication data
- Repository metadata and analysis results
- Pattern detection results and marketplace data
- API keys and audit logs
- Real-time collaboration state

This schema enables:
- Global scale with strong consistency
- Efficient parent-child queries through interleaving
- Fast lookups via strategic indexing
- Data integrity through foreign key constraints

## What

### User-Visible Behavior
- Sub-millisecond queries for user data
- Consistent reads across all regions
- Efficient pagination for large result sets
- Real-time updates for collaborative features

### Technical Requirements
- [ ] All tables properly defined with appropriate data types
- [ ] Primary keys optimized for distribution
- [ ] Foreign key relationships enforced
- [ ] Interleaved tables for performance
- [ ] Indexes for common query patterns
- [ ] Timestamp columns with commit timestamp option
- [ ] JSON columns for flexible metadata

### Success Criteria
- [ ] Schema can be deployed to Cloud Spanner
- [ ] All foreign key constraints are valid
- [ ] Indexes cover all common query patterns
- [ ] No hot spots in key distribution
- [ ] Schema supports all application features

## All Needed Context

### Documentation & References
- url: https://cloud.google.com/spanner/docs/schema-design
  why: Best practices for Spanner schema design
- url: https://cloud.google.com/spanner/docs/schema-and-data-model
  why: Data types and modeling patterns
- file: docs/architecture/technical-specification.md
  why: Source of current schema definition

### Complete Enhanced Schema Structure

```
ccl_main database (25+ tables, enterprise-ready)
├── users (root table)
│   ├── api_keys (related)
│   ├── mfa_configurations (related)
│   ├── user_sessions (related)
│   └── conversations (related)
│       └── messages (interleaved)
├── organizations (root table)
│   ├── teams (related)
│   │   └── team_members (related)
│   └── subscriptions (related)
├── repositories (root table)
│   ├── analyses (interleaved)
│   ├── patterns (interleaved)
│   ├── file_analyses (interleaved)
│   └── collaboration_sessions (related)
│       ├── session_participants (interleaved)
│       ├── session_events (interleaved)
│       └── session_annotations (interleaved)
├── marketplace_patterns (root table - enhanced)
│   ├── pattern_reviews (related)
│   ├── pattern_dependencies (related)
│   ├── pattern_licenses (related)
│   └── marketplace_revenue (related)
├── pattern_purchases (root table)
├── marketplace_payouts (root table)
├── webhooks (root table)
│   └── webhook_deliveries (interleaved)
├── usage_records (root table)
├── code_embeddings (root table)
├── ai_models (root table - NEW)
│   └── model_training_jobs (related - NEW)
├── language_parsers (root table - NEW)
├── analytics_events (root table - NEW)
├── security_incidents (root table - NEW)
├── system_metrics (root table - NEW)
├── compliance_events (root table)
├── feature_flags (root table)
└── audit_logs (root table)
```

### Known Gotchas & Library Quirks
- **CRITICAL**: Avoid monotonically increasing keys (causes hot spots)
- **CRITICAL**: Use STRING(36) for UUIDs, not BYTES
- **CRITICAL**: Use UUID v4 with bit reversal for better distribution
- **GOTCHA**: Interleaved tables must use parent's primary key as prefix
- **GOTCHA**: Can't drop columns, only make them nullable
- **GOTCHA**: Indexes on array columns have special syntax
- **WARNING**: Large JSON columns can impact performance
- **TIP**: Use allow_commit_timestamp=true for automatic timestamps
- **TIP**: Implement data retention policies from day one

## Implementation Blueprint

### Complete Schema Definition

```sql
-- Create database
CREATE DATABASE ccl_main;

-- UUID generation function for better distribution
-- Note: Use bit-reversed UUID v4 to avoid hotspots
-- Generate UUID v4, reverse first 8 characters: REVERSE(LEFT(UUID(), 8)) + RIGHT(UUID(), 28)

-- Users and authentication
CREATE TABLE users (
    user_id STRING(36) NOT NULL, -- Bit-reversed UUID v4
    email STRING(255) NOT NULL,
    name STRING(255),
    avatar_url STRING(1024),
    password_hash STRING(255), -- bcrypt hash
    email_verified BOOL NOT NULL DEFAULT FALSE,
    failed_login_attempts INT64 NOT NULL DEFAULT 0,
    locked_until TIMESTAMP,
    mfa_enabled BOOL NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_login_at TIMESTAMP,
    subscription_tier STRING(50) NOT NULL DEFAULT 'free',
    subscription_expires_at TIMESTAMP,
    organization_id STRING(36),
    settings JSON,
    metadata JSON,
    -- Data retention: Archive after 7 years of inactivity
    archived_at TIMESTAMP,
) PRIMARY KEY (user_id);

CREATE UNIQUE INDEX idx_users_email ON users(email);

-- Organizations for enterprise accounts
CREATE TABLE organizations (
    organization_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    subscription_tier STRING(50) NOT NULL DEFAULT 'enterprise',
    settings JSON,
    metadata JSON,
) PRIMARY KEY (organization_id);

-- Repository information
CREATE TABLE repositories (
    repository_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    organization_id STRING(36),
    name STRING(255) NOT NULL,
    url STRING(1024),
    provider STRING(50), -- github, gitlab, bitbucket, local
    last_analysis_at TIMESTAMP,
    last_commit_sha STRING(100),
    total_files INT64,
    total_lines INT64,
    primary_language STRING(50),
    languages JSON, -- {"javascript": 45.2, "python": 30.1, ...}
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (repository_id);

CREATE INDEX idx_repos_user ON repositories(user_id);
CREATE INDEX idx_repos_org ON repositories(organization_id);

-- Analysis runs (interleaved with repositories)
CREATE TABLE analyses (
    repository_id STRING(36) NOT NULL, -- Must be first for interleaving
    analysis_id STRING(36) NOT NULL,
    started_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    completed_at TIMESTAMP,
    status STRING(50) NOT NULL, -- pending, running, completed, failed
    commit_sha STRING(100),
    files_analyzed INT64,
    patterns_detected INT64,
    duration_ms INT64,
    error_message STRING(MAX),
    results JSON, -- Detailed analysis results
    -- Data retention: Archive after 2 years
    archived_at TIMESTAMP,
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
) PRIMARY KEY (repository_id, analysis_id),
INTERLEAVE IN PARENT repositories ON DELETE CASCADE;

-- Detected patterns (interleaved with repositories)
CREATE TABLE patterns (
    repository_id STRING(36) NOT NULL, -- Must be first for interleaving
    pattern_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    pattern_type STRING(100) NOT NULL,
    pattern_name STRING(255),
    confidence FLOAT64 NOT NULL,
    occurrences INT64 NOT NULL,
    files JSON, -- List of files where pattern appears
    template STRING(MAX),
    examples JSON,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    -- Data retention: Archive after 3 years
    archived_at TIMESTAMP,
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
    FOREIGN KEY (repository_id, analysis_id) REFERENCES analyses (repository_id, analysis_id),
) PRIMARY KEY (repository_id, pattern_id),
INTERLEAVE IN PARENT repositories ON DELETE CASCADE;

CREATE INDEX idx_patterns_type ON patterns(pattern_type);
CREATE INDEX idx_patterns_confidence ON patterns(confidence);
CREATE INDEX idx_patterns_analysis ON patterns(repository_id, analysis_id);
CREATE INDEX idx_patterns_created ON patterns(created_at DESC);

-- File-level analysis (interleaved with repositories)
CREATE TABLE file_analyses (
    repository_id STRING(36) NOT NULL, -- Must be first for interleaving
    file_id STRING(36) NOT NULL,
    analysis_id STRING(36) NOT NULL,
    file_path STRING(1024) NOT NULL,
    language STRING(50),
    lines_of_code INT64,
    complexity_score FLOAT64,
    ast_hash STRING(64), -- For change detection
    dependencies JSON,
    exports JSON,
    functions JSON,
    classes JSON,
    patterns JSON,
    embeddings_id STRING(36), -- Reference to stored embeddings
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    -- Data retention: Archive after 1 year
    archived_at TIMESTAMP,
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
    FOREIGN KEY (repository_id, analysis_id) REFERENCES analyses (repository_id, analysis_id),
) PRIMARY KEY (repository_id, file_id),
INTERLEAVE IN PARENT repositories ON DELETE CASCADE;

CREATE INDEX idx_files_path ON file_analyses(repository_id, file_path);
CREATE INDEX idx_files_language ON file_analyses(language);
CREATE INDEX idx_files_complexity ON file_analyses(complexity_score DESC);

-- Conversation history
CREATE TABLE conversations (
    conversation_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    repository_id STRING(36),
    started_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_message_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    message_count INT64 NOT NULL DEFAULT 0,
    context JSON,
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
) PRIMARY KEY (conversation_id);

CREATE INDEX idx_conversations_user ON conversations(user_id);

-- Individual messages (interleaved with conversations)
CREATE TABLE messages (
    conversation_id STRING(36) NOT NULL, -- Must be first for interleaving
    message_id STRING(36) NOT NULL,
    role STRING(50) NOT NULL, -- user, assistant
    content STRING(MAX) NOT NULL,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    tokens_used INT64,
    confidence_score FLOAT64,
    sources JSON, -- Referenced files/patterns
    metadata JSON,
    -- Data retention: Archive after 6 months
    archived_at TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations (conversation_id),
) PRIMARY KEY (conversation_id, message_id),
INTERLEAVE IN PARENT conversations ON DELETE CASCADE;

-- Teams and collaboration
CREATE TABLE teams (
    team_id STRING(36) NOT NULL,
    organization_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    description STRING(MAX),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    settings JSON,
    metadata JSON,
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (team_id);

CREATE INDEX idx_teams_org ON teams(organization_id);

-- Team membership
CREATE TABLE team_members (
    team_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    role STRING(50) NOT NULL, -- owner, admin, member, viewer
    added_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    added_by STRING(36),
    permissions JSON,
    metadata JSON,
    FOREIGN KEY (team_id) REFERENCES teams (team_id),
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (added_by) REFERENCES users (user_id),
) PRIMARY KEY (team_id, user_id);

-- Real-time collaboration sessions
CREATE TABLE collaboration_sessions (
    session_id STRING(36) NOT NULL,
    repository_id STRING(36) NOT NULL,
    created_by STRING(36) NOT NULL,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    expires_at TIMESTAMP NOT NULL,
    active BOOL NOT NULL DEFAULT TRUE,
    shared_context JSON,
    settings JSON,
    metadata JSON,
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
    FOREIGN KEY (created_by) REFERENCES users (user_id),
) PRIMARY KEY (session_id);

CREATE INDEX idx_sessions_repo ON collaboration_sessions(repository_id);
CREATE INDEX idx_sessions_active ON collaboration_sessions(active, expires_at);

-- Session participants (interleaved with sessions)
CREATE TABLE session_participants (
    session_id STRING(36) NOT NULL, -- Must be first for interleaving
    user_id STRING(36) NOT NULL,
    joined_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_seen_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    status STRING(50) NOT NULL DEFAULT 'active', -- active, idle, disconnected
    cursor_position JSON, -- Current file/line/column
    permissions JSON,
    metadata JSON,
    FOREIGN KEY (session_id) REFERENCES collaboration_sessions (session_id),
    FOREIGN KEY (user_id) REFERENCES users (user_id),
) PRIMARY KEY (session_id, user_id),
INTERLEAVE IN PARENT collaboration_sessions ON DELETE CASCADE;

-- Webhooks configuration
CREATE TABLE webhooks (
    webhook_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    organization_id STRING(36),
    url STRING(1024) NOT NULL,
    events ARRAY<STRING(100)> NOT NULL, -- ['analysis.completed', 'pattern.detected']
    secret STRING(255) NOT NULL, -- For signature verification
    active BOOL NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_triggered_at TIMESTAMP,
    success_count INT64 NOT NULL DEFAULT 0,
    failure_count INT64 NOT NULL DEFAULT 0,
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (webhook_id);

CREATE INDEX idx_webhooks_user ON webhooks(user_id);
CREATE INDEX idx_webhooks_active ON webhooks(active);

-- Webhook delivery attempts
CREATE TABLE webhook_deliveries (
    webhook_id STRING(36) NOT NULL,
    delivery_id STRING(36) NOT NULL,
    event_type STRING(100) NOT NULL,
    payload JSON NOT NULL,
    http_status INT64,
    response_body STRING(MAX),
    attempted_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    delivered_at TIMESTAMP,
    retry_count INT64 NOT NULL DEFAULT 0,
    next_retry_at TIMESTAMP,
    final_attempt BOOL NOT NULL DEFAULT FALSE,
    metadata JSON,
    -- Data retention: Archive after 30 days
    archived_at TIMESTAMP,
    FOREIGN KEY (webhook_id) REFERENCES webhooks (webhook_id),
) PRIMARY KEY (webhook_id, delivery_id),
INTERLEAVE IN PARENT webhooks ON DELETE CASCADE;

CREATE INDEX idx_deliveries_retry ON webhook_deliveries(next_retry_at) WHERE next_retry_at IS NOT NULL;

-- Organization subscriptions and billing
CREATE TABLE subscriptions (
    subscription_id STRING(36) NOT NULL,
    organization_id STRING(36),
    user_id STRING(36),
    plan STRING(50) NOT NULL, -- free, pro, team, enterprise
    status STRING(50) NOT NULL, -- active, cancelled, past_due, trialing
    stripe_subscription_id STRING(255),
    stripe_customer_id STRING(255),
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    cancel_at_period_end BOOL NOT NULL DEFAULT FALSE,
    cancelled_at TIMESTAMP,
    trial_start TIMESTAMP,
    trial_end TIMESTAMP,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
    FOREIGN KEY (user_id) REFERENCES users (user_id),
) PRIMARY KEY (subscription_id);

CREATE INDEX idx_subscriptions_org ON subscriptions(organization_id);
CREATE INDEX idx_subscriptions_user ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_status ON subscriptions(status);

-- Usage metrics and billing
CREATE TABLE usage_records (
    record_id STRING(36) NOT NULL,
    subscription_id STRING(36) NOT NULL,
    metric_name STRING(100) NOT NULL, -- api_calls, analysis_minutes, storage_gb
    quantity INT64 NOT NULL,
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    recorded_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    billed BOOL NOT NULL DEFAULT FALSE,
    metadata JSON,
    -- Data retention: Archive after 3 years for tax compliance
    archived_at TIMESTAMP,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions (subscription_id),
) PRIMARY KEY (record_id);

CREATE INDEX idx_usage_subscription ON usage_records(subscription_id, period_start);
CREATE INDEX idx_usage_metric ON usage_records(metric_name, period_start);
CREATE INDEX idx_usage_unbilled ON usage_records(billed, period_start) WHERE billed = FALSE;

-- Multi-factor authentication
CREATE TABLE mfa_configurations (
    user_id STRING(36) NOT NULL,
    mfa_type STRING(50) NOT NULL, -- totp, sms, webauthn
    secret_hash STRING(255), -- For TOTP
    phone_number STRING(50), -- For SMS
    credential_id STRING(255), -- For WebAuthn
    public_key BYTES(MAX), -- For WebAuthn
    backup_codes ARRAY<STRING(64)>, -- Encrypted backup codes
    enabled BOOL NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_used_at TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
) PRIMARY KEY (user_id, mfa_type);

-- Security sessions and device tracking
CREATE TABLE user_sessions (
    session_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    device_fingerprint STRING(255),
    ip_address STRING(45),
    user_agent STRING(1024),
    location_country STRING(2),
    location_city STRING(100),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_activity_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    expires_at TIMESTAMP NOT NULL,
    revoked_at TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
) PRIMARY KEY (session_id);

CREATE INDEX idx_sessions_user ON user_sessions(user_id);
CREATE INDEX idx_sessions_active ON user_sessions(expires_at, revoked_at) WHERE revoked_at IS NULL;

-- Pattern reviews and ratings
CREATE TABLE pattern_reviews (
    review_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    rating INT64 NOT NULL, -- 1-5 stars
    title STRING(255),
    content STRING(MAX),
    helpful_count INT64 NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    verified_purchase BOOL NOT NULL DEFAULT FALSE,
    metadata JSON,
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id),
    FOREIGN KEY (user_id) REFERENCES users (user_id),
) PRIMARY KEY (review_id);

CREATE INDEX idx_reviews_pattern ON pattern_reviews(pattern_id, rating DESC);
CREATE INDEX idx_reviews_user ON pattern_reviews(user_id);
CREATE UNIQUE INDEX idx_reviews_unique ON pattern_reviews(pattern_id, user_id);

-- Pattern dependencies and relationships
CREATE TABLE pattern_dependencies (
    pattern_id STRING(36) NOT NULL,
    depends_on_pattern_id STRING(36) NOT NULL,
    dependency_type STRING(50) NOT NULL, -- requires, enhances, conflicts_with
    version_constraint STRING(100), -- "^1.0.0", ">=2.0.0"
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id),
    FOREIGN KEY (depends_on_pattern_id) REFERENCES marketplace_patterns (pattern_id),
) PRIMARY KEY (pattern_id, depends_on_pattern_id);

-- Code embeddings storage
CREATE TABLE code_embeddings (
    embedding_id STRING(36) NOT NULL,
    content_hash STRING(64) NOT NULL, -- SHA256 of the code content
    content_type STRING(50) NOT NULL, -- function, class, file, pattern
    repository_id STRING(36),
    file_path STRING(1024),
    language STRING(50),
    embedding_vector ARRAY<FLOAT64> NOT NULL, -- 768-dimensional vector
    model_version STRING(50) NOT NULL,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    -- Data retention: Archive after 1 year
    archived_at TIMESTAMP,
    FOREIGN KEY (repository_id) REFERENCES repositories (repository_id),
) PRIMARY KEY (embedding_id);

CREATE UNIQUE INDEX idx_embeddings_hash ON code_embeddings(content_hash, model_version);
CREATE INDEX idx_embeddings_repo ON code_embeddings(repository_id);
CREATE INDEX idx_embeddings_language ON code_embeddings(language);

-- Compliance and audit trails
CREATE TABLE compliance_events (
    event_id STRING(36) NOT NULL,
    event_type STRING(100) NOT NULL, -- gdpr_request, data_export, account_deletion
    user_id STRING(36),
    organization_id STRING(36),
    resource_type STRING(50),
    resource_id STRING(36),
    before_state JSON,
    after_state JSON,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    processed_at TIMESTAMP,
    status STRING(50) NOT NULL DEFAULT 'pending', -- pending, completed, failed
    compliance_framework ARRAY<STRING(50)>, -- ['gdpr', 'ccpa', 'hipaa']
    metadata JSON,
    -- Data retention: Keep for 7 years for compliance
    archived_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (event_id);

CREATE INDEX idx_compliance_type ON compliance_events(event_type, created_at DESC);
CREATE INDEX idx_compliance_user ON compliance_events(user_id);
CREATE INDEX idx_compliance_status ON compliance_events(status, created_at);

-- Feature flags and experiments
CREATE TABLE feature_flags (
    flag_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    description STRING(MAX),
    enabled BOOL NOT NULL DEFAULT FALSE,
    rollout_percentage INT64 NOT NULL DEFAULT 0, -- 0-100
    target_users ARRAY<STRING(36)>,
    target_organizations ARRAY<STRING(36)>,
    conditions JSON, -- Complex targeting rules
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    expires_at TIMESTAMP,
    metadata JSON,
) PRIMARY KEY (flag_id);

CREATE UNIQUE INDEX idx_flags_name ON feature_flags(name);
CREATE INDEX idx_flags_enabled ON feature_flags(enabled, rollout_percentage);

-- Pattern marketplace (enhanced with full business model)
CREATE TABLE marketplace_patterns (
    pattern_id STRING(36) NOT NULL,
    author_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    description STRING(MAX),
    category STRING(100),
    language STRING(50),
    price_cents INT64 NOT NULL DEFAULT 0,
    currency STRING(3) NOT NULL DEFAULT 'USD',
    template STRING(MAX) NOT NULL,
    documentation STRING(MAX),
    examples JSON,
    tags ARRAY<STRING(50)>,
    version STRING(20),
    downloads INT64 NOT NULL DEFAULT 0,
    rating FLOAT64,
    rating_count INT64 NOT NULL DEFAULT 0,
    status STRING(50) NOT NULL DEFAULT 'draft', -- draft, published, deprecated, suspended
    published_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    -- Enhanced marketplace features
    license_type STRING(50) NOT NULL DEFAULT 'individual', -- individual, team, enterprise, open_source
    support_level STRING(50) NOT NULL DEFAULT 'community', -- community, standard, premium
    compatibility JSON, -- Framework/library compatibility info
    performance_metrics JSON, -- Benchmark data
    security_scan_results JSON, -- Security analysis results
    quality_score FLOAT64, -- ML-generated quality assessment
    revenue_share_percentage INT64 NOT NULL DEFAULT 70, -- Author's revenue share (70%)
    total_revenue_cents INT64 NOT NULL DEFAULT 0,
    featured BOOL NOT NULL DEFAULT FALSE,
    metadata JSON,
    FOREIGN KEY (author_id) REFERENCES users (user_id),
) PRIMARY KEY (pattern_id);

CREATE INDEX idx_marketplace_category ON marketplace_patterns(category);
CREATE INDEX idx_marketplace_language ON marketplace_patterns(language);
CREATE INDEX idx_marketplace_status ON marketplace_patterns(status);
CREATE INDEX idx_marketplace_featured ON marketplace_patterns(featured, rating DESC);
CREATE INDEX idx_marketplace_quality ON marketplace_patterns(quality_score DESC);
CREATE INDEX idx_marketplace_revenue ON marketplace_patterns(total_revenue_cents DESC);

-- Pattern purchase history
CREATE TABLE pattern_purchases (
    purchase_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    price_cents INT64 NOT NULL,
    currency STRING(3) NOT NULL,
    stripe_payment_id STRING(255),
    purchased_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id),
    FOREIGN KEY (user_id) REFERENCES users (user_id),
) PRIMARY KEY (purchase_id);

CREATE INDEX idx_purchases_user ON pattern_purchases(user_id);
CREATE INDEX idx_purchases_pattern ON pattern_purchases(pattern_id);

-- API key management
CREATE TABLE api_keys (
    key_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    organization_id STRING(36),
    key_hash STRING(64) NOT NULL, -- SHA256 of the actual key
    name STRING(255),
    scopes ARRAY<STRING(50)>,
    expires_at TIMESTAMP,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    revoked_at TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (key_id);

CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);
CREATE INDEX idx_api_keys_user ON api_keys(user_id);

-- Comprehensive audit logging
CREATE TABLE audit_logs (
    log_id STRING(36) NOT NULL,
    user_id STRING(36),
    organization_id STRING(36),
    action STRING(100) NOT NULL,
    resource_type STRING(50),
    resource_id STRING(36),
    ip_address STRING(45),
    user_agent STRING(1024),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
) PRIMARY KEY (log_id);

CREATE INDEX idx_audit_user ON audit_logs(user_id);
CREATE INDEX idx_audit_org ON audit_logs(organization_id);
CREATE INDEX idx_audit_action ON audit_logs(action);
CREATE INDEX idx_audit_time ON audit_logs(created_at DESC);

-- AI/ML Models and Training Jobs
CREATE TABLE ai_models (
    model_id STRING(36) NOT NULL,
    name STRING(255) NOT NULL,
    model_type STRING(50) NOT NULL, -- embedding, similarity, pattern_detection, code_completion
    version STRING(20) NOT NULL,
    status STRING(50) NOT NULL DEFAULT 'training', -- training, active, deprecated, failed
    training_data_source JSON, -- Repository IDs and pattern sources
    hyperparameters JSON,
    performance_metrics JSON, -- accuracy, precision, recall, f1_score
    vertex_ai_model_id STRING(255), -- Reference to Vertex AI model
    created_by STRING(36),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    trained_at TIMESTAMP,
    deployed_at TIMESTAMP,
    deprecated_at TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (created_by) REFERENCES users (user_id),
) PRIMARY KEY (model_id);

CREATE INDEX idx_models_type ON ai_models(model_type, status);
CREATE INDEX idx_models_status ON ai_models(status);
CREATE UNIQUE INDEX idx_models_name_version ON ai_models(name, version);

-- Model training jobs
CREATE TABLE model_training_jobs (
    job_id STRING(36) NOT NULL,
    model_id STRING(36) NOT NULL,
    status STRING(50) NOT NULL DEFAULT 'pending', -- pending, running, completed, failed
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    duration_seconds INT64,
    error_message STRING(MAX),
    vertex_ai_job_id STRING(255),
    training_config JSON,
    progress_percentage INT64 NOT NULL DEFAULT 0,
    metrics JSON, -- Training metrics over time
    resource_usage JSON, -- Compute resource consumption
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    FOREIGN KEY (model_id) REFERENCES ai_models (model_id),
) PRIMARY KEY (job_id);

CREATE INDEX idx_training_jobs_model ON model_training_jobs(model_id);
CREATE INDEX idx_training_jobs_status ON model_training_jobs(status, created_at);

-- Language parser plugins (WebAssembly-based)
CREATE TABLE language_parsers (
    parser_id STRING(36) NOT NULL,
    language STRING(50) NOT NULL,
    name STRING(255) NOT NULL,
    version STRING(20) NOT NULL,
    parser_type STRING(50) NOT NULL DEFAULT 'plugin', -- built-in, plugin, custom
    wasm_binary_url STRING(1024), -- Cloud Storage URL for WASM binary
    wasm_binary_hash STRING(64), -- SHA256 hash for integrity
    grammar_rules JSON,
    syntax_patterns JSON,
    error_patterns JSON,
    file_extensions ARRAY<STRING(20)>,
    capabilities ARRAY<STRING(50)>, -- ast, syntax_highlighting, error_detection, code_completion
    status STRING(50) NOT NULL DEFAULT 'active', -- active, inactive, error, deprecated
    performance_metrics JSON, -- Parse time, memory usage, success rate
    usage_statistics JSON, -- Files parsed, success/failure counts
    installed_by STRING(36),
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    last_used_at TIMESTAMP,
    metadata JSON,
    FOREIGN KEY (installed_by) REFERENCES users (user_id),
) PRIMARY KEY (parser_id);

CREATE UNIQUE INDEX idx_parsers_language_version ON language_parsers(language, version);
CREATE INDEX idx_parsers_status ON language_parsers(status);
CREATE INDEX idx_parsers_usage ON language_parsers(last_used_at DESC);

-- Enhanced collaboration session events (for real-time sync)
CREATE TABLE session_events (
    session_id STRING(36) NOT NULL,
    event_id STRING(36) NOT NULL,
    user_id STRING(36) NOT NULL,
    event_type STRING(50) NOT NULL, -- cursor_move, selection_change, code_edit, annotation_add
    event_data JSON NOT NULL, -- Specific event payload
    timestamp TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    sequence_number INT64 NOT NULL, -- For ordering events
    acknowledged_by ARRAY<STRING(36)>, -- Users who have seen this event
    metadata JSON,
    -- Data retention: Archive after 7 days
    archived_at TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES collaboration_sessions (session_id),
    FOREIGN KEY (user_id) REFERENCES users (user_id),
) PRIMARY KEY (session_id, event_id),
INTERLEAVE IN PARENT collaboration_sessions ON DELETE CASCADE;

CREATE INDEX idx_session_events_timestamp ON session_events(session_id, timestamp);
CREATE INDEX idx_session_events_user ON session_events(session_id, user_id);

-- Session annotations (comments, highlights, suggestions)
CREATE TABLE session_annotations (
    session_id STRING(36) NOT NULL,
    annotation_id STRING(36) NOT NULL,
    created_by STRING(36) NOT NULL,
    annotation_type STRING(50) NOT NULL, -- comment, highlight, suggestion, question
    file_path STRING(1024) NOT NULL,
    line_start INT64 NOT NULL,
    line_end INT64 NOT NULL,
    column_start INT64,
    column_end INT64,
    content STRING(MAX) NOT NULL,
    resolved BOOL NOT NULL DEFAULT FALSE,
    resolved_by STRING(36),
    resolved_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    updated_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    metadata JSON,
    FOREIGN KEY (session_id) REFERENCES collaboration_sessions (session_id),
    FOREIGN KEY (created_by) REFERENCES users (user_id),
    FOREIGN KEY (resolved_by) REFERENCES users (user_id),
) PRIMARY KEY (session_id, annotation_id),
INTERLEAVE IN PARENT collaboration_sessions ON DELETE CASCADE;

CREATE INDEX idx_annotations_file ON session_annotations(session_id, file_path);
CREATE INDEX idx_annotations_resolved ON session_annotations(session_id, resolved);

-- Marketplace revenue and payouts
CREATE TABLE marketplace_revenue (
    revenue_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    purchase_id STRING(36) NOT NULL,
    author_id STRING(36) NOT NULL,
    gross_amount_cents INT64 NOT NULL, -- Total purchase amount
    platform_fee_cents INT64 NOT NULL, -- Platform commission
    author_amount_cents INT64 NOT NULL, -- Amount for author
    currency STRING(3) NOT NULL,
    recorded_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    period_start DATE NOT NULL, -- For monthly aggregation
    period_end DATE NOT NULL,
    payout_id STRING(36), -- Reference to payout batch
    payout_status STRING(50) NOT NULL DEFAULT 'pending', -- pending, paid, withheld
    metadata JSON,
    -- Data retention: Keep for 7 years for tax compliance
    archived_at TIMESTAMP,
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id),
    FOREIGN KEY (purchase_id) REFERENCES pattern_purchases (purchase_id),
    FOREIGN KEY (author_id) REFERENCES users (user_id),
) PRIMARY KEY (revenue_id);

CREATE INDEX idx_revenue_author ON marketplace_revenue(author_id, period_start);
CREATE INDEX idx_revenue_pattern ON marketplace_revenue(pattern_id, period_start);
CREATE INDEX idx_revenue_payout ON marketplace_revenue(payout_status, period_start);

-- Payout batches and transactions
CREATE TABLE marketplace_payouts (
    payout_id STRING(36) NOT NULL,
    author_id STRING(36) NOT NULL,
    total_amount_cents INT64 NOT NULL,
    currency STRING(3) NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    status STRING(50) NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed
    payment_method STRING(50) NOT NULL, -- stripe, paypal, bank_transfer
    payment_processor_id STRING(255), -- External payment reference
    requested_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    processed_at TIMESTAMP,
    completed_at TIMESTAMP,
    failed_at TIMESTAMP,
    failure_reason STRING(MAX),
    transaction_fee_cents INT64,
    net_amount_cents INT64,
    metadata JSON,
    FOREIGN KEY (author_id) REFERENCES users (user_id),
) PRIMARY KEY (payout_id);

CREATE INDEX idx_payouts_author ON marketplace_payouts(author_id, period_start);
CREATE INDEX idx_payouts_status ON marketplace_payouts(status, requested_at);

-- Pattern licenses and entitlements
CREATE TABLE pattern_licenses (
    license_id STRING(36) NOT NULL,
    pattern_id STRING(36) NOT NULL,
    user_id STRING(36),
    organization_id STRING(36),
    license_type STRING(50) NOT NULL, -- individual, team, enterprise
    seats INT64 NOT NULL DEFAULT 1,
    expires_at TIMESTAMP,
    status STRING(50) NOT NULL DEFAULT 'active', -- active, expired, suspended, revoked
    license_key STRING(255) NOT NULL, -- Encrypted license key
    purchased_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    activated_at TIMESTAMP,
    last_validated_at TIMESTAMP,
    usage_count INT64 NOT NULL DEFAULT 0,
    metadata JSON,
    FOREIGN KEY (pattern_id) REFERENCES marketplace_patterns (pattern_id),
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (license_id);

CREATE INDEX idx_licenses_pattern ON pattern_licenses(pattern_id);
CREATE INDEX idx_licenses_user ON pattern_licenses(user_id);
CREATE INDEX idx_licenses_org ON pattern_licenses(organization_id);
CREATE INDEX idx_licenses_status ON pattern_licenses(status, expires_at);
CREATE UNIQUE INDEX idx_licenses_key ON pattern_licenses(license_key);

-- Advanced analytics and metrics
CREATE TABLE analytics_events (
    event_id STRING(36) NOT NULL,
    event_type STRING(100) NOT NULL, -- page_view, api_call, pattern_search, code_analysis
    user_id STRING(36),
    organization_id STRING(36),
    session_id STRING(36),
    resource_type STRING(50),
    resource_id STRING(36),
    properties JSON, -- Event-specific properties
    timestamp TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    ip_address STRING(45),
    user_agent STRING(1024),
    referrer STRING(1024),
    -- Data retention: Archive after 1 year
    archived_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
) PRIMARY KEY (event_id);

CREATE INDEX idx_analytics_type ON analytics_events(event_type, timestamp);
CREATE INDEX idx_analytics_user ON analytics_events(user_id, timestamp);
CREATE INDEX idx_analytics_org ON analytics_events(organization_id, timestamp);

-- Security threat detection and incidents
CREATE TABLE security_incidents (
    incident_id STRING(36) NOT NULL,
    incident_type STRING(100) NOT NULL, -- suspicious_login, rate_limit_exceeded, data_access_violation
    severity STRING(50) NOT NULL, -- low, medium, high, critical
    user_id STRING(36),
    organization_id STRING(36),
    ip_address STRING(45),
    details JSON NOT NULL, -- Incident-specific details
    detected_at TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    status STRING(50) NOT NULL DEFAULT 'open', -- open, investigating, resolved, false_positive
    assigned_to STRING(36),
    resolved_at TIMESTAMP,
    resolution_notes STRING(MAX),
    automated_response JSON, -- Actions taken automatically
    metadata JSON,
    -- Data retention: Keep for 2 years for security audit
    archived_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (user_id),
    FOREIGN KEY (organization_id) REFERENCES organizations (organization_id),
    FOREIGN KEY (assigned_to) REFERENCES users (user_id),
) PRIMARY KEY (incident_id);

CREATE INDEX idx_incidents_type ON security_incidents(incident_type, detected_at DESC);
CREATE INDEX idx_incidents_severity ON security_incidents(severity, status);
CREATE INDEX idx_incidents_user ON security_incidents(user_id, detected_at DESC);
CREATE INDEX idx_incidents_status ON security_incidents(status, detected_at);

-- System health and monitoring metrics
CREATE TABLE system_metrics (
    metric_id STRING(36) NOT NULL,
    service_name STRING(100) NOT NULL, -- analysis-engine, query-intelligence, pattern-mining
    metric_name STRING(100) NOT NULL, -- response_time_ms, memory_usage_mb, queue_depth
    metric_value FLOAT64 NOT NULL,
    tags JSON, -- Additional metric labels
    timestamp TIMESTAMP NOT NULL OPTIONS (allow_commit_timestamp=true),
    -- Data retention: Archive after 90 days
    archived_at TIMESTAMP,
) PRIMARY KEY (metric_id);

CREATE INDEX idx_metrics_service ON system_metrics(service_name, metric_name, timestamp);
CREATE INDEX idx_metrics_timestamp ON system_metrics(timestamp DESC);
```

### Query Patterns and Optimization

```sql
-- Common query patterns the schema is optimized for:

-- 1. User authentication and profile with MFA
SELECT u.*, mfa.mfa_type 
FROM users u
LEFT JOIN mfa_configurations mfa ON u.user_id = mfa.user_id AND mfa.enabled = TRUE
WHERE u.email = @email;

-- 2. User's repositories with recent analyses and team access
SELECT r.*, a.*, t.name as team_name
FROM repositories r
LEFT JOIN analyses a ON r.repository_id = a.repository_id
LEFT JOIN teams t ON r.organization_id = t.organization_id
LEFT JOIN team_members tm ON t.team_id = tm.team_id AND tm.user_id = @user_id
WHERE r.user_id = @user_id OR tm.user_id IS NOT NULL
ORDER BY a.started_at DESC;

-- 3. Pattern search with reviews and ratings
SELECT mp.*, AVG(pr.rating) as avg_rating, COUNT(pr.review_id) as review_count
FROM marketplace_patterns mp
LEFT JOIN pattern_reviews pr ON mp.pattern_id = pr.pattern_id
WHERE mp.status = 'published'
  AND mp.category = @category
  AND mp.language = @language
GROUP BY mp.pattern_id, mp.name, mp.description, mp.price_cents
ORDER BY mp.downloads DESC, avg_rating DESC;

-- 4. Conversation history with messages (uses interleaving)
SELECT c.*, m.*
FROM conversations c
JOIN messages m ON c.conversation_id = m.conversation_id
WHERE c.user_id = @user_id
  AND m.archived_at IS NULL
ORDER BY m.created_at DESC
LIMIT 50;

-- 5. Active collaboration sessions with participants
SELECT cs.*, sp.user_id, sp.status, sp.cursor_position
FROM collaboration_sessions cs
JOIN session_participants sp ON cs.session_id = sp.session_id
WHERE cs.repository_id = @repository_id
  AND cs.active = TRUE
  AND cs.expires_at > CURRENT_TIMESTAMP()
  AND sp.status IN ('active', 'idle');

-- 6. Semantic code search using embeddings
SELECT ce.*, r.name as repo_name, similarity_score
FROM code_embeddings ce
JOIN repositories r ON ce.repository_id = r.repository_id
CROSS JOIN UNNEST(
  ML.COSINE_DISTANCE(@query_vector, ce.embedding_vector)
) AS similarity_score
WHERE ce.language = @language
  AND ce.archived_at IS NULL
  AND similarity_score > 0.8
ORDER BY similarity_score DESC
LIMIT 20;

-- 7. Usage analytics for billing
SELECT ur.metric_name, SUM(ur.quantity) as total_usage
FROM usage_records ur
JOIN subscriptions s ON ur.subscription_id = s.subscription_id
WHERE s.organization_id = @organization_id
  AND ur.period_start >= @period_start
  AND ur.period_end <= @period_end
  AND ur.billed = FALSE
GROUP BY ur.metric_name;

-- 8. Compliance audit trail
SELECT ce.*, u.email, o.name as org_name
FROM compliance_events ce
LEFT JOIN users u ON ce.user_id = u.user_id
LEFT JOIN organizations o ON ce.organization_id = o.organization_id
WHERE @compliance_framework = ANY(ce.compliance_framework)
  AND ce.created_at >= @start_date
  AND ce.created_at <= @end_date
ORDER BY ce.created_at DESC;

-- 9. Feature flag evaluation
SELECT ff.*
FROM feature_flags ff
WHERE ff.enabled = TRUE
  AND (ff.expires_at IS NULL OR ff.expires_at > CURRENT_TIMESTAMP())
  AND (
    @user_id = ANY(ff.target_users) OR
    @organization_id = ANY(ff.target_organizations) OR
    (ARRAY_LENGTH(ff.target_users) = 0 AND ARRAY_LENGTH(ff.target_organizations) = 0)
  );

-- 10. Webhook delivery retry queue
SELECT wd.*
FROM webhook_deliveries wd
JOIN webhooks w ON wd.webhook_id = w.webhook_id
WHERE wd.next_retry_at <= CURRENT_TIMESTAMP()
  AND wd.final_attempt = FALSE
  AND w.active = TRUE
ORDER BY wd.next_retry_at ASC
LIMIT 100;

-- 11. Pattern dependency resolution
WITH RECURSIVE pattern_deps AS (
  -- Base case: direct dependencies
  SELECT pattern_id, depends_on_pattern_id, dependency_type, 1 as depth
  FROM pattern_dependencies
  WHERE pattern_id = @pattern_id
  
  UNION ALL
  
  -- Recursive case: transitive dependencies
  SELECT pd.pattern_id, d.depends_on_pattern_id, d.dependency_type, pd.depth + 1
  FROM pattern_deps pd
  JOIN pattern_dependencies d ON pd.depends_on_pattern_id = d.pattern_id
  WHERE pd.depth < 5 -- Prevent infinite recursion
)
SELECT pd.*, mp.name, mp.version
FROM pattern_deps pd
JOIN marketplace_patterns mp ON pd.depends_on_pattern_id = mp.pattern_id;

-- 12. Real-time session synchronization with events
SELECT sp.user_id, sp.cursor_position, sp.last_seen_at, u.name,
       se.event_type, se.event_data, se.timestamp
FROM session_participants sp
JOIN users u ON sp.user_id = u.user_id
LEFT JOIN session_events se ON sp.session_id = se.session_id 
  AND se.user_id = sp.user_id
  AND se.timestamp > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 5 MINUTE)
WHERE sp.session_id = @session_id
  AND sp.status = 'active'
  AND sp.last_seen_at > TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 30 SECOND)
ORDER BY se.timestamp DESC, sp.last_seen_at DESC;

-- 13. AI model performance and training status
SELECT am.name, am.model_type, am.status, am.performance_metrics,
       mtj.status as training_status, mtj.progress_percentage,
       mtj.started_at, mtj.completed_at
FROM ai_models am
LEFT JOIN model_training_jobs mtj ON am.model_id = mtj.model_id
WHERE am.model_type = @model_type
  AND am.status IN ('training', 'active')
ORDER BY am.created_at DESC;

-- 14. Marketplace revenue analytics for authors
SELECT mp.name, mp.pattern_id,
       COUNT(mr.revenue_id) as total_sales,
       SUM(mr.author_amount_cents) as total_revenue_cents,
       AVG(mr.author_amount_cents) as avg_sale_amount,
       mp.rating, mp.downloads
FROM marketplace_patterns mp
LEFT JOIN marketplace_revenue mr ON mp.pattern_id = mr.pattern_id
WHERE mp.author_id = @author_id
  AND mr.recorded_at >= @period_start
  AND mr.recorded_at <= @period_end
GROUP BY mp.pattern_id, mp.name, mp.rating, mp.downloads
ORDER BY total_revenue_cents DESC;

-- 15. Security incident monitoring and threat detection
SELECT si.incident_type, si.severity, si.user_id, si.ip_address,
       si.details, si.detected_at, si.status,
       u.email, u.name,
       COUNT(*) OVER (PARTITION BY si.ip_address) as incidents_from_ip
FROM security_incidents si
LEFT JOIN users u ON si.user_id = u.user_id
WHERE si.detected_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 24 HOUR)
  AND si.status IN ('open', 'investigating')
ORDER BY si.severity DESC, si.detected_at DESC;

-- 16. Language parser usage and performance analytics
SELECT lp.language, lp.name, lp.version, lp.status,
       lp.performance_metrics, lp.usage_statistics,
       COUNT(fa.file_id) as files_analyzed_today
FROM language_parsers lp
LEFT JOIN file_analyses fa ON lp.language = fa.language
  AND fa.created_at >= CURRENT_DATE()
WHERE lp.status = 'active'
GROUP BY lp.parser_id, lp.language, lp.name, lp.version, 
         lp.status, lp.performance_metrics, lp.usage_statistics
ORDER BY files_analyzed_today DESC;

-- 17. Real-time collaboration session activity feed
SELECT se.event_type, se.event_data, se.timestamp, se.sequence_number,
       u.name as user_name, u.avatar_url,
       sa.annotation_type, sa.content, sa.file_path, sa.line_start
FROM session_events se
JOIN users u ON se.user_id = u.user_id
LEFT JOIN session_annotations sa ON se.session_id = sa.session_id
  AND JSON_EXTRACT_SCALAR(se.event_data, '$.annotation_id') = sa.annotation_id
WHERE se.session_id = @session_id
  AND se.timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
  AND se.archived_at IS NULL
ORDER BY se.sequence_number DESC, se.timestamp DESC
LIMIT 100;

-- 18. Pattern licensing and entitlement validation
SELECT pl.license_id, pl.license_type, pl.seats, pl.expires_at,
       pl.status, pl.usage_count,
       mp.name as pattern_name, mp.version,
       u.email, o.name as organization_name
FROM pattern_licenses pl
JOIN marketplace_patterns mp ON pl.pattern_id = mp.pattern_id
LEFT JOIN users u ON pl.user_id = u.user_id
LEFT JOIN organizations o ON pl.organization_id = o.organization_id
WHERE pl.pattern_id = @pattern_id
  AND pl.status = 'active'
  AND (pl.expires_at IS NULL OR pl.expires_at > CURRENT_TIMESTAMP())
  AND (@user_id = pl.user_id OR @organization_id = pl.organization_id);

-- 19. System health monitoring and alerting
SELECT sm.service_name, sm.metric_name, 
       AVG(sm.metric_value) as avg_value,
       MAX(sm.metric_value) as max_value,
       MIN(sm.metric_value) as min_value,
       COUNT(*) as sample_count
FROM system_metrics sm
WHERE sm.timestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 HOUR)
  AND sm.archived_at IS NULL
  AND sm.service_name = @service_name
GROUP BY sm.service_name, sm.metric_name
HAVING avg_value > @threshold_value
ORDER BY avg_value DESC;

-- 20. Advanced analytics event tracking
SELECT ae.event_type,
       COUNT(*) as event_count,
       COUNT(DISTINCT ae.user_id) as unique_users,
       COUNT(DISTINCT ae.session_id) as unique_sessions,
       JSON_EXTRACT(ae.properties, '$.feature') as feature_used
FROM analytics_events ae
WHERE ae.timestamp >= @start_date
  AND ae.timestamp < @end_date
  AND ae.event_type = @event_type
  AND ae.archived_at IS NULL
GROUP BY ae.event_type, feature_used
ORDER BY event_count DESC;

-- 21. ML-powered code similarity search with embeddings
WITH similar_code AS (
  SELECT ce.*, r.name as repo_name,
    ML.COSINE_DISTANCE(@query_vector, ce.embedding_vector) as similarity_score
  FROM code_embeddings ce
  JOIN repositories r ON ce.repository_id = r.repository_id
  WHERE ce.language = @language
    AND ce.archived_at IS NULL
    AND ce.model_version = @model_version
)
SELECT sc.*, fa.file_path, fa.complexity_score, fa.functions
FROM similar_code sc
JOIN file_analyses fa ON sc.repository_id = fa.repository_id 
  AND sc.file_path = fa.file_path
WHERE sc.similarity_score > 0.85
ORDER BY sc.similarity_score DESC
LIMIT 20;
```

### Data Retention and Archival Policies

```sql
-- Archive inactive user accounts after 7 years
UPDATE users 
SET archived_at = CURRENT_TIMESTAMP()
WHERE last_login_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 YEAR)
  AND archived_at IS NULL;

-- Archive old analysis results after 2 years
UPDATE analyses 
SET archived_at = CURRENT_TIMESTAMP()
WHERE completed_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 YEAR)
  AND archived_at IS NULL;

-- Archive detailed file analyses after 1 year
UPDATE file_analyses 
SET archived_at = CURRENT_TIMESTAMP()
WHERE created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 YEAR)
  AND archived_at IS NULL;

-- Archive old conversation messages after 1 year
UPDATE messages 
SET archived_at = CURRENT_TIMESTAMP()
WHERE created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 YEAR)
  AND archived_at IS NULL;

-- Clean up old audit logs (retain 7 years for compliance)
DELETE FROM audit_logs 
WHERE created_at < TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 7 YEAR);
```

### Migration and Evolution Strategy

```sql
-- Adding new columns (always nullable first)
ALTER TABLE users ADD COLUMN two_factor_enabled BOOL;

-- Creating new indexes
CREATE INDEX idx_patterns_created ON patterns(created_at DESC);

-- Adding new foreign keys
ALTER TABLE repositories 
ADD CONSTRAINT fk_repo_team 
FOREIGN KEY (team_id) REFERENCES teams(team_id);
```

## Validation Loop

### Level 1: Schema Validation
```bash
# Validate SQL syntax
gcloud spanner databases ddl update ccl_main \
  --instance=ccl-prod \
  --ddl-file=schema.sql \
  --dry-run

# Check for schema conflicts
gcloud spanner databases describe ccl_main \
  --instance=ccl-prod
```

### Level 2: Performance Testing
```bash
# Test query performance
gcloud spanner databases execute-sql ccl_main \
  --instance=ccl-prod \
  --sql="EXPLAIN SELECT * FROM patterns WHERE pattern_type = 'security'"

# Check index usage
gcloud spanner operations list \
  --instance=ccl-prod \
  --filter="metadata.@type:UpdateDatabaseDdlMetadata"
```

### Level 3: Data Integrity Tests
```sql
-- Verify foreign key constraints
SELECT COUNT(*) FROM repositories 
WHERE user_id NOT IN (SELECT user_id FROM users);

-- Check interleaving relationships
SELECT COUNT(*) FROM analyses 
WHERE repository_id NOT IN (SELECT repository_id FROM repositories);

-- Validate data types
SELECT COUNT(*) FROM users 
WHERE LENGTH(user_id) != 36;
```

## Final Validation Checklist

- [ ] All CREATE statements execute without errors
- [ ] Foreign key constraints are properly defined
- [ ] Interleaved tables use correct parent keys
- [ ] Indexes cover all WHERE/ORDER BY clauses
- [ ] No sequential primary keys (avoid hot spots)
- [ ] JSON columns used appropriately for flexible data
- [ ] Timestamp columns use allow_commit_timestamp
- [ ] Schema supports all application queries
- [ ] Performance testing shows <10ms query times
- [ ] Schema changes are backward compatible

## Anti-Patterns to Avoid

1. **DON'T use auto-incrementing IDs** - Causes hot spots
2. **DON'T create too many indexes** - Impacts write performance
3. **DON'T use large VARCHAR columns** - Use STRING(MAX) for large text
4. **DON'T forget interleaving** - Miss performance benefits
5. **DON'T skip foreign keys** - Lose referential integrity
6. **DON'T use BYTES for UUIDs** - STRING(36) is more efficient
7. **DON'T create wide tables** - Split into related tables
8. **DON'T ignore timezone handling** - Always use TIMESTAMP type
9. **DON'T nest JSON too deeply** - Impacts query performance
10. **DON'T forget cleanup policies** - Archive old audit logs
