name: "Google Cloud Platform Infrastructure Setup"
description: |
  Complete setup of Google Cloud Platform infrastructure for the CCL platform including
  project configuration, service enablement, IAM setup, networking, and security configuration.

---

## Goal
Establish a complete, production-ready Google Cloud Platform infrastructure for the CCL platform with proper security, networking, monitoring, and cost optimization configured from the start.

## Why
- **Foundation First**: All services depend on proper GCP infrastructure
- **Security by Design**: Implement zero-trust architecture from the beginning
- **Cost Optimization**: Proper resource configuration prevents cost overruns
- **Scalability**: Infrastructure must support growth from startup to enterprise

## What
A comprehensive GCP infrastructure setup that:
- Configures multiple GCP projects for different environments
- Sets up proper IAM roles and service accounts
- Establishes secure networking with VPC and firewall rules
- Configures all required GCP services and APIs
- Implements monitoring, logging, and alerting
- Sets up cost management and budgets

### Success Criteria
- [ ] All GCP projects created and configured
- [ ] Required APIs enabled across all projects
- [ ] IAM roles and service accounts properly configured
- [ ] VPC networking established with security rules
- [ ] All CCL services can deploy successfully
- [ ] Monitoring and logging operational
- [ ] Cost budgets and alerts configured
- [ ] Security scanning and compliance enabled

## All Needed Context

### Project Structure
```yaml
GCP Projects:
  ccl-platform-dev:
    purpose: Development environment
    services: All CCL services for development
    data_retention: 30 days
    
  ccl-platform-staging:
    purpose: Staging/testing environment
    services: All CCL services for testing
    data_retention: 90 days
    
  ccl-platform-prod:
    purpose: Production environment
    services: All CCL services for production
    data_retention: 7 years
    
  ccl-platform-shared:
    purpose: Shared resources (DNS, monitoring)
    services: Cloud DNS, monitoring dashboards
    data_retention: Permanent
```

### Required GCP Services
```yaml
Core Services:
  - Cloud Run: Microservice hosting
  - Cloud Spanner: Primary database
  - Cloud Storage: File and object storage
  - Firestore: Real-time database
  - BigQuery: Data warehouse and analytics
  - Memorystore (Redis): Caching layer
  
AI/ML Services:
  - Vertex AI: Machine learning platform
  - AI Platform: Model training and serving
  - Natural Language AI: Text processing
  - Translation AI: Multi-language support
  
Networking:
  - VPC: Virtual private cloud
  - Cloud Load Balancing: Traffic distribution
  - Cloud CDN: Content delivery
  - Cloud DNS: Domain name system
  
Security:
  - Identity and Access Management: Authentication
  - Cloud KMS: Key management
  - Secret Manager: Secret storage
  - Security Command Center: Security monitoring
  
Operations:
  - Cloud Monitoring: Metrics and alerting
  - Cloud Logging: Log aggregation
  - Cloud Trace: Distributed tracing
  - Cloud Profiler: Performance profiling
```

### Current Codebase Context
```bash
# Reference configuration patterns from:
examples/infrastructure/
├── gcp_setup.sh           # GCP setup script patterns
├── iam_configuration.yaml # IAM role definitions
├── vpc_configuration.yaml # Network configuration
└── service_accounts.yaml  # Service account setup
```

### Desired Infrastructure Structure
```bash
infrastructure/
├── terraform/              # Infrastructure as Code
│   ├── environments/       # Environment-specific configs
│   │   ├── dev/
│   │   ├── staging/
│   │   └── prod/
│   ├── modules/            # Reusable Terraform modules
│   │   ├── gcp-project/
│   │   ├── networking/
│   │   ├── iam/
│   │   └── monitoring/
│   └── shared/             # Shared infrastructure
├── scripts/                # Setup and maintenance scripts
│   ├── setup-gcp.sh       # Initial GCP setup
│   ├── enable-apis.sh     # API enablement
│   ├── create-service-accounts.sh
│   └── setup-monitoring.sh
├── configs/                # Configuration files
│   ├── iam-policies.yaml  # IAM policy definitions
│   ├── firewall-rules.yaml # Network security rules
│   └── monitoring-alerts.yaml # Alerting configuration
└── docs/
    ├── README.md          # Infrastructure documentation
    ├── runbook.md         # Operations runbook
    └── disaster-recovery.md # DR procedures
```

### Service Account Requirements
```yaml
Service Accounts:
  ccl-analysis-engine:
    purpose: Analysis Engine service authentication
    permissions:
      - spanner.databases.read
      - spanner.databases.write
      - storage.objects.read
      - storage.objects.write
      - aiplatform.endpoints.predict
      
  ccl-query-intelligence:
    purpose: Query Intelligence service authentication
    permissions:
      - spanner.databases.read
      - firestore.documents.read
      - firestore.documents.write
      - aiplatform.endpoints.predict
      - redis.instances.use
      
  ccl-pattern-mining:
    purpose: Pattern Mining service authentication
    permissions:
      - bigquery.datasets.read
      - bigquery.tables.read
      - bigquery.jobs.create
      - aiplatform.trainingJobs.create
      - storage.objects.read
      - storage.objects.write
      
  ccl-marketplace:
    purpose: Marketplace service authentication
    permissions:
      - spanner.databases.read
      - spanner.databases.write
      - firestore.documents.read
      - firestore.documents.write
      - storage.objects.read
      
  ccl-collaboration:
    purpose: Collaboration service authentication
    permissions:
      - firestore.documents.read
      - firestore.documents.write
      - redis.instances.use
      - spanner.databases.read
      
  ccl-deployment:
    purpose: CI/CD deployment authentication
    permissions:
      - run.services.create
      - run.services.update
      - storage.objects.create
      - cloudbuild.builds.create
```

### Network Configuration
```yaml
VPC Configuration:
  ccl-vpc:
    region: us-central1
    subnets:
      ccl-services-subnet:
        cidr: 1*******/24
        purpose: Microservices
        
      ccl-data-subnet:
        cidr: 10.0.2.0/24
        purpose: Databases and storage
        
      ccl-ml-subnet:
        cidr: 10.0.3.0/24
        purpose: ML training and inference
        
Firewall Rules:
  allow-internal:
    direction: ingress
    source_ranges: [10.0.0.0/16]
    allowed: all internal traffic
    
  allow-https:
    direction: ingress
    source_ranges: [0.0.0.0/0]
    allowed: tcp:443
    
  allow-health-checks:
    direction: ingress
    source_ranges: [130.211.0.0/22, 35.191.0.0/16]
    allowed: tcp:8080-8090
```

## Implementation Blueprint

### Phase 1: Project Setup
1. **Create GCP Projects**
   ```bash
   # Create projects
   gcloud projects create ccl-platform-dev --name="CCL Development"
   gcloud projects create ccl-platform-staging --name="CCL Staging"
   gcloud projects create ccl-platform-prod --name="CCL Production"
   gcloud projects create ccl-platform-shared --name="CCL Shared Resources"
   
   # Set billing account
   gcloud billing projects link ccl-platform-dev --billing-account=$BILLING_ACCOUNT
   gcloud billing projects link ccl-platform-staging --billing-account=$BILLING_ACCOUNT
   gcloud billing projects link ccl-platform-prod --billing-account=$BILLING_ACCOUNT
   gcloud billing projects link ccl-platform-shared --billing-account=$BILLING_ACCOUNT
   ```

2. **Enable Required APIs**
   ```bash
   # Core APIs for all projects
   PROJECTS=("ccl-platform-dev" "ccl-platform-staging" "ccl-platform-prod")
   APIS=(
     "run.googleapis.com"
     "spanner.googleapis.com"
     "storage.googleapis.com"
     "firestore.googleapis.com"
     "bigquery.googleapis.com"
     "redis.googleapis.com"
     "aiplatform.googleapis.com"
     "compute.googleapis.com"
     "cloudbuild.googleapis.com"
     "secretmanager.googleapis.com"
     "monitoring.googleapis.com"
     "logging.googleapis.com"
   )
   
   for project in "${PROJECTS[@]}"; do
     for api in "${APIS[@]}"; do
       gcloud services enable $api --project=$project
     done
   done
   ```

### Phase 2: IAM Configuration
1. **Create Service Accounts**
   ```bash
   # Analysis Engine service account
   gcloud iam service-accounts create ccl-analysis-engine \
     --display-name="CCL Analysis Engine" \
     --project=ccl-platform-prod
   
   # Grant necessary permissions
   gcloud projects add-iam-policy-binding ccl-platform-prod \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/spanner.databaseUser"
   ```

2. **Configure IAM Policies**
   ```yaml
   # iam-policies.yaml
   bindings:
     - members:
         - serviceAccount:<EMAIL>
       role: roles/spanner.databaseUser
     - members:
         - serviceAccount:<EMAIL>
       role: roles/firestore.user
   ```

### Phase 3: Networking Setup
1. **Create VPC Network**
   ```bash
   # Create VPC
   gcloud compute networks create ccl-vpc \
     --subnet-mode=custom \
     --project=ccl-platform-prod
   
   # Create subnets
   gcloud compute networks subnets create ccl-services-subnet \
     --network=ccl-vpc \
     --range=1*******/24 \
     --region=us-central1 \
     --project=ccl-platform-prod
   ```

2. **Configure Firewall Rules**
   ```bash
   # Allow internal traffic
   gcloud compute firewall-rules create ccl-allow-internal \
     --network=ccl-vpc \
     --allow=tcp,udp,icmp \
     --source-ranges=10.0.0.0/16 \
     --project=ccl-platform-prod
   ```

### Phase 4: Database Setup
1. **Create Spanner Instance**
   ```bash
   gcloud spanner instances create ccl-instance \
     --config=regional-us-central1 \
     --description="CCL Production Database" \
     --nodes=3 \
     --project=ccl-platform-prod
   ```

2. **Create Firestore Database**
   ```bash
   gcloud firestore databases create \
     --region=us-central1 \
     --project=ccl-platform-prod
   ```

### Phase 5: Monitoring Setup
1. **Configure Cloud Monitoring**
   ```bash
   # Create notification channels
   gcloud alpha monitoring channels create \
     --display-name="CCL Alerts Email" \
     --type=email \
     --channel-labels=email_address=<EMAIL> \
     --project=ccl-platform-prod
   ```

2. **Set up Alerting Policies**
   ```yaml
   # monitoring-alerts.yaml
   alertPolicy:
     displayName: "High Error Rate"
     conditions:
       - displayName: "Error rate > 5%"
         conditionThreshold:
           filter: 'resource.type="cloud_run_revision"'
           comparison: COMPARISON_GREATER_THAN
           thresholdValue: 0.05
   ```

## Validation Gates

### Infrastructure Validation
```bash
# Verify project setup
gcloud projects list --filter="name:ccl-platform"

# Check API enablement
gcloud services list --enabled --project=ccl-platform-prod

# Verify service accounts
gcloud iam service-accounts list --project=ccl-platform-prod

# Test network connectivity
gcloud compute networks describe ccl-vpc --project=ccl-platform-prod

# Verify database instances
gcloud spanner instances list --project=ccl-platform-prod
gcloud firestore databases list --project=ccl-platform-prod
```

### Security Validation
```bash
# Check IAM policies
gcloud projects get-iam-policy ccl-platform-prod

# Verify firewall rules
gcloud compute firewall-rules list --project=ccl-platform-prod

# Test service account permissions
gcloud auth activate-service-account --key-file=service-account-key.json
gcloud spanner databases list --project=ccl-platform-prod
```

### Monitoring Validation
```bash
# Check monitoring setup
gcloud monitoring channels list --project=ccl-platform-prod

# Verify alerting policies
gcloud alpha monitoring policies list --project=ccl-platform-prod

# Test log aggregation
gcloud logging logs list --project=ccl-platform-prod
```

## Success Metrics

### Infrastructure Metrics
- **Project Setup**: All 4 projects created and configured
- **API Enablement**: 100% required APIs enabled
- **Service Accounts**: All service accounts created with proper permissions
- **Network Configuration**: VPC and subnets operational

### Security Metrics
- **IAM Compliance**: 100% least-privilege access
- **Network Security**: All firewall rules properly configured
- **Secret Management**: All secrets stored in Secret Manager
- **Audit Logging**: 100% API calls logged

### Operational Metrics
- **Monitoring Coverage**: 100% services monitored
- **Alerting Setup**: All critical alerts configured
- **Cost Management**: Budgets and alerts set up
- **Backup Strategy**: All data backup configured

## Final Validation Checklist
- [ ] All GCP projects created and configured
- [ ] Required APIs enabled across environments
- [ ] Service accounts created with proper permissions
- [ ] VPC networking configured with security rules
- [ ] Spanner and Firestore databases created
- [ ] Monitoring and alerting configured
- [ ] Cost budgets and alerts set up
- [ ] Security scanning enabled
- [ ] Documentation complete
- [ ] Runbook created
- [ ] Disaster recovery plan documented

---

## Implementation Notes

### Security Best Practices
- Use least-privilege access for all service accounts
- Enable audit logging for all API calls
- Implement network segmentation with VPC
- Use Secret Manager for all sensitive data

### Cost Optimization
- Set up billing budgets and alerts
- Use committed use discounts for predictable workloads
- Implement resource labeling for cost tracking
- Regular cost review and optimization

### Operational Excellence
- Implement comprehensive monitoring and alerting
- Create detailed runbooks for common operations
- Set up automated backup and disaster recovery
- Regular security and compliance audits
