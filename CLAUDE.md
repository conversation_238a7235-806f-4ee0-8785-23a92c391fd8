# CCL Project Context for Claude Code

name: "CCL Project Context"
description: |
  Complete context engineering setup for AI-powered development of the CCL (Codebase Context Layer) platform.
  
  Core Principles:
  - **Context is King**: Include ALL necessary documentation, examples, and caveats
  - **Validation Loops**: Provide executable tests/lints the AI can run and fix
  - **Information Dense**: Use keywords and patterns from the codebase
  - **Progressive Success**: Start simple, validate, then enhance

## Goal
Enable AI coding assistants to understand and develop the CCL platform with minimal iterations, comprehensive context awareness, and production-ready code quality.

## Why
This context system enables:
- AI assistants to understand CCL architecture instantly
- Consistent development practices across all CCL services
- Validation at every step to ensure code quality
- Reduced debugging and faster development cycles
- Production-ready implementations from first attempt

## What
### User-Visible Behavior
- AI assistants have complete CCL platform understanding
- Automatic validation of code quality and architecture compliance
- Progressive implementation following established patterns
- Comprehensive error handling and troubleshooting

### Technical Requirements
- [ ] Complete CCL architecture understanding
- [ ] Service-specific implementation patterns
- [ ] Validation commands for quality assurance
- [ ] Integration with existing PRPs
- [ ] Context engineering best practices

### Success Criteria
- [ ] AI can implement features following CCL patterns
- [ ] All validation commands execute successfully
- [ ] Code follows established CCL conventions
- [ ] Integration with PRPs is seamless
- [ ] Development velocity increases significantly

# 🔄 Project Awareness & Context

## Always Reference These Files (In Order)
1. **Read `PLANNING.md`** at conversation start for architecture, goals, and constraints
2. **Check `TASK.md`** before any work - add new tasks with date if not listed
3. **Consult `PRPs/implementation-guide.md`** for detailed code patterns and examples
4. **Review `PRPs/architecture-patterns.md`** for architectural patterns and principles
5. **Check `PRPs/feature-specifications.md`** for feature specifications and requirements
6. **Use `INITIAL.md`** as template for new feature requests

## Context Loading Commands
```bash
# Start any CCL development session with:
/initialize-context-system     # Load complete CCL context
/integrate-task-system        # Sync with current tasks
/setup-dev-environment       # Prepare development environment
/start-dev-session           # Begin development with full context
```

## CCL Project Overview
CCL (Codebase Context Layer) is a cloud-native, AI-powered platform for codebase intelligence. You're working on a multi-service architecture using:
- **Rust** (analysis-engine): AST parsing and code analysis
- **Python** (query-intelligence, pattern-mining): AI/ML operations
- **Go** (marketplace): High-performance API services
- **TypeScript** (web, sdk): Frontend and client libraries

# 🏗️ Architecture & Service Boundaries

## Service Responsibilities (NEVER MIX)
- `analysis-engine/`: Code parsing, AST analysis (Rust only)
- `query-intelligence/`: Natural language processing (Python only)
- `pattern-mining/`: Pattern detection and ML (Python only)
- `marketplace/`: Commerce and distribution (Go only)
- `web/`: Frontend UI (TypeScript/React only)
- `sdk/`: Client libraries (TypeScript only)

## Google Cloud Platform Standards
```yaml
Required Services:
  - Spanner: Transactional data (patterns, users, billing)
  - BigQuery: Analytics and aggregations
  - Firestore: Real-time collaboration data
  - Cloud Storage: Code artifacts and analysis results
  - Vertex AI: ML model training and inference
  - Cloud Run: All service deployments
  - Pub/Sub: Event-driven communication
```

# 🧱 Code Structure & Modularity

## File Organization
- **Never exceed 500 lines** per file - split into modules
- **Service structure**:
  ```
  service-name/
  ├── cmd/               # Entry points
  ├── internal/          # Private implementation
  ├── pkg/              # Public packages
  ├── api/              # API definitions (proto/openapi)
  ├── tests/            # All test files
  └── docs/             # Service documentation
  ```

## Language-Specific Patterns

### Rust (Analysis Engine)
```rust
// Always use Result types with custom errors
pub fn analyze_codebase(path: &Path) -> Result<Analysis, AnalysisError>

// Use async/await for I/O
// Use rayon for CPU-bound parallelism
// Prefer &str over String for inputs
```

### Python (AI Services)
```python
# Always use type hints
from typing import List, Dict, Optional

# Use dataclasses for models
@dataclass
class QueryResult:
    answer: str
    confidence: float
    references: List[CodeReference]

# Async by default
async def process_query(query: str) -> QueryResult:
```

### Go (Marketplace)
```go
// Follow standard project layout
// Always use context.Context
func (s *Service) GetPattern(ctx context.Context, id string) (*Pattern, error)

// Return errors, don't panic
// Use structured logging
```

### TypeScript (Web/SDK)
```typescript
// Strict mode always
// No any types
interface Pattern {
  id: string;
  name: string;
  // Full typing required
}

// React: functional components only
// Use React Query for data fetching
```

# 🧪 Testing & Reliability

## Testing Requirements
- **Unit tests**: Minimum 90% coverage
- **Integration tests**: All API endpoints
- **E2E tests**: Critical user journeys
- Test file naming: `*_test.{ext}` alongside source

## Test Structure
```python
# Example Python test structure
async def test_query_processor_success():
    # Arrange
    processor = QueryProcessor()
    query = "Find authentication patterns"
    
    # Act
    result = await processor.process(query)
    
    # Assert
    assert result.confidence > 0.8
    assert len(result.references) > 0
```

# ✅ Task Management

## Task Workflow
1. Check `TASK.md` for current work
2. Update task status when starting
3. Mark complete immediately when done
4. Add discovered tasks under "Discovered During Work"

## Git Workflow
```bash
# Branch naming
feature/analysis-api-implementation
fix/memory-leak-pattern-detector
docs/api-authentication-guide

# Commit format (conventional commits)
feat: add repository analysis endpoint
fix: resolve WebSocket connection timeout
docs: update SDK installation guide
test: add pattern validation tests
refactor: optimize AST traversal

# Always squash merge to main
```

# 📎 Development Standards

## API Design
- REST for external APIs
- gRPC for internal service communication
- GraphQL for flexible frontend queries
- OpenAPI 3.0 documentation required

## Error Handling
```python
# Always use custom exceptions
class PatternNotFoundError(CCLError):
    def __init__(self, pattern_id: str):
        super().__init__(
            message=f"Pattern {pattern_id} not found",
            error_code="PATTERN_NOT_FOUND",
            status_code=404
        )
```

## Performance Requirements
- Query response: <100ms (p95)
- Analysis: <5 minutes for 1M LOC
- Pattern detection: <30s for standard repo
- API rate limits:
  - Free: 1,000/hour
  - Pro: 10,000/hour
  - Team: 100,000/hour
  - Enterprise: Unlimited

# 🔐 Security Requirements

## Zero Trust Principles
- Authenticate every request
- Authorize at service boundaries
- Encrypt all data (transit & rest)
- Audit log everything

## Compliance Standards
- SOC2 Type II compliant
- HIPAA ready
- GDPR compliant
- CCPA compliant

## Secret Management
```bash
# NEVER hardcode secrets
# Use Secret Manager exclusively
gcloud secrets versions access latest --secret="api-key"

# Local development: .env files (git-ignored)
# Production: Secret Manager only
```

# 📚 Common Commands

## Development
```bash
# Local development
make dev-up              # Start all services locally
make dev-logs           # View service logs
make dev-down           # Stop services

# Testing
make test               # Run all tests
make test-service SERVICE=analysis-engine
make test-integration   # Integration tests only

# Building
make build-all          # Build all services
make build SERVICE=marketplace

# Linting
make lint               # Run all linters
make fmt               # Format code
```

## Deployment
```bash
# Deploy to environment
make deploy ENV=development
make deploy ENV=staging
make deploy ENV=production SERVICE=query-intelligence

# Infrastructure
cd infrastructure/
terraform plan -out=tfplan
terraform apply tfplan
```

# 🧠 CCL-Specific Implementation Gotchas

## CRITICAL CCL Gotchas
```yaml
analysis_engine_gotchas:
  - Rust AST parsing can consume massive memory - use streaming for >1M LOC
  - Language detection must happen before parser selection
  - WebAssembly compilation required for pattern plugins
  - Use rayon for CPU-bound parallelism, tokio for I/O

query_intelligence_gotchas:
  - Gemini 2.0 has rate limits: 60 requests/minute in development
  - Vector embeddings require 1536 dimensions for text-embedding-004
  - Context window limit is 2M tokens - chunk large codebases
  - Confidence scores below 0.7 should trigger clarification

pattern_mining_gotchas:
  - Scikit-learn models need consistent feature dimensions
  - Pattern clustering requires minimum 5 examples per cluster
  - DBSCAN epsilon tuning critical for pattern quality
  - Cache feature vectors to avoid recomputation

marketplace_gotchas:
  - Spanner transactions limited to 20,000 mutations
  - Stripe webhook validation required for security
  - Pattern artifacts must be <10MB for upload
  - WASM validation prevents malicious patterns

collaboration_gotchas:
  - WebSocket connections don't auto-scale like HTTP
  - Firestore realtime listeners have 1MB document limit
  - Session state requires careful conflict resolution
  - Presence tracking needs heartbeat mechanisms

infrastructure_gotchas:
  - Cloud Run cold starts affect first requests (use min-instances: 1)
  - VPC Service Controls can block legitimate cross-service calls
  - BigQuery streaming has eventual consistency (up to 90 minutes)
  - Vertex AI quotas vary by region (us-central1 has highest limits)
```

## Validation Commands for CCL
```bash
# Always run these before committing
make lint-ccl                    # CCL-specific linting
make test-ccl                    # Full CCL test suite
make security-scan-ccl           # Security vulnerability scan
make performance-test-ccl        # Performance benchmarks

# Service-specific validation
make validate-analysis-engine    # Rust compilation and tests
make validate-query-intelligence # Python type checking and AI tests
make validate-pattern-mining     # ML model validation
make validate-marketplace        # Go tests and API validation
make validate-collaboration      # WebSocket and real-time tests

# Integration validation
make test-service-integration    # Cross-service communication
make test-e2e-ccl               # End-to-end user journeys
make load-test-ccl              # Performance under load
```

# 🚨 Critical Anti-Patterns

## NEVER DO:
1. Direct database access across services
2. Synchronous calls for long operations  
3. Store business logic in frontend
4. Manual scaling configurations
5. Custom authentication systems
6. Hardcode configuration values
7. Skip security reviews
8. Deploy without tests passing
9. **Mix service languages** (Rust only in analysis-engine, Python only in AI services, etc.)
10. **Ignore rate limits** for Vertex AI or external APIs

## ALWAYS DO:
1. Use service APIs for cross-service data
2. Implement async patterns with Pub/Sub
3. Keep business logic server-side
4. Use autoscaling configurations
5. Use Identity Platform/Firebase Auth
6. Use environment variables/Secret Manager
7. Get security approval for changes
8. Ensure all tests pass before deploy
9. **Follow CCL service boundaries** strictly
10. **Implement circuit breakers** for external dependencies

# 🛠️ Debugging & Monitoring

## Logging Standards
```python
import structlog
logger = structlog.get_logger()

# Always include context
logger.info(
    "query_processed",
    query_id=query_id,
    duration_ms=duration,
    confidence=result.confidence
)
```

## Distributed Tracing
- Use OpenTelemetry
- Propagate trace context
- Add custom spans for operations
- Include business metrics

## Error Investigation
1. Check Cloud Logging for errors
2. Review distributed traces
3. Check service health endpoints
4. Verify configuration values

# 📋 PR Requirements

## Before Submitting PR:
1. All tests pass
2. Coverage maintained >90%
3. No linting errors
4. Documentation updated
5. CHANGELOG entry added
6. Security scan passed

## PR Description Template:
```markdown
## What
Brief description of changes

## Why
Context and motivation

## How
Technical approach

## Testing
How it was tested

## Screenshots
(if UI changes)
```

# 🔗 Key Resources

## Internal
- Architecture: `docs/architecture/`
- API Specs: `api/openapi/` and `api/proto/`
- Runbooks: `docs/runbooks/`
- Security: `docs/security/`

## External
- [GCP Best Practices](https://cloud.google.com/docs/enterprise/best-practices-for-enterprise-organizations)
- [Vertex AI Docs](https://cloud.google.com/vertex-ai/docs)
- [Domain-Driven Design](https://martinfowler.com/bliki/DomainDrivenDesign.html)

# 🎯 Confidence Score: 9/10

## High Confidence Areas (9-10/10):
- **CCL Architecture**: Well-defined microservices with clear boundaries
- **GCP Integration**: Established patterns for all required services
- **Context Engineering**: Comprehensive documentation and examples
- **Validation Systems**: Executable commands for quality assurance
- **Service Patterns**: Clear implementation guides for each technology

## Medium Confidence Areas (7-8/10):
- **AI/ML Integration**: Vertex AI patterns established but may need tuning
- **Real-time Features**: WebSocket scaling may require optimization
- **Performance Optimization**: Benchmarks defined but need real-world validation

## Areas Requiring Validation (6-7/10):
- **Large-scale Deployment**: Multi-region patterns need testing
- **Advanced AI Features**: Complex query understanding needs iteration
- **Marketplace Economics**: Revenue models need market validation

## Risk Mitigation:
1. **Progressive Implementation**: Start with MVP, validate, then enhance
2. **Comprehensive Testing**: All validation loops must pass
3. **Monitoring First**: Implement observability before features
4. **Security Reviews**: Required for all production changes

---

## 📋 Development Workflow Checklist

### Before Starting Any Task:
- [ ] Read this CLAUDE.md file completely
- [ ] Check TASK.md for current work and dependencies  
- [ ] Review relevant PRPs for implementation patterns
- [ ] Run `/initialize-context-system` command
- [ ] Understand service boundaries and technology constraints

### During Development:
- [ ] Follow CCL service language restrictions
- [ ] Implement validation commands alongside features
- [ ] Add comprehensive tests (unit, integration, E2E)
- [ ] Document all gotchas and troubleshooting steps
- [ ] Update TASK.md with progress and discoveries

### Before Committing:
- [ ] Run all validation commands successfully
- [ ] Ensure >90% test coverage
- [ ] Verify security scan passes
- [ ] Update documentation as needed
- [ ] Get code review approval

### After Deployment:
- [ ] Monitor service health dashboards
- [ ] Verify performance benchmarks
- [ ] Update team on any operational learnings
- [ ] Archive completed tasks in TASK.md

---

Remember: CCL is an enterprise platform. Every change impacts real users. Maintain quality, security, and performance standards at all times. Use this comprehensive context to build production-ready code from the first implementation.